const { astReplace } = require('./base/code-split')
const traverse = require('@babel/traverse').default

function pageMixAdd({ content }) {
  if (content.indexOf('pageMixin(') > -1) {
    return content
  }

  return astReplace(content, ast => {
    const result = []
    traverse(ast, {
      ExportDefaultDeclaration(path) {
        const node = path.node

        if (node.declaration && node.declaration.type === 'ObjectExpression') {
          result.push(
            ...[
              {
                start: node.declaration.start,
                type: 'insert',
                content: 'pageMixin('
              },
              {
                start: node.declaration.end,
                type: 'insert',
                content: ')'
              }
            ]
          )
        }
      }
    })

    return result
  })
}

module.exports = pageMixAdd
