<import
  name="contents-list"
  src="../../components/contents/contents-list.ux"
></import>
<import name="header" src="../../components/header"></import>

<template>
  <div class="page-wrapper">
    <header title="目录" @back-click="backIconHandle"></header>
    <div class="title-bar">
      <text class="text-black">共{{ total }}章</text>
    </div>
    <contents-list
      list="{{catalog}}"
      chapter-title="{{chapterTitle}}"
      @detail="goToDetail"
    ></contents-list>
  </div>
</template>

<script>
import { contentsData } from '../../assets/data/contents.js'
import { getChapterImg } from '../../assets/data/book-content.js'

export default {
  public: {
    bookId: '',
    bookTitle: '',
    chapterArr: []
  },
  private: {
    total: '',
    catalog: [],
    chapterTitle: []
  },
  onInit() {
    // 开发时应该调用接口，根据bookId，pageNo(1), reverse(false)获取章节目录，返回第一页内容contents，总章节数total，总页数pageCount，算出pageNumStart和pageNumEnd。
    this.total = contentsData[this.bookId].length
    this.chapterTitle = contentsData[this.bookId]
    this.catalog = getChapterImg(this.bookId, this.total)
    this.chapterArr = JSON.parse(this.chapterArr)
  },
  goToDetail(evt) {
    /* $utils.routetheUrl('subPages/read', {
      bookId: this.bookId,
      bookTitle: this.bookTitle,
      total: contentsData[this.bookId].length,
      chapterArr: this.chapterArr,
      chapterId: Number(evt.detail.id) + 1
    }) */
    this.$app.$def.selectChapter = Number(evt.detail.id) + 1
    $utils.goBack()
  },

  backIconHandle() {
    $utils.goBack();
  },
}
</script>

<style lang="less">
@import '../../assets/styles/index.less';
.g22kjdgy {
  color: #ffffff;
}
</style>
