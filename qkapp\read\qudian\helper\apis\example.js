import $ajax from '../ajax'

/**
 * @desc 在实际开发中，您可以将 baseUrl 替换为您的请求地址前缀；
 *
 * 已将 $apis 挂载在 global，您可以通过如下方式，进行调用：
 * $apis.example.getApi().then().catch().finally()
 *
 * 备注：如果您不需要发起请求，删除 apis 目录，以及 app.ux 中引用即可；
 */
//测试域名s
const baseUrl = process.env.NODE_ENV == 'development' ? 'http://devapi.qdreads.com/api/' : 'https://api.qdreads.com/api/'

var appDef = null

//发布域名
// const baseUrl = 'https://apis.readnos.com/api/' 
/**
  * 获取验证码
  */
const codeURL = "index/code"

/**
 *   注册  
 */
const registerUrl = "user/register"
/**
 * 游客登录
 */
const quickUrl = "quick/open-v3"

/**
 * 书城信息
 */
const bookUrl = "home/data"

/**
 * 书城换一换
 */
const changeurl = "home/change"
/**
 * 
 * 我的
 */
const userurl = "user/mine"

/**
 * 分类名称
 */
const homecategoryUrl = "home/category"
/**
 * 排行
 */
const rankbookUrl = "home/rank"
/**
 * 分类
 */
const stackRoomUrl = "home/category-more"

/**
 * 书籍详情
 */
const detailsUrl = "book/detail"

/**
 * 书架
 */
const bookshelfUrl = "user/bookshelf"

/**
 *   移除书架
 */
const removeBookshelfUrl = "user/remove-bookshelf"

/**
 *   目录
 */
const catalogUrl = "book/catalog"

/**
 * 阅读章节内容 new v1.2.14 add
 */
const chapter3Url = "book/chapter-content3"
/**
* 章节扣费 v1.2.14 add
*/
const chapterDeductionCoinUrl = "book/chapter-deduction"
/**
* 阅读章节记录 v1.2.14 add
*/
const setReadRecordUrl = "book/set-read-log"
/**
 *   推荐搜索
 */
const searchreUrl = "book/search-recommend-book"

/**
 * 搜索
 */
const searchUrl = "index/search"

/**
 * 充值记录
 */
const payLogUrl = "user/pay-log"

/**
 * 单章消费记录
 */
const consumeUrl = "user/consume-chapter-log"

/**
 * 修改个人信息
 */
const uploadUrl = "user/upload"
/**
 * 退出登录
 */
const logoutUrl = "user/logout"

/**
 * 添加到桌面
 */
const deskTopUrl = "quick/desk"

/**
 * 消息通知点击上报
 */
const pushMessage_url = "push/report"

/**
 * 热门搜索排行榜 
 */
const searchRankBook_url = "book/search-rank-book"

/**
 * 书架推荐
 */
const shelfBanner_url = "bookshelf/banner"

/**
 * 二级分类
 */
const category_url = "category/second"

/**
 * 充值金额
 */
const paymentMoney_url = "payment/money"

/**
 * 任务banner
 */
const activityList_url = "activity/list";

/**
 * 活动中心任务初始页
 */
const taskIndex_url = "weal/index";

/**
 * 我的金币
 */
const userBookTickets_url = "user/book-tickets";

/**
 * 首页底部栏目   /home/<USER>
 */
const homeBottom_url = "home/bottom"

/**
* 书籍最近阅读章节
*/
const userLastBookChapter_url = "user/last-book-chapter"

/**
* 领取金币
*/
const welfareReceive_url = "weal/get-bt"
const welfareReceive_url_new = "weal/get-bt2"
/**
 * 签到
 */
const welfareSignIn_url = "weal/sign-in"

/**
* 投诉提交
*/
const submitComplaint_url = "complaint/complaint"

/**
 * 获取福利页数据 根据类型获取不同的数据
 */
const centerType_url = "weal/center-type"

/**
 * 注销账号
 */
const writeOff_url = "user/del-user"

/**
* 新增订阅
*/
const subscribe_add_url = "subscribe/add"
/**
* 订阅查询
*/
const subscribe_check_url = "subscribe/is-subscribe"

/**
* 订阅列表
*/

const subscribe_list_url = "subscribe/list"
/**
* 取消订阅
*/
const subscribe_cancel_url = "subscribe/cancel"

/**
 * 会员价格列表
 */
const vip_price_url = "user/vip-price"

/**
 * 福利任务状态更新
 */
const task_updata_url = "weal/edit-user-task"

/**
 * 小说推荐 （详情页以及阅读页）
 */
const book_recommend_url = "recommend/get-page-recommend"

/**
 * 拆红包配置
 */
 const welfare_red_bag = "weal/get-cash-envelope-config"

 /**
 * 拆红包
 */
const welfare_red_bag_open = "weal/reveive-cash-envelope"

/**
 * 金币红包信息
 */
 const user_balance_info = "user/get-user-balance"
   

 /**
 * 兴趣接口
 */
const user_interest_settings = "user/get-interest"

 /**
 * 兴趣接口
 */
const set_interest_settings = "user/set-interest"

/**
 * 是否设置了兴趣
 */
 const user_has_interest = "user/have-interest"

 /**
 * 是否设置了兴趣
 */
  const other_task = "weal/other-task"
 
/**
 * 绑定信息
 */
const cash_info = "user/get-user-cash-info"
/**
 * 绑定
 */
 const cash_bind = "user/bind-user-cash-info"

/**
 * 解绑
 */
 const cash_unbind = "user/ubind-user-cash-info"

/**
 * 浏览记录
 */
const read_history = "user/get-user-read-record"

/**
 * 加桌
 */
const add_bookshelf = "user/add-bookshelf"

/**
 * 浏览记录
 */
const get_redbag = "weal/reveive-cash-envelope"

/**
 * 请求多本小说详情
 */
const books_detail = "book/get-book-details"

function getAppDefPageName() {
  if (appDef) {
    return appDef.pageCodeInfo.pathCurUrl
  } else {
    return '默认页面'
  }
}

export default {
  setAppDef(obj) {
    if (obj) {
      appDef = obj
    }
  },
  getCodeApi(data) {
    return $ajax.post(`${baseUrl}${codeURL}`, data, getAppDefPageName())
  },
  register(data) {
    return $ajax.post(`${baseUrl}${registerUrl}`, data, getAppDefPageName())
  },
  login(data) {
    return $ajax.post(`${baseUrl}${quickUrl}`, data, getAppDefPageName())
  },
  bookinfo(data) {
    return $ajax.get(`${baseUrl}${bookUrl}`, data, getAppDefPageName())
  },
  userDataInfo(data) {
    return $ajax.get(`${baseUrl}${userurl}`, data, getAppDefPageName())
  },
  bookchange(data) {
    return $ajax.get(`${baseUrl}${changeurl}`, data, getAppDefPageName())
  },
  rankbookInfo(data) {
    return $ajax.get(`${baseUrl}${rankbookUrl}`, data, getAppDefPageName())
  },
  stackRoomInfo(data) {
    return $ajax.get(`${baseUrl}${stackRoomUrl}`, data, getAppDefPageName())
  },
  bookDetails(data) {
    return $ajax.get(`${baseUrl}${detailsUrl}`, data, getAppDefPageName())
  },
  bookshelf(data) {
    return $ajax.get(`${baseUrl}${bookshelfUrl}`, data, getAppDefPageName())
  },
  removeBookshelf(data) {
    return $ajax.post(`${baseUrl}${removeBookshelfUrl}`, data, getAppDefPageName())
  },
  getCatalog(data) {
    return $ajax.get(`${baseUrl}${catalogUrl}`, data, getAppDefPageName())
  },
  getChapterContentV3(data) {
    return $ajax.get(`${baseUrl}${chapter3Url}`, data, getAppDefPageName())
  },
  chapterDeductionCoin(data) {
    return $ajax.get(`${baseUrl}${chapterDeductionCoinUrl}`, data, getAppDefPageName())
  },
  setReadRecord(data) {
    return $ajax.get(`${baseUrl}${setReadRecordUrl}`, data, getAppDefPageName())
  },
  searchreData(data) {
    return $ajax.get(`${baseUrl}${searchreUrl}`, data, getAppDefPageName())
  },
  searchData(data) {
    return $ajax.get(`${baseUrl}${searchUrl}`, data, getAppDefPageName())
  },
  payLogInfo(data) {
    return $ajax.get(`${baseUrl}${payLogUrl}`, data, getAppDefPageName())
  },
  consumeInfo(data) {
    return $ajax.get(`${baseUrl}${consumeUrl}`, data, getAppDefPageName())
  },
  uploadInfo(data) {
    return $ajax.post(`${baseUrl}${uploadUrl}`, data, getAppDefPageName())
  },
  logout(data) {
    return $ajax.post(`${baseUrl}${logoutUrl}`, data, getAppDefPageName())
  },
  homecategory(data) {
    return $ajax.get(`${baseUrl}${homecategoryUrl}`, data, getAppDefPageName())
  },
  addDeskTopInfo(data) {
    return $ajax.get(`${baseUrl}${deskTopUrl}`, data, getAppDefPageName())
  },
  pushMessage(data) {
    return $ajax.post(`${baseUrl}${pushMessage_url}`, data, getAppDefPageName())
  },
  searchRankBookApi(data) {
    return $ajax.get(`${baseUrl}${searchRankBook_url}`, data, getAppDefPageName())
  },
  shelfBannerApi(data) {
    return $ajax.get(`${baseUrl}${shelfBanner_url}`, data, getAppDefPageName())
  },
  categoryApi(data) {
    return $ajax.get(`${baseUrl}${category_url}`, data, getAppDefPageName())
  },
  paymentMoneyApi(data) {
    return $ajax.get(`${baseUrl}${paymentMoney_url}`, data, getAppDefPageName())
  },
  getActivityList(data) {
    return $ajax.get(`${baseUrl}${activityList_url}`, data, getAppDefPageName());
  },
  getTask(data) {
    return $ajax.get(`${baseUrl}${taskIndex_url}`, data, getAppDefPageName());
  },
  userBookTickets(data) {
    return $ajax.get(`${baseUrl}${userBookTickets_url}`, data, getAppDefPageName());
  },
  homeBottomApi(data) {
    return $ajax.get(`${baseUrl}${homeBottom_url}`, data, getAppDefPageName());
  },
  userLastBookChapterApi(data) {
    return $ajax.get(`${baseUrl}${userLastBookChapter_url}`, data, getAppDefPageName());
  },
  welfareReceiveApi(data) {
    return $ajax.post(`${baseUrl}${welfareReceive_url}`, data, getAppDefPageName());
  },
  welfareReceiveNewApi(data) {
    return $ajax.post(`${baseUrl}${welfareReceive_url_new}`, data, getAppDefPageName());
  },
  welfareSignInApi(data) {
    return $ajax.get(`${baseUrl}${welfareSignIn_url}`, data, getAppDefPageName());
  },
  complaintApi(data) {
    return $ajax.post(`${baseUrl}${submitComplaint_url}`, data, getAppDefPageName());
  },
  centerTypeApi(data) {
    return $ajax.post(`${baseUrl}${centerType_url}`, data, getAppDefPageName());
  },
  writeOffApi(data) {
    return $ajax.get(`${baseUrl}${writeOff_url}`, data, getAppDefPageName());
  },
  subscribeAddApi(data) {
    return $ajax.get(`${baseUrl}${subscribe_add_url}`, data, getAppDefPageName());
  },
  subscribeCheckApi(data) {
    return $ajax.get(`${baseUrl}${subscribe_check_url}`, data, getAppDefPageName());
  },
  subscribeListApi(data) {
    return $ajax.get(`${baseUrl}${subscribe_list_url}`, data, getAppDefPageName());
  },
  subscribeCancelApi(data) {
    return $ajax.get(`${baseUrl}${subscribe_cancel_url}`, data, getAppDefPageName());
  },
  vipPriceApi(data) {
    return $ajax.get(`${baseUrl}${vip_price_url}`, data, getAppDefPageName());
  },
  taskUpdataApi(data) {
    return $ajax.get(`${baseUrl}${task_updata_url}`, data, getAppDefPageName());
  },
  bookRecommendApi(data) {
    return $ajax.get(`${baseUrl}${book_recommend_url}`, data, getAppDefPageName());
  },
  welfareRedBagApi(data) {
    return $ajax.get(`${baseUrl}${welfare_red_bag}`, data, getAppDefPageName());
  },
  welfareRedBagOpenApi(data) {
    return $ajax.get(`${baseUrl}${welfare_red_bag_open}`, data, getAppDefPageName());
  },
  getUserBalanceInfoApi(data) {
    return $ajax.get(`${baseUrl}${user_balance_info}`, data, getAppDefPageName());
  },
  getInterestSettings(data) {
    return $ajax.get(`${baseUrl}${user_interest_settings}`, data, getAppDefPageName());
  },
  setInterestSettings(data) {
    return $ajax.post(`${baseUrl}${set_interest_settings}`, data, getAppDefPageName());
  },
  getHasInterest(data) {
    return $ajax.get(`${baseUrl}${user_has_interest}`, data, getAppDefPageName());
  },
  getOtherTaskApi(data) {
    return $ajax.get(`${baseUrl}${other_task}`, data, getAppDefPageName());
  },
  getCashInfoApi(data) {
    return $ajax.get(`${baseUrl}${cash_info}`, data, getAppDefPageName());
  },
  cashInfoBindApi(data) {
    return $ajax.get(`${baseUrl}${cash_bind}`, data, getAppDefPageName());
  },
  cashInfoUnbindApi(data) {
    return $ajax.get(`${baseUrl}${cash_unbind}`, data, getAppDefPageName());
  },
  readHistoryApi(data) {
    return $ajax.get(`${baseUrl}${read_history}`, data, getAppDefPageName());
  },
  addBookshelfApi(data) {
    return $ajax.post(`${baseUrl}${add_bookshelf}`, data, getAppDefPageName());
  },
  getRedBagApi(data) {
    return $ajax.post(`${baseUrl}${get_redbag}`, data, getAppDefPageName());
  },
  getBooksDetail(data) {
    return $ajax.get(`${baseUrl}${books_detail}`, data, getAppDefPageName());
  },
}
