/**
* page : 个人中心样式表
* author: yang<PERSON>
* date: 2022-02-25
*/
.user-wrapper {
    width: 750px;
    height: 100%;
    background-color: #F4F5F7;
    background-image: url(https://img.qdreads.com/v163/user/user-top-bg.png);
    background-repeat: no-repeat;
    background-size: 750px 683px;
    flex-direction: column;
    padding: 0 30px;
    .user-header-section {
        flex-direction: row;
        padding-top: 16px;
        align-items: center;
        .user-head {
            width: 117px;
            height: 117px;
            border-radius: 50%;
            align-items: center;
            justify-content: center;
            
            image{
                width: 117px;
                height: 117px;
                border-radius: 50%;
            }
        }
        .user-info {
            flex-direction: column;
            margin-left: 16px;
            .user-info-phone {
                font-size: 32px;
                color: #333;
                font-weight: 700;
            }
            .user-info-id {
                font-size: 24px;
                font-weight: 400;
                color: #999999;
                margin-top: 10px;
            }
            .user-info-login-btn {
                color: #c0a56f;
                font-size: 32px;
                font-weight: 700;
                height: 44px;
                line-height: 44px;
            }
        }
        .user-info-empty {
            flex: 1;
        }
    }
    .user-coupon-section {
        height: 160px;
        border-radius: 24px;
        padding: 0 30px;
        justify-content: space-between;
        align-items: center;
        background-color: #fff;
        margin-top: 30px;
        .fdc{
            flex-direction: column;
            padding: 0px 30px;
        }
        .user-coupon-dec{
            font-size: 24px;
            color: #999;
            font-weight: 500;
            margin-top: 16px;
        }
        .user-make-coupon {
            height: 56px;
            justify-content: center;
            align-items: center;
            .user-make-coupon-cion {
                width: 32px;
                height: 32px;
                margin-right: 4px;
            }
            .user-make-coupon-text {
                font-size: 24px;
                font-weight: bold;
                color: #F11212;
            }
        }
        .right-icon{
            width: 24px;
            height: 24px;
        }
        .user-coupon-number {
            font-size: 40px;
            font-weight: bold;
            color: #333;
            text-align: center;
            height: 40px;
        }
        .user-coupon-empty {
            flex: 1;
        }
        .user-make-coupon-line{
            position: absolute;
            width: 1px;
            height: 80px;
            top: 40px;
            right: 177px;
            background-color: #F0F0F0;
        }
    }
    .user-vip-section {
        height: 80px;
        width: 642px;
        background-color: #e8d7bb;
        border-top-left-radius: 24px;
        border-top-right-radius: 24px;
        margin: 0 auto;
        margin-top: 44px;

        .user-vip-box {
            position: relative;
            width: 100%;
            height: 140px;
            .des {
                font-size: 24px;
                color: #fff;
                line-height: 24px;
                height: 24px;
                margin-left: 155px;
                margin-top: 28px;
            }
            .user-vip-bg {
                width: 100%;
                height: 80px;
                position: absolute;
                left: 0;
                top: 0;
            }
            .user-vip-expire {
                position: absolute;
                left: 120px;
                top: 80px;
                font-size: 24px;
                color: rgba(102,79,48,0.58);
                line-height: 24px;
                height: 24px;
            }
            .user-vip-to-up {
                width: 145px;
                height: 48px;
                background: linear-gradient(90deg,#fff1dc, #ffd699 100%);
                border-radius: 200px;
                font-size: 26px;
                color: #675538;
                position: absolute;
                right: 20px;
                top: 16px;
                text-align: center;
            }
        }
             
    }
    // .item-head{
    //     justify-content: space-between;
    //     background-color: #fff;
    //     .user-title2 {
    //         width: 148px;
    //         height: 36px;
    //     }
    //     .user-money-to-up {
    //         font-size: 28px;
    //         color: #c0a56f;
    //         font-weight: 700;
    //     }
    //     .right-icon {
    //         width: 24px;
    //         height: 24px;
    //     }
    // }
    .user-money-section {
        height: 280px;
        border-radius: 24px;
        padding: 30px;
        flex-direction: column;
        background-color: #fff;
        .title{
            justify-content: space-between;
            background-color: #fff;
            .user-title2 {
                height: 36px;
                font-size: 36px;
                font-weight: bold;
                color: #333;
                line-height: 36px;
            }
            .user-money-to-up {
                font-size: 24px;
                color: #F11212;
                font-weight: bold;
            }
            .right-icon {
                width: 24px;
                height: 24px;
            }
        }
        .user-money-content{
            margin-top: 40px;
        }
        .user-money-number {
            font-family: DIN, DIN-Bold;
            font-size: 40px;
            font-weight: 700;
            color: #333;
            height: 40px;
            margin-top: 20px;
        }
        .user-money-des {
            font-size: 24px;
            color: #999;
            height: 24px;
        }
        .user-money-item {
            padding: 30px;
            width: 300px;
            height: 144px;
            flex-direction: column;
            background-color:#F2F7FF;
            border-radius: 18px;
            .icon{
                width: 104px;
                height: 104px;
                position: absolute;
                right: 13px;
                top: 20px;
            }
        }
        .user-money-item-2 {
            background-color:#FFF9F3;
            margin-left: 30px;
        }
        .user-money-empty {
            flex: 1;
        }
        .user-money-to-up {
            color: #0aafff;
            font-size: 28px;
            font-weight: 700;

        }
            
    }
    .user-menu-section {
        flex-direction: column;
        background-color: #fff;
        border-radius: 24px;
        margin: 30px 0;
        width: 690px;
        .user-menu-item {
            height: 100px;
            align-items: center;
            padding-left: 32px;
            padding-right: 24px;
            .user-menu-icon {
                width: 40px;
                height: 40px;
            }
            .user-menu-name {
                font-size: 32px;
                font-weight: 700;
                color: #333333;
                margin-left: 20px;
            }
            .user-menu-empty {
                flex: 1;
            }
            .user-menu-arrow {
                width: 26px;
                height: 26px;
            }
        }
    }


}

.swiper-wrapper{
    width: 100%;
    margin-top: 20px;
}

.user-header-title {
    height: 44px;
    align-items: center;

    > image {
        width: 90px;
        height: 44px;
    }
}