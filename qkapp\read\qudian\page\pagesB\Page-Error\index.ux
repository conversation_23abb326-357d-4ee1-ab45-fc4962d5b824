<template>
  <div class="page-error-wrapper">
    <common-header
      title="出错了"
      text-center="{{backTitleIsCenter}}"
      onback-click="pageBack"
    ></common-header>
    <div class="page-error-content">
      <image class="error-image" src="https://img.qdreads.com/image%2F2022-09-28%2Fnowife%402x.png"></image>
      <text class="error-title">哎呀，出错了，请重试～</text>
      <text class="error-btn" onclick="reloadClick">重试</text>
    </div>
  </div>
</template>
<import name="common-header" src="../../components/common-back-header/index"></import>
<script>
export default {
    data: {
        backTitleIsCenter: false,
        pathUrl: '',
        intent: 0,
        isLogin: 'true',
        pageDetail: {
            pageUrl: '错误页',
            pageName: '错误页',
            pageCode: 'READ_COMMON',
            pageOrigin: ''
        },
    },
    onInit() {
        this.$app.$def.setAppData('isNeedReloadPage', '1')
    },
    onShow() {
      
    },
    onHide() {
      
    },
    async reloadClick() {
        await this.channelUserLoginFunction()
        COMMON_REPORT_UTILS.page_click_report('重试') //点击上报
        $utils.goBack()
    },
    async pageBack() {
        await this.channelUserLoginFunction()
        COMMON_REPORT_UTILS.page_click_report('返回') //点击上报
        $utils.goBack()
    },
    async onBackPress() {
        await this.channelUserLoginFunction()
        $utils.goBack()
        return true
    },

  channelUserLoginFunction() {
    let that = this
    return new Promise(async (resolve) => {
      if (this.isLogin == 'true') {
        resolve()
        return
      }
      let loginRes = await that.$app.$def.login({ intent: that.intent ? that.intent : 1 })
      if (loginRes.code == 200) {
        resolve()
      } else {
        if (times == 0) {
          that.channelUserLoginFunction(times + 1)
        } else {
          resolve()
        }
      }
    })
  },
}
</script>

<style lang="less">
.page-error-wrapper {
  flex-direction: column;
  width: 100%;
  .page-error-content {
    width: 100%;
    flex: 1;
    align-items: center;
    flex-direction: column;
    .error-image {
      width: 350px;
      height: 370px;
      margin-top: 328px;
    }
    .error-title {
      height: 24px;
      font-size: 24px;
      font-weight: 500;
      text-align: center;
      color: #999999;
      margin-top: 40px;
    }
    .error-btn {
      width: 240px;
      height: 80px;
      background-color: #38415f;
      border-radius: 12px;
      font-size: 32px;
      font-weight: 500;
      text-align: center;
      color: #ffefce;
      margin-top: 40px;
    }
  }
}
</style>
