<template>
  <!-- template里只能有一个根节点 -->
  <div class="novel-item search-novel-item" @click="bookItemClickHandler">
    <image src="{{novelData.bookIconUrl}}" class="novel-pic"></image>
    <div class="novel-detail">
      <div class="nd-name-wrapper">
        <text class="novel-title">{{ novelData.bookName }}</text>
        <text class="ly-ily" if="novelData.score">{{ novelData.score | formatScore }}分</text>
      </div>
      <text class="novel-desc">{{ novelData.bookContenUrl.replace('\r\n','') }}</text>
      <div class="novel-bottom-detail">
        <text class="novel-author">{{ novelData.bookAuthor }}</text>
        <text
          if="{{novelData && isShowType && novelData.bookType}}"
          class="novel-type"
          >{{ novelData.bookType }}</text
        >
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data: {
    list: [],
    randomCount: 0
  },
  onInit() {
    this.randomCount = Math.ceil(Math.random() * 5)
  },
  bookItemClickHandler() {
    this.$emit('compClick', { bookId: this.novelData.bookId, bookName: this.novelData.bookName })
  },
  props: {
    novelData: {
      type: Object,
      default: null
    },
    isShowType: {
      type: Boolean,
      default: false
    }
  },
  formatScore(value) {
    if (!value) return ''
    return value / 10
  },
}
</script>

<style lang="less">
@import './index.less';
</style>
