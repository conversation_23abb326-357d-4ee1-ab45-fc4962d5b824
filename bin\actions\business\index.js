const path = require('path')
const { getAppName, questionBusinessVersion } = require('../../utils/question')
const { setAppBasePath } = require('../../config/app-base')
const { consoleSplit } = require('../../utils/console')
const modifyBusinessFile = require('../../main/tasks/modify-business')
const { projectPath } = require('../../main/constant')
const { checkVersion: checkSdkVersion } = require('../../main/tasks/check-version')

async function mainTask() {
  // 检查版本
  await checkSdkVersion()

  // 2.0.3 ==> v203
  consoleSplit('选择升级版本')
  const updateVersion = await questionBusinessVersion()

  consoleSplit('选择应用')
  const { category, code, appType = 'tool', name } = await getAppName()

  setAppBasePath(path.resolve(projectPath, './qkapp', `${category}/${code}`))

  // 修改业务文件
  modifyBusinessFile({ category, code, name }, updateVersion, appType)
}

module.exports = mainTask
