<template>
  <div class="item-container" @click="itemClick">
    <image class="item-image" src="{{item.image}}"></image>
    <div class="item-info">
      <text class="info-title">{{ item.title }}</text>
      <text class="info-des">上次阅读到：第{{ 1 }}话</text>
      <text class="info-des">作者：{{ item.author }}</text>
      <text class="info-des">{{ item.description }}</text>
    </div>
    <slot></slot>
  </div>
</template>

<script>
export default {
  props: ['item'],

  itemClick() {
    this.$emit('itemClick', { item: this.item })
  }
}
</script>

<style lang="less">
@import '../../assets/styles/index.less';

.item-container {
  width: 100%;
  height: 214px;
  margin-top: 30px;
}

.item-image {
  width: 160px;
  height: 214px;
  border-radius: 14px;
}

.item-info {
  flex-direction: column;
  height: 100%;
  flex: 1;
  margin-left: 20px;
  padding: 21px 0 19px;
  justify-content: space-between;
}

.info-title {
  height: 42px;
  color: #333333;
  font-size: 32px;
  font-weight: bold;
}

.info-des {
  height: 24px;
  color: #999999;
  font-size: 24px;
  .text-lines(1);
}
</style>
