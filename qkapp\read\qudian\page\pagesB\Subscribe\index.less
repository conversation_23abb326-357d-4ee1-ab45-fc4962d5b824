/**
* page :订阅记录页面样式
* author: ya<PERSON><PERSON>
* date: 2022-03-07
*/
.subscribe-record-wrapper {
    flex-direction: column;
    background-color: #fff;
    width: 750px;
    height: 100%;
    .scubscribe-list {
        flex: 1;
        width: 750px;
        padding: 40px 30px;
        flex-direction: column;
        .scubscribe-list-item {
            flex-direction: row;
            width: 100%;
            margin-bottom: 40px;
            .scubscribe-list-item-image {
                width: 128px;
                height: 181px;
                border-radius: 10px;
            }
            .scubscribe-list-item-content {
                flex: 1;
                flex-direction: column;
                margin: 6px 30px 6px 39px;
                height: 169px;
                .content-book-name {
                    font-size: 34px;
                    color: #333;
                    height: 34px;
                    lines: 1;
                    text-overflow: ellipsis;
                }
                .content-book-des {
                    height: 73px;
                    line-height: 36px;
                    font-size: 24px;
                    color: #ADADAD;
                    lines: 2;
                    text-overflow: ellipsis;
                    margin-top: 20px;
                }
                .content-subscribe-time {
                    font-size: 22px;
                    color: #ADADAD;
                    height: 22px;
                    margin-top: 20px;
                }
            }
            .scubscribe-list-item-btn {
                width: 156px;
                height: 62px;
                border-radius: 100px;
                border: 1px solid #d8d8d8;
                font-size: 24px;
                color:  #999999;
                text-align: center;
                margin-top: 54px;
            }
        }
        // 空页面
        .empty-section {
            flex-direction: column;
            .empty-image {
                width: 350px;
                height: 370px;
                margin: 0 auto;
            }
            .empty-des {
                font-size: 24px;
                font-weight: 500;
                color: #999999;
                text-align: center;
            }
            .empty-btn {
                width: 240px;
                height: 80px;
                background-color: #38415f;
                border-radius: 12px;
                color: #ffefce;
                text-align: center;
                margin-top: 40px;
                font-size: 32px;
                margin: 0 auto;
                margin-top: 40px;
            }
        }
    }
}