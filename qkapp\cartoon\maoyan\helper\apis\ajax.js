/**
 * 封装了一些网络请求方法，方便通过 Promise 的形式请求接口
 */
import $fetch from '@system.fetch'
import $utils from '../../common/utils'

Promise.prototype.finally = function (callback) {
    const P = this.constructor
    return this.then(
        value => P.resolve(callback()).then(() => value),
        reason =>
            P.resolve(callback()).then(() => {
                throw reason
            })
    )
}

/**
 * 调用快应用 fetch 接口做网络请求
 * @param params
 */
var android = ""
var userid = ""
function fetchPromise(params) {
    let page_url = params.url.split('?')
    console.log('请求信息', JSON.stringify(params));
    return new Promise((resolve, reject) => {
        $utils.getStorage('token').then(res => {
            $utils.getStorage('android').then(data => {
                if (data) {
                    android = data
                }
            })
            $utils.getStorage('userid').then(user => {
                if (user) {
                    userid = user
                }
            })
            $fetch
                .fetch({
                    url: params.url,
                    method: params.method,
                    data: params.data,
                    header: { Authorization: res, android: android, userid: userid }
                }).then(response => {
                    console.log("请求结果=>>>>>>>>>>>>>>", JSON.parse(response.data.data))
                    let data = JSON.parse(response.data.data)
                    if (data.code == 200) {
                        if (data.result.jwt_token) {
                            $utils.setStorage("token", data.result.jwt_token)
                        }
                        resolve(data.result)
                    }
                    if (data.code == 401) {
                        $utils.showToast("登录异常,请重新登录!")
                        $utils.routetheUrl('pages/registered');
                        $fetch
                            .fetch({
                                url: process.env.NODE_ENV == 'development' ? 'http://devapi.inheweb.com/api/login/login' : 'https://api.inheweb.com/api/login/login',
                                data: { openid: android },
                                method: 'post',
                            }).then(reset => {
                                // console.log("登录请求结果=>>>>>>>>>>>>>>", JSON.parse(reset.data.data))
                                let data = JSON.parse(reset.data.data)
                                if (data.code == 200) {
                                    $utils.setStorage("token", data.result.data.jwt_token)
                                }
                            })
                    }
                    if (data.code == 404) {
                        try {
                            let _err = JSON.stringify(response.data.data)
                            COMMON_REPORT_UTILS.error_log_report(`${page_url[0]}`, `${_err.substr(0, 1024)}`, '接口请求', `找不到接口`)
                        } catch (e) { }
                    }
                    if (data.code == 500) {
                        try {
                            let _err = JSON.stringify(response.data.data)
                            COMMON_REPORT_UTILS.error_log_report(`${page_url[0]}`, `${_err.substr(0, 1024)}`, '接口请求', `内部错误`)
                        } catch (e) { }
                    }
                }).catch((error, code) => {
                    try {
                        if (error.code != 2001) {
                            let _err = JSON.stringify(error)
                            COMMON_REPORT_UTILS.error_log_report(`${page_url[0]}`, `${_err.substr(0, 1024)}`, '接口请求', `请求超时`)
                        }
                    } catch (e) { }
                    console.log(`请求失败, code = ${JSON.stringify(error)}`)
                    reject(error)
                })
        })
    })
}


export default {
    post: function (url, params) {
        return fetchPromise({
            method: 'post',
            url: url,
            data: params
        })
    },
    get: function (url, params) {
        return fetchPromise({
            method: 'get',
            url: $utils.queryString(url, params)
        })
    },
    put: function (url, params) {
        return fetchPromise({
            method: 'put',
            url: url,
            data: params
        })
    },
    delete: function (url, params) {
        return fetchPromise({
            method: 'delete',
            url: url,
            data: params
        })
    }
    // 如果，method 您需要更多类型，可自行添加更多方法；
}
