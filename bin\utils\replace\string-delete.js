const { findCodeMatch } = require('./base/match')

function stringDelete({ content, oldCode, newCode, failCodeInfo, file, desc }) {
  let matchedOldCode = findCodeMatch(oldCode, content)

  return content.replace(matchedOldCode, '')
}

function stringDeleteOneLine({ content, oldCode, newCode, failCodeInfo, file, desc }) {
  // console.log('stringDeleteOneLine', content, oldCode, newCode, failCodeInfo, file, desc)
  let matchedOldCode = findCodeMatch(oldCode, content)

  const newLineInfo = findNearestNewlines(content, matchedOldCode)

  content = String(content.slice(0, newLineInfo.before)) + content.slice(newLineInfo.after)

  return content
}

function findNearestNewlines(str, substring) {
  // 获取子串的起始位置
  if(!substring){
    return { before: -1, after: -1 }
  }

  const startIndex = str.indexOf(substring)

  if (startIndex === -1) {
    return { before: -1, after: -1 } // 如果子串不存在，返回 -1
  }

  // 子串的结束索引
  const endIndex = startIndex + substring.length

  // 查找子串前的最近的换行符 '\n'
  const beforeNewlineIndex = str.lastIndexOf('\n', startIndex)

  // 查找子串后的最近的换行符 '\n'
  const afterNewlineIndex = str.indexOf('\n', endIndex)

  return {
    before: beforeNewlineIndex === -1 ? 0 : beforeNewlineIndex, // 如果没找到，返回0
    after: afterNewlineIndex === -1 ? str.length : afterNewlineIndex // 如果没找到，返回字符串长度
  }
}

module.exports = {
  stringDelete,
  stringDeleteOneLine
}
