<import name="my-toast" src="apex-ui/components/toast/index"></import>
<import
  name="contents-list"
  src="../../components/contents/contents-list.ux"
></import>
<import name="header" src="../../components/header"></import>

<template>
  <div class="page-wrapper detail-wrapper">
    <div class="top-wrapper" style="{{topStyle}}">
      <div class="custom-title-bar">
        <div style="align-items:center;">
          <div class="img-wrapper" @click="goBack">
            <image src="../../assets/v100/back-icon.png"></image>
          </div>
          <text>详情页</text>
        </div>
      </div>
    </div>
    <div class="info-wrapper">
      <div class="tag-wrapper">
        <div class="tag-content">
          <text class="book-title">{{ bookInfo.title }}</text>
          <div class="book-tag">
            <text class="tag-1">榜单TOP{{ bookInfo.rank }}</text>
            <text class="tag-2">{{ bookInfo.hot }}讨论数</text>
          </div>
        </div>
        <div class="collect-wrapper {{isInCollect?'opacity-6':''}}" @click="addToCollect">
          <image src="../../assets/v100/collect-icon.png" ></image>
          <text>{{isInCollect?'已收藏':'收藏'}}</text>
        </div>
      </div>

      <text class="info-title">预告</text>
      <text class="introduction">{{ bookInfo.description }}</text>
      <text class="author">作者：{{ bookInfo.author }}</text>
      <div class="score-wrapper">
        <text class="score-number">
          <span class="sn-number">{{ bookInfo.score }}</span
          ><span class="sn-label">评分</span>
        </text>
        <text class="score-des">已有{{ bookInfo.review }}人打分</text>
        <image
          class="score-img"
          src="../../assets/v100/detail-avatar.png"
        ></image>
      </div>
    </div>
    <text class="info-title" style="margin-left:24px;">漫画选集</text>
    <contents-list
      list="{{catalog}}"
      chapter-title="{{chapterTitle}}"
      @detail="goToDetail"
    ></contents-list>
    <div class="detail-bottom-bar">
      <text
        class="btn btn-shelf {{isInShelf ? 'btn-shelf-disabled' : ''}}"
        @click="addToShelf"
      >
        {{ isInShelf ? '已加入书架' : '加入书架' }}
      </text>
      <text class="btn-read" @click="goToContent">开始阅读</text>
    </div>
    <my-toast id="toast"></my-toast>
  </div>
</template>

<script>
import { getChapterImg } from '../../assets/data/book-content.js'
import { contentsData } from '../../assets/data/contents.js'
import { bookListData } from '../../assets/data/book-list.js'

export default {
  public: {
    info: '',
    bookId: '',
  },
  private: {
    bookInfo: {},
    isInShelf: false,
    shelfList: [],
    topStyle: {
      backgroundImage: ''
    },
    catalog: [],
    chapterTitle: [],
    collectList: [],
    isInCollect: false,
    copyBookList: [],
  },
  async onInit() {
    // 此处直接将图书内容传进来，实际开发中也可以传入id查询图书信息
    this.copyBookList = [...bookListData]
    if (this.info) {
      this.bookInfo = JSON.parse(this.info);
    } else if (this.bookId) {
      this.bookInfo = this.copyBookList.find(item => item.id == this.bookId);
    } else {
      this.bookInfo = this.copyBookList[0];
    }

    this.chapterTitle = contentsData[this.bookInfo.id]
    this.catalog = getChapterImg(this.bookInfo.id, this.bookInfo.count)
    this.topStyle.backgroundImage = this.bookInfo.swiper
    // $shelfList:全局变量
    this.shelfList = $shelfList
    this.isInShelf = !!this.shelfList.filter(book => {
      return book.id === this.bookInfo.id
    }).length
    this.collectList = $collectList
    this.isInCollect = !!this.collectList.filter(book => {
      return book.id === this.bookInfo.id
    }).length;
  },
  goToDetail(evt) {
    $utils.routetheUrl('subPages/read', {
      bookId: this.bookInfo.id,
      bookTitle: this.bookInfo.title,
      total: this.bookInfo.count,
      chapterArr: this.bookInfo.chapterArr,
      chapterId: Number(evt.detail.id) + 1
    })
  },
  goToContent() {
    $utils.routetheUrl('subPages/read', {
      bookId: this.bookInfo.id,
      bookTitle: this.bookInfo.title,
      total: this.bookInfo.count,
      chapterArr: this.bookInfo.chapterArr
    })
  },
  addToShelf() {
    if (this.isInShelf) {
      return
    }
    if (!this.bookInfo.id) {
      this.$child('toast').showToast({
        content: '图书信息有误',
        icon: 'warning'
      })
      return
    }
    this.isInShelf = true
    this.shelfList.push(this.bookInfo)
    $utils.setShelfList(this.shelfList, true) // 保存到全局变量和storage
    this.$child('toast').showToast({
      content: '已加入书架'
    })
  },
  addToCollect() {
    if (this.isInCollect) {
      return
    }
    if (!this.bookInfo.id) {
      this.$child('toast').showToast({
        content: '图书信息有误',
        icon: 'warning'
      })
      return
    }
    this.isInCollect = true
    this.collectList.push(this.bookInfo)
    $utils.setCollectList(this.collectList, true)
  },
  goBack() {
     $utils.goBack()
  },
}
</script>

<style lang="less">
@import '../../assets/styles/index.less';
.custom-title-bar {
  background: linear-gradient(0deg,rgba(0,0,0,0.00), #000000 100%);
}

.detail-wrapper {
  height: 100%;
}

.detail-bottom-bar {
  padding: @app-padding @gap-3;
  background-color: @white;
  .border-top-mixins();
  .flex-box-mixins(row, space-between, center);
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
}
.btn-shelf {
  color: @brand;
  width: 240px;
  height: 96px;
  background-color: @second;
  border-radius: 48px;
  text-align: center;
}
.btn-read {
  color: @white;
  width: 420px;
  height: 96px;
  background-color: @brand;
  border-radius: 48px;
  text-align: center;
}
.btn-shelf-disabled {
  color: @text-grey;
  width: 240px;
  height: 96px;
  background-color: #f3f3f3;
  border-radius: 48px;
  text-align: center;
}

.top-wrapper {
  width: 100%;
  height: 440px;
  .section-container;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
}

.book-title {
  margin-top: 30px;
  height: 36px;
  color: @text-black;
  font-size: 36px;
  font-weight: bold;
}

.book-tag {
  margin-top: 20px;
  height: 30px;

  .tag-1,
  .tag-2 {
    font-size: 20px;
    text-align: center;
  }

  .tag-1 {
    color: #ffffff;
    width: 121px;
    height: 30px;
    background-color: @brand;
    border-radius: 4px;
  }

  .tag-2 {
    width: 124px;
    height: 30px;
    background-color: #f3f3f5;
    border-radius: 4px;
    margin-left: 23px;
    color: #90939a;
  }
}

.info-title {
  height: 30px;
  color: #333333;
  font-size: 30px;
  margin-top: 30px;
  font-weight: bold;
}

.info-wrapper {
  .section-container;
  padding: 0 @app-padding;
  margin-top: -27px;
  background-color: @white;
  border-top-left-radius: 24px;
  border-top-right-radius: 24px;

  .introduction {
    color: #666666;
    font-size: 28px;
    lines: 3;
    text-overflow: ellipsis;
    line-height: 40px;
    margin-top: 30px;
  }

  .author {
    height: 28px;
    color: #666666;
    font-size: 28px;
    margin-top: 20px;
  }

  .score-wrapper {
    width: 702px;
    height: 120px;
    background-color: #f3f3f3;
    border-radius: 14px;
    margin-top: 30px;
    padding: 0 30px;
    align-items: center;

    .score-number {
      .sn-number {
        color: @brand;
        font-size: 60px;
        font-weight: bold;
      }

      .sn-label {
        color: rgba(54,195,255,0.96);;
        font-size: 28px;
      }
    }

    .score-des {
      margin-left: auto;
      height: 30px;
      color: #999999;
      font-size: 30px;
    }

    .score-img {
      margin-left: 10px;
      width: 80px;
      height: 40px;
    }
  }
}

.chapter-list {
  flex: 1;
  margin-top: 30px;
  width: 100%;
  padding: 0 @app-padding;
  flex: 1;
}

.chapter-list-item {
  align-items: center;
  height: 114px;

  image {
    width: 192px;
    height: 114px;
    border-radius: 4px;
    margin-right: 20px;
    background-color: #666;
  }

  text {
    height: 30px;
    color: #333333;
    font-size: 30px;
  }
}

.tag-wrapper {
  width: 100%;
}

.tag-content {
  flex-direction: column;
  flex: 1;
}

.collect-wrapper {
  width: 148px;
  height: 54px;
  background-color: @brand;
  border-radius: 13px;
  align-items: center;
  justify-content: center;
  margin: auto 0;

  image {
    width: 30px;
    height: 30px;
    margin-right: 4px;
  }

  text {
    height: 32px;
    color: #ffffff;
    font-size: 32px;
  }
}

.opacity-6 {
  opacity: 0.6;
}
</style>
