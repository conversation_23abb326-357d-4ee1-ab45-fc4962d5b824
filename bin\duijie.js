#!/usr/bin/env node
const { questionSelection } = require('./utils/question')
const childProcess = require('child_process')
const args = process.argv.slice(2)
async function main() {
  try {
    const operation = await questionSelection()

    switch (operation) {
      case 'up':
        childProcess.execSync('npx ad up', { stdio: 'inherit' })
        break
      case 'run':
        const command = args.includes('-up') ? 'npx ad run --upgrade' : 'npx ad run'
        childProcess.execSync(command, { stdio: 'inherit' })
        break
      case 'release-log':
        childProcess.execSync('npm run release-log', { stdio: 'inherit' })
        break
      case 'release':
        childProcess.execSync('node release.js', { stdio: 'inherit' })
        break
      case 'plint':
        childProcess.execSync('npm run plint', { stdio: 'inherit' })
        break
      default:
        console.log('没有找到对应的操作')
        break
    }
  } catch (error) {
    console.error('Error:', error.message)
    process.exit(1)
  }
}

main()
