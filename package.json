{"name": "ad-interface", "version": "2.0.5", "description": "广告 SDK 对接层", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "prepare": "husky install", "new": "node ./script/create-version.js"}, "repository": {"type": "git", "url": "ssh://*******************:2222/cy/fre/quickapp/ad-interface.git"}, "bin": {"ad": "./bin/index.js", "dj": "./bin/duijie.js"}, "author": "", "license": "ISC", "devDependencies": {"@babel/eslint-parser": "^7.24.7", "@babel/runtime": "^7.24.7", "@types/quickapp": "npm:quickapp-interface@^1.0.0", "ad-sdk": "git+https://gitlab.ghfkj.cn/cy/fre/quickapp/ad-sdk.git#release-v2.0.5.7", "apex-ui": "^1.9.5", "babel-eslint": "^10.1.0", "eslint": "^8.57.0", "eslint-config-alloy": "^5.1.2", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "husky": "^9.0.11", "less": "^4.1.1", "less-loader": "^10.0.1", "lint-staged": "^15.2.7", "prettier": "^3.3.2", "prettier-plugin-ux": "^0.3.0"}, "lint-staged": {"**/**.{ux,js,json,less,scss,css,pcss,md,vue}": ["eslint"]}, "dependencies": {"@babel/core": "^7.24.7", "colors": "^1.4.0", "commander": "^12.1.0", "inquirer": "^10.0.1", "inquirer-autocomplete-prompt": "^0.12.2", "koa": "^2.15.3", "mitt": "^3.0.1", "node-watch": "^0.7.4", "open": "^10.1.0", "parse5": "^7.1.2", "pinyin-pro": "^3.24.2"}, "browserslist": ["chrome 65"], "peerDependencies": {"commander": "^12.1.0"}}