<template>
  <div class="wrap {{bgColor}}" onclick="handleClick">
    <stack>
      <div class="box"></div>
      <div class="circle-box {{activeClass}}">
        <div class="circle"></div>
      </div>
      <text class="open-text font-icon" if="{{checked&&open}}">{{
        open || '开启'
      }}</text>
      <text class="close-text font-icon" if="{{!checked&&close}}">{{
        close || '关闭'
      }}</text>
    </stack>
  </div>
</template>

<style lang="less">
@import '../../assets/styles/index.less';

@SwitchWidth: 100px;
@SwitchHeight: 48px;
@SwitchPadding: 4px;
@SwitchCircleSize: 40px;
@SwitchCircleTranslateX: @SwitchWidth - @SwitchCircleSize - @SwitchPadding * 2;
@SwitchCircleColor: #ffffff;
@SwitchOnColor: @brand;
@SwitchOffColor: #d8d8d8;
@SwitchDisabledOnColor: #cff1f5;
@SwitchDisabledOffColor: #f3f3f3;

.wrap {
  width: @SwitchWidth;
  height: @SwitchHeight;
  border-radius: @SwitchCircleSize;
  .open-text {
    color: #ffffff;
    font-size: 20px;
    padding-left: 10px;
  }
  .close-text {
    color: #ffffff;
    font-size: 20px;
    padding-left: 45px;
  }
}

stack {
  align-items: center;
}

.box {
  width: 100%;
  height: 100%;
  border-radius: @SwitchHeight;
}

.switch-on {
  background-color: @SwitchOnColor;
}

.switch-off {
  background-color: @SwitchOffColor;
}

.switch-disabled-on {
  .box {
    background-color: @SwitchDisabledOnColor;
  }
  .circle {
  }
}

.switch-disabled-off {
  .box {
    background-color: @SwitchDisabledOffColor;
  }
  .circle {
    background-color: #cccccc;
  }
}

.circle-box {
  padding-left: @SwitchPadding;
  padding-right: @SwitchPadding;
  width: 100%;
}

.circle-checked {
  animation-name: checked;
  animation-duration: 100ms;
  animation-iteration-count: 1;
  animation-fill-mode: forwards;
  transform: translateX(@SwitchCircleTranslateX);
}

.circle-dischecked {
  animation-name: dischecked;
  animation-duration: 100ms;
  animation-iteration-count: 1;
  animation-fill-mode: forwards;
  transform: translateX(0);
}

.circle {
  width: @SwitchCircleSize;
  height: @SwitchCircleSize;
  border-radius: @SwitchCircleSize;
  background-color: @SwitchCircleColor;
}

@keyframes checked {
  0% {
    transform: translateX(0px);
  }
  100% {
    transform: translateX(@SwitchCircleTranslateX);
  }
}

@keyframes dischecked {
  0% {
    transform: translateX(@SwitchCircleTranslateX);
  }
  100% {
    transform: translateX(0px);
  }
}
</style>

<script>
export default {
  data() {
    return {
      activeClass: '',
      bgColor: 'switch-off',
      checked: this.value
    }
  },
  props: {
    value: {
      default: false
    },
    open: {
      default: ''
    },
    close: {
      default: ''
    },
    disabled: {
      default: false
    },
    name: {
      default: ''
    }
  },

  onInit() {
    if (this.checked) {
      this.bgColor = 'switch-on'
    } else {
      this.bgColor = 'switch-off'
    }
    this.$watch('value', 'checkedChange')
    this.$watch('disabled', 'configChange')
    this.checkedChange()
  },
  checkedChange() {
    this.checked = this.value
    this.isChecked()
  },
  configChange() {
    this.isChecked()
  },
  handleClick() {
    let disabled = this.disabled
    if (!disabled) {
      this.checked = !this.checked
      this.isChecked()
      this.$emit('change', { checked: this.checked, name: this.name })
    }
  },
  isChecked() {
    if (this.disabled) {
      if (this.checked) {
        this.activeClass = 'circle-checked'
        this.bgColor = 'switch-disabled-on'
      } else {
        this.activeClass = 'circle-dischecked'
        this.bgColor = 'switch-disabled-off'
      }
    } else {
      if (this.checked) {
        this.activeClass = 'circle-checked'
        this.bgColor = 'switch-on'
      } else {
        this.activeClass = 'circle-dischecked'
        this.bgColor = 'switch-off'
      }
    }
  }
}
</script>
