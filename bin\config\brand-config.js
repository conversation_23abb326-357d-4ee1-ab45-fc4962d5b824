/**
 * 对接配置
 * 分为 单 APP 配置和 分厂商配置，如果两者都配置了同一个字段，则以app配置为准
 * @type {{app: {}, brand: {}}}
 */
const config = {
  // 分厂商的配置统一配置
  brand: {
    xiaomi: {
      app: {
        // 检测安装包列表
        checkPackageList: {
          dev: ['com.njh.biubiu'],
          prod: [
            'com.huawei.fastapp.dev',
            'org.hapjs.mockup',
            'org.hapjs.debugger',
            'com.fvcorp.android.aijiasuclient',
            'com.dongguo.feiyu',
            'com.huawei.welink',
            'com.huawei.works',
            'com.yiqixie.kem',
            'com.bytedance.ad.deliver',
            'com.baidu.hi',
            'com.qi.earthnutproxy',
            'com.qi.staticsproxy',
            'com.lishun.flyfish',
            'com.vivo.hybrid.sdkdemo',
            'com.vivo.hybrid.debugger',
            'com.teamtalk.im',
            'com.sie.mp',
            'com.ss.android.lark.kami',
            'com.baidu.fengchao.ui',
            'com.expressvpn.vpn',
            'com.windscribe.vpn',
            'com.fishervpn.freevpn',
            'com.zouqi.qingfeng',
            'com.xy.vpn',
            'com.daxiang.vpn',
            'ch.protonvpn.android',
            'com.fastfun.vpn',
            'com.suxxt.vpnanonymity',
            'com.zaozhu.netplus',
            'com.ifast.virtualvpn',
            'com.qlbd.quanliangpromote',
            'com.zx.a2_quickfox',
            'com.birdvpn.app',
            'com.freevpnintouch',
            'com.nordvpn.android',
            'top.a6b.shendunet',
            'cn.ccspeed',
            'com.goldenfrog.vyprvpn.app',
            'com.mi.oa',
            'com.wl.ufovpn',
            'com.surfshark.vpnclient.android',
            'com.mengdie.turtlenew',
            'hotspotshield.android.vpn',
            'com.njh.biubiu',
            'com.whitebunny.vpn',
            'com.netease.uu',
            'com.avira.vpn',
            'com.xiongmao886.tun',
            'com.dmvpn.vpnfree',
            'com.sanye.xiongyingjiasuqi',
            'com.heytap.ocsp.client',
            'cn.i4.mobile',
            'com.iclicash.adv'
          ]
        },
        checkPackageListWhite: {
          dev: ['com.tencent.mm', 'com.tencent.mobileqq'],
          prod: ['com.tencent.mm', 'com.tencent.mobileqq']
        }
      }
    },
    hw: {
      app: {
        // 检测安装包列表
        checkPackageList: {
          dev: ['com.njh.biubiu'],
          prod: [
            'com.huawei.fastapp.dev',
            'org.hapjs.mockup',
            'org.hapjs.debugger',
            'com.fvcorp.android.aijiasuclient',
            'com.dongguo.feiyu',
            'com.huawei.welink',
            'com.huawei.works',
            'com.yiqixie.kem',
            'com.bytedance.ad.deliver',
            'com.baidu.hi'
          ]
        },
        checkPackageListWhite: {
          dev: ['com.tencent.mm', 'com.tencent.mobileqq'],
          prod: ['com.tencent.mm', 'com.tencent.mobileqq']
        }
      }
    },
    oppo: {
      app: {
        // 检测安装包列表
        checkPackageList: {
          dev: ['com.njh.biubiu'],
          prod: [
            'com.huawei.fastapp.dev',
            'org.hapjs.mockup',
            'org.hapjs.debugger',
            'com.zouqi.qingfeng',
            'com.fvcorp.android.aijiasuclient',
            'com.zaozhu.netplus',
            'com.qlbd.quanliangpromote',
            'com.mengdie.turtlenew',
            'cn.ccspeed',
            'com.xiongmao886.tun',
            'com.zx.a2_quickfox',
            'com.sanye.xiongyingjiasuqi',
            'com.dongguo.feiyu',
            'com.netease.uu',
            'com.njh.biubiu',
            'com.huawei.welink',
            'com.huawei.works',
            'com.vivo.hybrid.sdkdemo',
            'com.vivo.hybrid.debugger',
            'com.sie.mp',
            'com.teamtalk.im',
            'com.ss.android.lark.kami',
            'com.yiqixie.kem',
            'com.bytedance.ad.deliver',
            'com.baidu.fengchao.ui',
            'ch.protonvpn.android',
            'com.avira.vpn',
            'com.birdvpn.app',
            'com.daxiang.vpn',
            'com.dmvpn.vpnfree',
            'com.fastfun.vpn',
            'com.expressvpn.vpn',
            'com.fishervpn.freevpn',
            'com.freevpnintouch',
            'com.goldenfrog.vyprvpn.app',
            'com.ifast.virtualvpn',
            'com.nordvpn.android',
            'com.surfshark.vpnclient.android',
            'com.suxxt.vpnanonymity',
            'com.whitebunny.vpn',
            'com.windscribe.vpn',
            'com.wl.ufovpn',
            'com.xy.vpn',
            'hotspotshield.android.vpn',
            'com.lishun.flyfish',
            'top.a6b.shendunet',
            'com.qi.earthnutproxy',
            'com.qi.staticsproxy',
            'com.mi.oa',
            'com.baidu.hi'
          ]
        },
        checkPackageListWhite: {
          dev: ['com.tencent.mm', 'com.tencent.mobileqq'],
          prod: ['com.tencent.mm', 'com.tencent.mobileqq']
        }
      }
    },
    vivo: {
      app: {
        // 检测安装包列表
        checkPackageList: {
          dev: ['com.njh.biubiu'],
          prod: [
            'com.huawei.fastapp.dev',
            'org.hapjs.mockup',
            'org.hapjs.debugger',
            'com.zouqi.qingfeng',
            'com.fvcorp.android.aijiasuclient',
            'com.zaozhu.netplus',
            'com.qlbd.quanliangpromote',
            'com.mengdie.turtlenew',
            'cn.ccspeed',
            'com.xiongmao886.tun',
            'com.zx.a2_quickfox',
            'com.sanye.xiongyingjiasuqi',
            'com.dongguo.feiyu',
            'com.netease.uu',
            'com.njh.biubiu',
            'com.huawei.welink',
            'com.huawei.works',
            'com.vivo.hybrid.sdkdemo',
            'com.vivo.hybrid.debugger',
            'com.sie.mp',
            'com.teamtalk.im',
            'com.ss.android.lark.kami',
            'com.yiqixie.kem',
            'com.bytedance.ad.deliver',
            'com.baidu.fengchao.ui',
            'ch.protonvpn.android',
            'com.avira.vpn',
            'com.birdvpn.app',
            'com.daxiang.vpn',
            'com.dmvpn.vpnfree',
            'com.fastfun.vpn',
            'com.expressvpn.vpn',
            'com.fishervpn.freevpn',
            'com.freevpnintouch',
            'com.goldenfrog.vyprvpn.app',
            'com.ifast.virtualvpn',
            'com.nordvpn.android',
            'com.surfshark.vpnclient.android',
            'com.suxxt.vpnanonymity',
            'com.whitebunny.vpn',
            'com.windscribe.vpn',
            'com.wl.ufovpn',
            'com.xy.vpn',
            'hotspotshield.android.vpn',
            'com.lishun.flyfish',
            'top.a6b.shendunet',
            'com.qi.earthnutproxy',
            'com.qi.staticsproxy',
            'com.mi.oa',
            'com.baidu.hi'
          ]
        },
        checkPackageListWhite: {
          dev: ['com.tencent.mm', 'com.tencent.mobileqq'],
          prod: ['com.tencent.mm', 'com.tencent.mobileqq']
        }
      }
    },
    honor: {
      app: {
        // 检测安装包列表
        checkPackageList: {
          dev: ['com.njh.biubiu'],
          prod: [
            'com.huawei.fastapp.dev',
            'org.hapjs.mockup',
            'org.hapjs.debugger',
            'com.zouqi.qingfeng',
            'com.fvcorp.android.aijiasuclient',
            'com.zaozhu.netplus',
            'com.qlbd.quanliangpromote',
            'com.mengdie.turtlenew',
            'cn.ccspeed',
            'com.xiongmao886.tun',
            'com.zx.a2_quickfox',
            'com.sanye.xiongyingjiasuqi',
            'com.dongguo.feiyu',
            'com.netease.uu',
            'com.njh.biubiu',
            'com.huawei.welink',
            'com.huawei.works',
            'com.vivo.hybrid.sdkdemo',
            'com.vivo.hybrid.debugger',
            'com.sie.mp',
            'com.teamtalk.im',
            'com.ss.android.lark.kami',
            'com.bytedance.ad.deliver',
            'com.baidu.fengchao.ui',
            'ch.protonvpn.android',
            'com.avira.vpn',
            'com.birdvpn.app',
            'com.daxiang.vpn',
            'com.dmvpn.vpnfree',
            'com.fastfun.vpn',
            'com.expressvpn.vpn',
            'com.fishervpn.freevpn',
            'com.freevpnintouch',
            'com.goldenfrog.vyprvpn.app',
            'com.ifast.virtualvpn',
            'com.nordvpn.android',
            'com.surfshark.vpnclient.android',
            'com.suxxt.vpnanonymity',
            'com.whitebunny.vpn',
            'com.windscribe.vpn',
            'com.wl.ufovpn',
            'com.xy.vpn',
            'hotspotshield.android.vpn',
            'com.lishun.flyfish',
            'top.a6b.shendunet',
            'com.qi.earthnutproxy',
            'com.qi.staticsproxy',
            'com.yiqixie.kem',
            'com.mi.oa',
            'com.baidu.hi'
          ]
        },
        checkPackageListWhite: {
          dev: ['com.tencent.mm', 'com.tencent.mobileqq'],
          prod: ['com.tencent.mm', 'com.tencent.mobileqq']
        }
      }
    }
  }
}

module.exports = config
