<template>
  <div class="dialog-wrap">
    <!-- <div class="dialog-mask-layer" @click="dialogClose"></div> -->
    <div class="dialog-content">
      <text class="dialog-title" if="title">{{ title }}</text>
      <div class="dialog-btn" style="justify-content: {{ cancelBtnText&&confirmBtnText? 'space-between' : 'center'}} ">
        <text class="dialog-cancel-btn" if="cancelBtnText" @click="dialogClose">{{ cancelBtnText }}</text>
        <text class="dialog-confirm-btn" if="confirmBtnText" @click="dialogConfirm">{{ confirmBtnText }}</text>
      </div>
    </div>
  </div>
</template>


<script>
export default {
  data() {
    return {

    };
  },
  props: {
    title: {  // 文本
      type: String,
      default: ''
    },
    cancelBtnText: {
      type: String,
      default: ''
    },
    confirmBtnText: {
      type: String,
      default: ''
    }
  },

  onInit() {

  },
  /**
   * 弹窗-关闭
   */
  dialogClose() {
    this.isOpen = false;
    this.$emit('dialogClose')
  },
  /**
   * 弹窗-确认
   */
  dialogConfirm() {
    this.$emit('dialogConfirm')
  }
};

</script>
<style lang="less">
  .dialog-wrap {
    position: fixed;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.60);
    flex-direction: column;
    justify-content: center;
    align-items: center;
    // .dialog-mask-layer {
        // flex: 1;
    // }
    .dialog-content {
      margin: 0 auto;
      padding: 30px;
      flex-direction: column;
      width: 590px;
      // height: 300px;
      background-color: #fff;
      border-radius: 36px;
      .dialog-title {
          text-align: center;
          padding: 20px 50px 50px;
          font-size: 32px;
          font-weight: 700;
          color: #333333;
          line-height: 52px;
          margin-bottom: 40px;
      }
      .dialog-btn {
          flex: 1;
          justify-content: space-between;
          .dialog-confirm-btn{
              width: 250px;
              height: 80px;
              background-color: #F11212;
              border-radius: 200px;
              font-size: 32px;
              font-weight: bold;
              color: #ffffff;
              text-align: center;
          }
          .dialog-cancel-btn {
              width: 250px;
              height: 80px;
              background-color: #fff;
              border-radius: 200px;
              font-size: 32px;
              color: #999999;
              text-align: center;
              border: 2px solid #d8d8d8;
          }
      }
    }
  }
</style>
