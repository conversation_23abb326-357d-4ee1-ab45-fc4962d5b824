<template>
  <div class="activity-wrappar">
    <div class="web-container">
      <web
        id="web"
        class="web"
        src="{{webUrl}}"
        onpagefinish="onPageFinishHandle"
        onmessage="onMessageHandle"
      ></web>
    </div>
    <div @click="backClickHandle" class="icon-wrapper" style="top: {{statusBarHeight + 30}}px">
        <image src="https://img.qdreads.com/v155/images/back-icon.png"></image>
    </div>
    <!-- 返回腾讯系App的button -->
    <back-app-button
      if="{{$app.$def.isTencentBackButtonVisible}}"
      btn-text="{{$app.$def.tencentBackName}}"
      back-url="{{$app.$def.tencentBackUrl}}"
      package-name="{{$app.$def.tencentBackPkg}}"
    ></back-app-button>
  </div>
</template>
<import name="common-header" src="../../components/common-back-header/index"></import>
<import name="back-app-button" src="../../components/back-app-button"></import>

<script>
import share from '@service.share';
import router from '@system.router'
export default pageMixin({
    private: {
        pushId: null,
        webUrl: 'https://share.qdreads.com/#/invite',
        targetUrl: 'https://share.qdreads.com/#/startPage',
        pageDetail: {
            pageRoute: '/pagesB/Activity',
            pageName: '邀请好友页',
            pageUrl: '邀请好友页',
            pageCode: 'READ_COMMON',
            pageOrigin: ''
        },
    },
    public: {
        pathUrl: "",
        taskCode: 'invite', // 1早上分享 2中午分享 3晚上分享 4邀请好友
        statusBarHeight: 40,
        shareType: '',
    },

    onInit() {
        this.pathUrl = this.pathUrl
        this.pushId = this.pushId

        this.shareType = this.getShareTypeByTaskCode()
        console.log(this.shareType)
        $utils.getStorage("userInfo").then(res => {
            if (res) {
                var shareId = JSON.parse(res)['id'];
                this.targetUrl = `${this.targetUrl}?shareType=${this.shareType}&shareId=${shareId}`;
            }
        })
        this.statusBarHeight = sdk.env.device.statusBarHeight

    },
    onShow() { 
    

    },
    onHide() {
      
    },
    // 根据任务code获取分享type
    getShareTypeByTaskCode() {
        let shareType = 4
        switch (this.taskCode) {
            case 'mornShare':
                shareType = 1
                break
            case 'noonShare':
                shareType = 2
                break
            case 'nightShare':
                shareType = 3
                break
            case "invite":
                shareType = 4
                break
        }
        return shareType
    },


    toShare(platforms) {
        const that = this
        share.share({
            shareType: 0,
            title: '邀请好友得金币',
            imagePath: '/assets/image/logo.png',
            targetUrl: that.targetUrl,
            platforms: platforms,
            success: function (data) {
                console.log('handling success')
                // 分享后福利任务状态更新
                that.$app.$def.taskInfoUpdateHandle({
                    task_code: that.taskCode
                })
            },
            fail: function (data, code) {
                console.log(`handling fail, failMess=${data},code=${code}`)
            },
            cancel: function (data) {
                console.log('handling success')
                // 分享后福利任务状态更新
                that.$app.$def.taskInfoUpdateHandle({
                    task_code: that.taskCode
                })
            }
        })
    },

    // web页面事件回调
    onMessageHandle(e) {
        console.log("接收H5发送的消息", e)
        COMMON_REPORT_UTILS.page_click_report('邀请好友') //点击上报
        var than = this
        share.getAvailablePlatforms({
            success: function (res) {
                console.log("res=", res.platforms)
                if (res.platforms.length > 0) {
                    than.toShare(res.platforms);
                } else {
                    than.toShare(['SYSTEM']);
                }

            },
            fail: function (err) {
                console.log("err=", err)
            }
        })
    },

    // web加载完成回调
    onPageFinishHandle(e) {
        let that = this;
        $utils.getStorage("token").then(res => {
            that.$element('web').postMessage({ message: res });
        })
    },

    // 按钮返回
    backClickHandle() {
        this.stacksPath()
                COMMON_REPORT_UTILS.page_click_report('返回') //点击上报
    },
    // 物理返回
    onBackPress() {
        // if (this.pushId != null && this.pushId != undefined) {}
        this.stacksPath()
        return true
    },
    // 返回路径判断
    stacksPath() {
        if (this.pushId != null && this.pushId != undefined) {
            $utils.routeReplacetheUrl('pagesA/Main')
        } else {
            this.$page.finish()
        }
    }
})
</script>

<style lang="less">
.activity-wrappar {
  width: 100%;
  flex-direction: column;
  .web-container {
    width: 100%;
    height: 100%;
    .web {
      width: 100%;
      height: 100%;
    }
  }
}

.icon-wrapper {
    position: absolute;
    left: 24px;
    width: 50px;
    image {
        width: 16px;
        height: 30px;
    }
}
</style>
