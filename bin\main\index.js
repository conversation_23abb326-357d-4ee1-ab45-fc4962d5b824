const path = require('path')
const fs = require('fs')
const { getAppName, questionBrand } = require('../utils/question')
const { setAppBasePath } = require('../config/app-base')
const { parseConfig } = require('../config/parse')
const { consoleSplit } = require('../utils/console')
const { deleteGenerateFile } = require('../main/tasks/delete-src')
const copyPublicFile = require('./tasks/copy-public')
const copyAppFile = require('./tasks/copy-app')
const { watchAppFile, watchPublicFile } = require('./tasks/watch-file')
const loading = require('../utils/loading')
const { projectPath } = require('./constant')
const { updateManifest, appGlobalConfig } = require('./tasks/create-config')
const { selectSdk } = require('./tasks/select-sdk')
const { compBrandCode } = require('./tasks/comp-brand-code')
const { checkVersion } = require('./tasks/check-version')
const { checkandCloneSDK, getYLHandBaidu } = require('../utils/sdkinit')
const { updateVersion } = require('./tasks/updateVersion')
const { editFont } = require('./tasks/edit-font')
const { versionCodeFormat } = require('./tasks/version-code-format')
const { quickappConfig } = require('./tasks/quickapp-config')
const { createAppDefinePlugin } = require('./tasks/create-app-plugin')

async function mainTask({
  ignoreSDK,
  sdk: inputSdk,
  app: inputApp,
  brand: inputBrand,
  exit: isInstantExit,
  upgrade: isUpdateVersion
}) {
  if (isUpdateVersion) {
    // 只升级版本号
    const { category, code, company } = await getAppName(inputApp)
    setAppBasePath(path.resolve(projectPath, './qkapp', `${category}/${code}`))
    consoleSplit('升级版本号')
    updateVersion(inputBrand)
    versionCodeFormat(inputBrand, company)
    process.exit(0)
  }

  if (!isInstantExit) {
    await checkVersion()
  }
  let selectSdkVersion = '本地node_modules/SDK'
  let isCompanyDivided = false // 是否区分公司

  if (!ignoreSDK) {
    // 选择sdk并更新
    selectSdkVersion = inputSdk || (await selectSdk())
  }
  consoleSplit('当前选择的SDK版本: ' + selectSdkVersion)
  deleteGenerateFile()

  consoleSplit('选择应用')
  const { category, code, appType = 'tool', company } = await getAppName(inputApp)
  consoleSplit('选择厂商')
  const brand = inputBrand || (await questionBrand())

  setAppBasePath(path.resolve(projectPath, './qkapp', `${category}/${code}`))

  // 版本号格式化
  consoleSplit('版本号格式化')
  versionCodeFormat(brand, company)

  if (isUpdateVersion) {
    process.exit(0)
  }

  // SDK 厂商代码生成
  consoleSplit('生成&更新SDK厂商代码')
  checkandCloneSDK(selectSdkVersion)
  // 业务 厂商代码生成
  consoleSplit('生成业务厂商代码')
  compBrandCode(brand, selectSdkVersion)

  // 复制应用文件
  consoleSplit('复制业务代码')
  copyAppFile({ category, code })

  consoleSplit('生成 SDK 厂商代码')
  require(path.resolve(projectPath, `./packages/ad-sdk/${selectSdkVersion}/scripts/brand/copy`))(brand)
  if (
    fs.existsSync(path.resolve(projectPath, `./packages/ad-sdk/${selectSdkVersion}/scripts/quickapp-manager-config.js`))
  ) {
    isCompanyDivided = true
    require(path.resolve(projectPath, `./packages/ad-sdk/${selectSdkVersion}/scripts/quickapp-manager-config.js`))(
      projectPath,
      company,
      brand
    )
  }
  const useStrReplace = require(
    path.resolve(projectPath, `./packages/ad-sdk/${selectSdkVersion}/package.json`)
  ).useComStr
  consoleSplit('复制百度和优量会')
  const { baiduVersion, ylhVersion } = getYLHandBaidu(selectSdkVersion)
  console.log('baiduVersion', baiduVersion)
  console.log('ylhVersion', ylhVersion)
  // 复制公共文件和SDK
  consoleSplit('复制公共文件和SDK')
  copyPublicFile(appType, selectSdkVersion)
  const { manifest, app } = parseConfig(brand)
  // 更新自定义配置，生成 manifest
  updateManifest(manifest, selectSdkVersion.replace('release-v', ''))

  // 特殊处理
  consoleSplit('特殊处理')
  editFont(projectPath)

  // 配置quickapp-config.js
  consoleSplit('配置quickapp-config.js')
  quickappConfig(isCompanyDivided, projectPath, company, brand, useStrReplace)

  // 生成 APP-config-define-plugin 配置文件
  createAppDefinePlugin(app, brand)

  // 生成全局 APP 配置
  appGlobalConfig(app)
  if (isInstantExit) {
    process.exit(0)
  }
  consoleSplit('开始监听应用和公共文件变化')
  // 监听 APP 代码变更
  watchAppFile({ category, code }, brand)
  // 监听 公共部分 代码变更
  watchPublicFile(appType, brand)
  // 加载动画
  loading()
}

module.exports = mainTask
