const fs = require('fs')
const path = require('path')
const { editFile } = require('../../utils/file-edit')
const lessPath = ['./src/pages/Read/index.less', './src/components/icon/index.ux'] // 需要替换的文件路径
const oldValue = 'https://test-fre.ghfkj.cn/' // 需要替换的值

function editFont(projectPath) {
  if (!fs.existsSync(path.resolve(projectPath, `./src/company-config.js`))) {
    return
  }
  const ttfUrlPath = path.resolve(projectPath, `./src/company-config.js`)
  const ttfUrl = require(ttfUrlPath).pro.__BUS_PUBLIC_OSS_BASE_URL__.replace(/"/g, '')
  lessPath.forEach(filePath => {
    if (!fs.existsSync(path.resolve(projectPath, filePath))) {
      return
    }
    editFile(filePath, oldValue, ttfUrl)
  })
}

module.exports = {
  editFont
}
