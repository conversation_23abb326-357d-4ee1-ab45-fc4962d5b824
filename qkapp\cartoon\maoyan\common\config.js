let TaskStatus = {
    type: '',
    status: false,
    num: 0
}

let nowBrightness, brightFlag = true;

/**
 * 
 * @returns 是否加桌
 */
function getAddDeskTop() {
    return new Promise((resolve, reject) => {
        require('@system.shortcut').hasInstalled({
            success: function (res) {
                resolve(res)
            },
            fail: function () {
                console.log('获取加桌失败');
                reject()
            }
        })
    })
}
/**
 * 监测支付宝是否安装
 * @returns 
 */
function alipayInstall() {
    return new Promise((resolve, reject) => {
        require('@system.package').hasInstalled({
            package: 'com.eg.android.AlipayGphone',
            success: function (data) {
                console.log(`handling success: ${data.result}`)
                resolve()
            },
            fail: function (data, code) {
                console.log(`handling fail, code = ${code}`)
                reject()
            }
        })
    })

}
/**
 *  分享
 * @returns  Promise
 */
function share(channelId, linkId) {
    LOG('=== config share')
    TaskStatus.type = 'share'
    TaskStatus.num = 0
    return new Promise((resolve, reject) => {
        // 写自己项目的分享
        try {

            require('@service.share').getAvailablePlatforms({
                success: function (data) {

                    require('@service.share').share({
                        shareType: 0,
                        title: '即时省电',
                        targetUrl: `https://hapjs.org/app/com.zywl.battery/pages/webPull?channelId=${channelId}&linkId=${linkId}`,
                        imagePath: '/assets/images/logo.png',
                        summary: '任务天天做，福利时时领',
                        platforms: ['WEIXIN', 'WEIXIN_CIRCLE', 'SYSTEM'],
                        success: function (data) {
                            console.log('handling success')
                            resolve()

                        },
                        cancel: function (data, code) {
                            console.log(`handling fail,${data} code = ${code}`)
                            reject()
                        },
                        fail: function (data, code) {

                            reject(true)
                            console.log(`handling complete, ${data} code = ${code}`)
                        },
                    })
                },
                fail: function (data, code) {

                }
            })

        } catch (error) {
            LOG('===error', error)
        }
    })
}
/**
 *  加桌
 * @returns  Promise
 */
function addDesk() {
    LOG('=== config addDesk')
    TaskStatus.type = 'addDesk'
    TaskStatus.num = 0
    return new Promise((resolve, reject) => {

        require('@system.shortcut').hasInstalled({
            success: function (res) {
                if (res) {
                    resolve()
                } else {
                    require('@system.shortcut').install({
                        success: function () {
                            console.log('handling success')
                            resolve()
                        },
                        fail: function (data, code) {
                            console.log(`handling fail, code = ${code}, errorMsg=${data}`)
                            reject(code)
                        },
                    })
                }

            }
        })



    })
}
/**
 * 日历提醒
 * @returns Promise
 */
function dateTip(brand) {
    LOG('=== config dateTip')
    // 次日同时间提示；标题：福利更新了   文案：
    TaskStatus.type = 'dateTip'
    TaskStatus.num = 0

    let description = ''
    if (brand == 'xiaomi') {
        description = '复制链接到浏览打开领取奖励：'
    } else {
        description = '查看今日福利：'
    }
    return new Promise((resolve, reject) => {
        require('@system.calendar').insert({
            title: '【即时省电】福利更新了',
            description: description + `https://hapjs.org/app/com.zywl.battery/pages/webPull?calendar=${Date.now()}`,
            startDate: Date.now() + 24 * 60 * 60 * 1000,
            endDate: Date.now() + 100 * 24 * 60 * 60 * 1000,
            remindMinutes: [5],
            allDay: false,
            duration: 'PT1H',
            rrule: 'FREQ=DAILY;WKST=SU;COUNT=1;BYHOUR=19;BYMINUTE=30;BYSECOND=0',
            success: function (data) {
                resolve()
            },
            fail: function (data, code) {
                console.log(`handling fail, code = ${code}, errorMsg=${data}`)
                reject(true)
            },
        })
    })
}
/**
 *  点击左上角返回
 * @returns Promise canBack 必填,用来标记是否进行触发物理返回
 */
function clickBack() {
    LOG('=== config clickBack')
    return new Promise((resolve, reject) => {
        resolve({ canBack: true })
    })
}
/**
 *  一键省电
 * @returns  Promise
 */
function clickBtn(pageUrl) {
    LOG('=== config clickBtn')
    return new Promise((resolve, reject) => {
        DEVICE_UTILS.getBrightness().then(res => {
            console.log("当前亮度信息HOME：", res)
            if (res) {
                nowBrightness = res.value
                if (brightFlag) {
                    // - 亮度
                    let setBrightness = Number(nowBrightness) - 70 <= 1 ? 1 : Number(nowBrightness) - 70
                    DEVICE_UTILS.setBrightness(setBrightness).then(res => {
                        nowBrightness = setBrightness;
                        brightFlag = false;
                        resolve()
                    })
                } else {
                    //  + 亮度
                    let setBrightness = Number(nowBrightness) + 70 >= 255 ? 200 : Number(nowBrightness) + 70
                    DEVICE_UTILS.setBrightness(setBrightness).then(res => {
                        nowBrightness = setBrightness;
                        brightFlag = true;
                        resolve()
                    })
                }
            }
        })
    })
}
/**
 *  toast 展示之前需要做的事
 * @returns  Promise
 */
function ToastShow(msg, _this) {
    LOG('=== config ToastShow')
    return new Promise((resolve, reject) => {
        try {
            _this.$child("toast").showToast({
                content: msg,
                duration: 3,
                mask: false
            })
        } catch (error) {
            LOG("=== error", error)
        }
        resolve()
    })
}
/**
 *  宝箱 展示之前需要做的事
 * @returns  Promise
 */
function boxShow() {
    LOG('=== config boxShow')
    TaskStatus.type = 'boxShow'
    TaskStatus.num = 0
    return new Promise((resolve, reject) => {
        resolve()
    })
}

/**
 *  跳转至活动页 需要定制 处理
 * @param  actionInfo 当前需要打开的活动页
 * @returns  Promise
 */
 function gotoAction(actionInfo, pageUrl) {
    TaskStatus.type = 'gotoAction'
    TaskStatus.num = 0
    LOG('=== config gotoAction', actionInfo)
    return new Promise((resolve, reject) => {
        resolve({
            url: '/pages/Action', //需要跳转的链接
            params: {
                actionLink: actionInfo.actionLink,
                isCanInit: true,
                pageUrl: pageUrl,
                isRouterBack: true
            }, // 携带参数
        })
    })
}

// 激励视频方案
function video(pageUrl, _this) {
    TaskStatus.type = 'video'
    TaskStatus.num = 0
    return new Promise((resolve, reject) => {
        resolve()
    })
}
function redPacket() {
    TaskStatus.type = 'redPacket'
    TaskStatus.num = 0
    LOG('=== config redPacket')
    return new Promise((resolve, reject) => {
        resolve()
    })
}
function sign() {
    TaskStatus.type = 'sign'
    TaskStatus.num = 0
    LOG('=== config sign')
    return new Promise((resolve, reject) => {
        resolve()
    })
}
function noAdToast(msg, _this) {
    LOG('=== config sign')
    return new Promise((resolve, reject) => {
        try {
            _this.$child("toast").showToast({
                content: msg,
                duration: 3,
                mask: false
            })
        } catch (error) {

        }
        resolve()
    })
}
function getTaskStatus() {
    return TaskStatus
}
function setTaskStatus(status) {
    LOG("=== setTaskStatus", status)
    TaskStatus.status = status || false
}
function setTaskNum() {
    TaskStatus.num += 1
}

function goToKeepMain(flag = true) {
    require('@system.router').push({
        uri: '/pages/Main',
        params: { isMakeMoneyH5: flag },
    })
}
/**
 * 砍价投诉页面
 */
function goToKeepComplaint() {
    require('@system.router').push({
        uri: '/subPages/complaint',
        params: { isMakeMoneyH5: true },
    })
}


function pushWebPull() {
    require('@system.router').replace({
        uri: '/pages/webPull',
        params: { h5: 'task' },
    })
}

function getWXToken() {
    const wxaccount = require("@service.wxaccount")
    let type = wxaccount.getType();
    console.log("===type", type)
    return new Promise((resolve, reject) => {
        if (type == 'APP') {
            console.log("wxaccount authorize 1111")
            try {
                wxaccount.authorize({
                    scope: 'snsapi_userinfo',
                    state: 'randomString',
                    success: function (data) {
                        console.log("wxaccount authorize success:" + JSON.stringify(data));
                        resolve({
                            code: data.code,
                            state: data.state
                        })
                    },
                    fail: function (data, code) {
                        console.log("wxaccount authorize  fail", data, code)
                        if (code == -2004) {
                            reject({
                                msg: '用户拒绝授权',
                            })
                        } else if (code == 1000) {
                            reject({
                                msg: '微信未安装',
                            })
                        } else if (code == 201) {
                            reject({
                                msg: '用户取消授权',
                            })
                        } else {
                            // 签名问题，配置问题 等其他问题

                            reject({
                                msg: '当前不支持微信提现',
                            })
                        }

                    },
                    cancel: function () {
                        console.log("wxaccount authorize cancelled.");
                        reject({
                            msg: '用户取消授权',
                        })
                    },
                    incomplete: function () {
                        console.log("wxaccount authorize incomplete.");

                    }
                });
            } catch (error) {
                console.log("wxaccount authorize error.", error);
            }
        } else {
            reject({
                msg: '当前不支持微信登录',
            })
        }

    })
}

function exchange() {
    require("@system.router").replace({
        uri: '/pages/webPull/exchange'
    })
}
function amountdetail() {
    require("@system.router").push({
        uri: '/pages/webPull/amountdetail'
    })
}
function moneydetail() {
    require("@system.router").push({
        uri: '/pages/webPull/moneydetail'
    })
}

function account(alipay, wechat) {
    require("@system.router").push({
        uri: '/pages/webPull/account',
        params: { alipay: alipay, wechat: wechat },
    })
}

function goToDefault() {
    require("@system.router").replace({
        uri: 'pages/Main'
    })
}

export default {
    share,
    addDesk,
    dateTip,
    clickBack,
    clickBtn,
    ToastShow,
    gotoAction,
    boxShow,
    video,
    redPacket,
    sign,
    noAdToast,
    getTaskStatus,
    setTaskStatus,
    setTaskNum,
    goToKeepMain,
    goToKeepComplaint,
    getWXToken,
    exchange,
    amountdetail,
    moneydetail,
    account,
    pushWebPull,
    alipayInstall,
    getAddDeskTop,
    goToDefault,
}
