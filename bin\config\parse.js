#!/usr/bin/env node
// 初始化 配置文件
const fs = require('fs')
const path = require('path')
const { getAppBasePath } = require('../config/app-base')
const { merge } = require('lodash')

const CONFIG_NAME = 'brand-config.js'

// 读取配置文件路径
const projectPath = path.resolve(__dirname)
const globalConfigPath = path.resolve(projectPath, CONFIG_NAME)

/**
 * 解析配置
 *
 * @param brand 当前打包的厂商
 */
function parseConfig(brand) {
  const appConfigPath = path.resolve(getAppBasePath(), CONFIG_NAME)

  if (!fs.existsSync(appConfigPath)) {
    const str = fs.readFileSync(path.resolve(__dirname, '../template/app.config.js'))
    fs.writeFileSync(appConfigPath, str)
  }
  const appConfig = require(appConfigPath)
  const projectPath = require(globalConfigPath)

  // 全局配置下的 厂商 => app 配置
  const projectBrandAppConfig = projectPath.brand[brand].app || {}

  return {
    app: merge({}, appConfig.app, projectBrandAppConfig),
    manifest: merge({}, appConfig.manifest, appConfig.brand[brand].manifest)
  }
}

function getConfig() {
  return require(globalConfigPath)
}

module.exports = {
  getConfig,
  parseConfig
}
