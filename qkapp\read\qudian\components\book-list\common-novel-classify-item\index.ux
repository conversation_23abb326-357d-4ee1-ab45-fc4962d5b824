<template>
  <!-- template里只能有一个根节点 -->
  <div style="width: 100%">
    <div style="width: 100%" if="{{novelData}}" @click="bookItemClickHandler">
      <stack class="book-pic">
        <image src="https://img.qdreads.com/v173/sjyy_dcc.png" style="width:142px;height:189px;margin-top:7px;"></image>
        <div style="margin-left:7px;"> 
          <image
            src="{{novelData.bookIconUrl}}"
            style="object-fit: fill;width:128px;height:181px;border-radius: 6px;"
          ></image>
          <book-status xstype="{{novelData.xstype}}"></book-status>
        </div>
      </stack>
      <div class="book-item-detail">
        <text class="book-name">{{ novelData.bookName }}</text>
        <text class="book-desc">{{ novelData.bookContenUrl }}</text>
        <div style="height: 22px;margin-top: 22px;align-items: center;">
            <text style="height: 22px;font-size: 22px;color: #C8AB80;">{{novelData.fcate}}</text>
            <div style="width:4px;height:4px;border-radius:50%;background-color:#adadad;margin:2px 4px 0;"></div>
            <text style="height: 22px;font-size: 22px;color: #adadad;">{{ novelData.bookNum | readNumHandle }}人阅读</text>
        </div>
      </div>
    </div>
    <div style="width: 100%" else>
      <div style="background-color: #EFEFEF" class="book-pic"></div>
      <div class="book-item-detail">
        <text style="background-color: #EFEFEF" class="book-name"></text>
        <text style="background-color: #EFEFEF" class="book-desc"></text>
        <div class="book-type">
          <text style="background-color: #EFEFEF;width: 140px;" class="book-author"></text>
          <text style="background-color: #EFEFEF;width: 100px;" class="book-type-detail novel-type-4"></text>
        </div>
      </div>
    </div>
  </div>
  
</template>

<import name="book-status" src="../../../components/book-list/book-status/index.ux"></import>

<script>
export default {
  data: {
    randomCount: 0
  },
  onInit() {
    this.randomCount = Math.ceil(Math.random() * 5)
  },
  bookItemClickHandler() {
    if(!CLICK_UTILS.dom_click_vali_shake(`novelClassifyItem_${this.__id__}`,500)) return

    this.$emit('compClick', { bookId: this.novelData.bookId, bookName: this.novelData.bookName })
  },
  //阅读人数处理 万级 处理成小数点
  readNumHandle(readNum) {
    if (Number(readNum) > 10000) {
      return (Number(readNum) / 10000).toFixed(1) + '万'
    }
    return readNum
  },
  props: {
    novelData: {
      type: Object,
      default: null
    },
    isShowType: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style lang="less">
@import './index.less';
</style>
