<template>
  <div class="ti-wrapper">
    <stack style="justify-content: center;">
      <image class="ti-icon-1" src="{{data.task_icon}}"></image>
      <div class="ti-lable-text">
        <text if="{{data.task_code == 'consecutiveLogin'}}">会员</text>
        <text elif="{{data.task_code == 'readAdvert'}}">盲盒</text>
        <text else>+{{data.book_tickets}}</text>
        <image if="{{data.task_code == 'consecutiveLogin'}}" src="https://img.qdreads.com/v155/welfare1.5.0/task-tag-2.png"></image>
        <image elif="{{data.task_code == 'readAdvert'}}" src="https://img.qdreads.com/v155/welfare1.5.0/task-tag-3.png"></image>
        <image else src="https://img.qdreads.com/v155/welfare1.5.0/task-tag.png"></image>
      </div>
    </stack>
    <div class="ti-info">
      <text class="ti-title">{{data.task_name}}</text>
      <text class="ti-des">{{data.task_desc}}</text>
    </div>
    <!-- <text @click="btnClick" class="ti-button ti-button-{{data.status}} ti-button-{{formatState(data).type}}">{{formatState(data)['text']}}</text> -->
    <text if="{{data.status == 0}}"  @click="btnClick" class="ti-button {{ formatState(data)['type'] == 1 ? 'button-color-2':'button-color-3' }}">{{ formatState(data)['text'] }}</text>
    <text elif="{{data.status == 1}}" class="ti-button" @click="btnClick">待领取</text>
    <text else class="ti-button ti-button-2">已完成</text>
  </div>
</template>

<script>
export default {
  data: {
    
  },
  
  props: {
    data: {
      type: Object,
      default: {
        icon: "https://img.qdreads.com/v155/welfare1.5.0/task-icon-1.png",
        tag: "https://img.qdreads.com/v155/welfare1.5.0/task-tag.png",
        num: "+100",
        task_name: "兴趣爱好",
        des: "设置读书喜好",
      }
    }
  },
  
  onInit() {},

  btnClick() {
    let formatObj = this.formatState(this.data)
    if (formatObj.type == 2) return
    this.$emit('btnClick', {data: this.data, text: formatObj.text})
  },

  formatState(item) {
    let H = new Date().getHours()
    let obj = new Object({
      text: '去完成',
      type: 1
    })
    if (item.task_code == 'mornShare') {
      if (H < 6) {
        obj.text = "时间未到"
        obj.type = 2
      } else if (H >= 6 && H < 12) {
        obj.text = "去完成"
        obj.type = 1
      } else {
        obj.text = "已过期"
        obj.type = 2;
      }
    } else if (item.task_code == 'noonShare') {
      if (H < 12) {
        obj.text = "时间未到"
        obj.type = 2;
      } else if (H >= 12 && H < 18) {
        obj.text = "去完成"
        obj.type = 1
      } else {
        obj.text = "已过期"
        obj.type = 2
      }
    } else if (item.task_code == 'nightShare') {
      if (H < 18) {
        obj.text = "时间未到"
        obj.type = 2
      } else if (H >= 18 && H < 24) {
        obj.text = "去完成"
        obj.type = 1
      } else {
        obj.text = '已过期'
        obj.type = 2
      }
    }
    return obj
  },
}
</script>

<style>
.ti-wrapper {
  align-items: center;
  width: 638px;
  height: 140px;
}

.ti-lable-text {
  width: 84px;
  height: 32px;
  background: linear-gradient(90deg,#fb8720, #f84a41);
  border-radius: 16px;
  align-items: center;
  margin-top: 60px;
  justify-content: center;
}

.ti-lable-text > text {
  height: 20px;
  font-size: 20px;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 2px;
}

.ti-lable-text > image {
  margin-left: 6px;
  width: 20px;
  height: 20px;
}

.ti-button {
  width: 140px;
  height: 60px;
  background: linear-gradient(90deg,#fb8720, #f84a41 100%);
  border-radius: 30px;
  font-size: 28px;
  font-weight: 700;
  text-align: center;
  color: #ffffff;
}

.ti-info {
  margin-left: 24px;
  flex-direction: column;
  flex: 1;
}

.ti-title {
  height: 32px;
  font-size: 32px;
  font-weight: 500;
  color: #333333;
}

.ti-des {
  height: 24px;
  font-size: 24px;
  font-weight: 400;
  color: #999999;
  margin-top: 16px;
}

.ti-icon-1 {
  width: 80px;
  height: 80px;
}

.ti-button-2 {
  background: linear-gradient(90deg,#dfdfdf, #d1d1d1);
}

.button-color-2 {
  background: linear-gradient(90deg,#fb8720, #f84a41 100%);
}

.button-color-3 {
    background: linear-gradient(180deg, #eaeaea, #e1e1e1 100%);
    color:  #b9b8b8;
}
</style>
