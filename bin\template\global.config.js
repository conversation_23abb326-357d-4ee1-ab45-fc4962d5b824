/**
 * 对接配置
 * 分为 单 APP 配置和 分厂商配置，如果两者都配置了同一个字段，则以app配置为准
 * @type {{app: {}, brand: {}}}
 */
const config = {
  // 系统数据
  app: {
    // 系统内部数据，都写在这里，之后会统一放在 $config 变量下
    // 支持dev和prod，如果一致，则不用写 dev prod，直接写一个即可
  },

  // 分厂商配置，如果有相同字段，则以分厂商配置为准
  brand: {
    xiaomi: {
      app: {
        // 检测安装包列表
        checkPackageList: {
          dev: [],
          prod: []
        }
      }
    },
    hw: {
      app: {
        // 检测安装包列表
        checkPackageList: {
          dev: [],
          prod: []
        }
      }
    },
    oppo: {
      app: {
        // 检测安装包列表
        checkPackageList: {
          dev: [],
          prod: []
        }
      }
    },
    vivo: {
      app: {
        // 检测安装包列表
        checkPackageList: {
          dev: [],
          prod: []
        }
      }
    },
    honor: {
      app: {
        // 检测安装包列表
        checkPackageList: {
          dev: [],
          prod: []
        }
      }
    }
  }
}

module.exports = config
