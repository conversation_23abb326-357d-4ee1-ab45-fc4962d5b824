/** 
*  page：书架页样式
*  author: yang<PERSON>
*  date: 2022-02-24
*/
.shelf-wrapper {
    width: 750px;
    flex-direction: column;
    background-color: #F5F5F5;
    
    // 书架书籍
    .shelf-book-section {
        padding: 30px 0;
        flex-direction: column;
        background-color: #ffffff;
        width: 690px;
        margin: 30px 0 0 30px;
        border-radius: 24px;

        .shelf-book-section-list {
            width: 100%;
            flex-direction: column;

            .shelf-boook-item-image {
                width: 183px;
                height: 259px;
                border-radius: 6px;
            }

            .shelf-boook-item-title {
                font-size: 30px;
                height: 30px;
                lines: 1;
                text-overflow: ellipsis;
                color: #333333;
                margin-top: 0px;
            }
            .shelf-boook-item-author {
                font-size: 24px;
                color: #adadad;
                margin-top: 18px;
                height: 24px;
                lines: 1;
                text-overflow: ellipsis;
            }
            
            .empty{
                width: 100%;
                justify-content: center;
                padding-top: 100px;

                .empty-section {
                    width: 702px;
                    flex-direction: column;
                    .empty-image {
                        width: 350px;
                        height: 370px;
                        margin: 0 auto;
                    }
                    .empty-des {
                        font-size: 24px;
                        font-weight: 500;
                        color: #999999;
                        text-align: center;
                    }
                }
            }
        }
        
    }
    .dialog-wrap {
        position: fixed;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.60);
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .dialog-mask-layer {
            flex: 1;
        }
        .dialog-content {
            flex-direction: column;
            width: 540px;
            height: 270px;
            background-color: #fff;
            border-radius: 24px;

            .shelf-del-title {
                height: 50px;
                text-align: center;
                margin-top: 50px;
                margin-bottom: 60px;
                color: #333333;
                font-size: 34px;
                font-weight: 600;
            }
            .shelf-del-btn {
                width: 225px;
                height: 80px;
                background-color: #f11212;
                border-radius: 50px;
                text-align: center;
                font-size: 32px;
                font-weight: 600;
                color: #ffffff;
            }
        }
      }

    .top-bg {
        width: 750px;
        height: 444px;

        image {
            width: 100%;
            height: 100%;
        }
    }

    .shelf-recommom-section {
        margin-top: 46px;
        height: 267px;
    }

    .recommom-swiper {
        width: 100%;
        // padding: 0 30px;
        // padding-left: 56px;
        // indicator-color: #7b7a8c;
        // indicator-selected-color: #ffffff;
        // indicator-size: 6px;
        height: 100%;
        border-radius: 24px;
        // indicator-bottom: 10px;
    }

    .recommom-info-wrapper {
        flex-direction: column;
        flex-grow: 1;
    }

    .ri-name {
        lines: 1;
        font-size: 30px;
        color: #333333;
        height: 30px;
        text-overflow: ellipsis;
        margin-right: 108px;
    }

    .ri-content {
        font-size: 24px;
        color: #adadad;
        line-height: 36px;
        lines: 2;
        margin-top: 16px;
        text-overflow: ellipsis;
    }

    .recommom-wrapper {
        width: 690px;
        padding: 30px 30px 4px 23px;
    }

    .recommom-image-wrapper {
        // width: 160px;
        // height: 198px;
        border-radius: 6px;
        margin-right: 33px;
    }

    .ri-author-wrapper {
        justify-content: space-between;
        margin-top: 20px;
    }

    .ri-author {
        font-size: 22px;
        color: #c7c6ce;
        height: 22px;
        margin-top: 10px;
    }

    .ri-btn {
        width: 164px;
        height: 56px;
        background-color: #38415f;
        border-radius: 32px;
        justify-content: center;

        > text {
            font-size: 28px;
            color: #ffffff;
        }
    }

    .shelf-list-title {
        justify-content: space-between;
        padding: 0 30px;
        
        .shelf-edit {
            font-size: 28px;
            color: #333333;
            height: 28px;
            margin-top: 8px;
        }
    }

    .ri-star-wrapper {
        margin-top: 32px;
    }

    .ri-star {
        height: 24px;
        width: 24px;
    }

    .ri-score {
        font-size: 24px;
        color: #fcb909;
        line-height: 24px;
        height: 24px;
        margin-left: 12px;
    }
}

.shelf-ad-item {
    // height: 304px;
}

.ad-wrapper{
    width: 100%;
     margin-top: 20px;
}

.shelf-header {
    top: 0;
    left: 0;
    width: 750px;
    align-items: center;
    padding-left: 30px;
    position: absolute;
}

.shelf-title1 {
    width: 144px;
    height: 36px;
}


.banzi-image {
    position: absolute;
    top: 216px;
    left: 16px;
    width: 718px;
    height: 48px;
}

.image-cover {
    width: 183px;
    height: 259px;
    opacity: 0.6;
    background-color: #000000;
    border-radius: 8px;
}

.shelf-header-title {
    font-size: 40px;
    color: #232730;
    font-weight: bold;
}

.shelf-guide-2 {
    width: 330px;
    height: 50px;
    background-color: #333333;
    border-radius: 10px;
    align-items: center;
    justify-content: center;
}

.shelf-guide-2-wrapper {
    flex-direction: column;
    position: absolute;
    transition: opacity 1s ease-in 0s;
}

.triangle1 {
    width: 10px;
    height: 15px;
    background-repeat: no-repeat;
    background:  linear-gradient(-56deg, #333333 50%, rgba(255, 255, 255, 0) 50%);
    background-size: 100% 50%;
  }
  
  .triangle2 {
    width: 10px;
    height: 15px;
    background-repeat: no-repeat;
    background:  linear-gradient(56deg, #333333 50%, rgba(255, 255, 255, 0) 50%);
    background-size: 100% 50%;
  }

.shelf-del-cancel-btn {
    width: 225px;
    height: 80px;
    border: 2px solid #d8d8d8;
    border-radius: 50px;
    margin: 0 30px;
    text-align: center;
    font-size: 32px;
    color: #999999;
}

.indi-wrapper {
    height: 8px;
}

.indicator {
    background-color: #C0C0C0;
    border-radius: 50%;
    width: 6px;
    height: 6px;
}

.shelf-boook-item-del {
    width: 34px;
    height: 34px;
    position: absolute;
    left: 10px;
    top: 10px;
}

.swiper-lottie {
    width: 132px;
    height: 48px;
    position: absolute;
    top: 18px;
    right: 16px;
}

.loading-view-image {
    width: 112px;
    height: 112px;
    justify-content: center;
    align-items: center;

    image {
      width: 78px;
      height: 78px;
    }
}

.loading-lottie {
    width: 78px;
    height: 78px;
}

.show-loading {
    animation-name: showLoading;
    animation-duration: 100ms;
    animation-fill-mode: forwards;
    animation-timing-function: ease-in-out;
}

.hide-loading {
    animation-name: hideLoading;
    animation-duration: 100ms;
    animation-fill-mode: forwards;
    animation-timing-function: ease-in-out;
}

@keyframes hideLoading {
    0% {
        transform: translateY(112px);
    }
    100% {
        transform: translateY(0px);
    }
}

@keyframes showLoading {
    0% {
        transform: translateY(0px);
    }
    100% {
        transform: translateY(112px);
    }
}

.shelf-boook-item {
    width: 197px;
    flex-direction: column;
    margin-top: 40px;
    flex-shrink: 0;
}