const tagReplace = require('../utils/replace/tag-replace')
const fs = require('fs')
const path = require('path')

const code = fs.readFileSync(path.resolve(__dirname, './code/main.ux')).toString()

const result = tagReplace({
  content: code,
  oldCode: 'div.custom-pop-group',
  newCode: `<div class="native-boost-group" style="width: 750px; height: 500px; opacity: 0;position:absolute;">
      <custom-pop-group
        onevent-watch="nativeBoostGroupEventDispatchHandler"
        onappear="popComponmentAppearHandler"
      ></custom-pop-group>
    </div>`,
  failCodeInfo: [],
  file: '',
  desc: ''
})

console.log(result)
