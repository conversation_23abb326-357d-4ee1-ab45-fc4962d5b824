<template>
  <!-- template里只能有一个根节点 -->
  <div>
    <div class="classify-wrapper">
      <div style="flex-direction:column;width:750px;">
        <common-header></common-header>
        <div class="classify-header">
          <div class="tab-container">
            <div
              for="{{headerTabData.list}}"
              @click="sexTabClick($idx)"
              class="tab-item"
            >
              <div class="tab-title-wrapper">
                <text class="{{headerTabData.index == $idx ? 'tab-title-select' : 'tab-title-normal'}}">{{$item.title}}</text>
              </div>
              <div if="{{headerTabData.index == $idx}}" style="margin: 6px auto 0">
                <div style="width: 8px;height: 8px;border-top-left-radius: 100%;background-color: #f11212;"></div>
                <div style="width: 30px;height: 8px;background-color: #f11212;"></div>
                <div style="width: 8px;height: 8px;border-bottom-right-radius: 100%;background-color: #f11212;"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div style="margin-top:30px;flex-grow:1;">
        <list class="classify-left-content">
          <list-item 
            type="classify" 
            class="first-classify-item {{index == bookFirstCategoryData.list.length - 1 ? 'first-classify-margin' : ''}}" 
            for="(index,item) in bookFirstCategoryData.list" 
            @click="firstCategoryClickHandler(index)"
          >
            <text
                class="first-classify-text-item {{bookFirstCategoryData.index == index ? 'first-classify-text-active' : ''}}"
                >{{ item.title }}</text
              >
          </list-item>
        </list>
        <div style="flex-grow:1;flex-direction:column;background-color:#ffffff;border-top-left-radius: 24px;border-top-right-radius: 24px;">
          <text style="height: 26px;font-size: 26px;color: #666666;margin:30px 40px 0;">{{bookFirstCategoryData.list[bookFirstCategoryData.index].title}}</text>

          <div class="classify-status-list">
            <text
              for="(index,item) in bookStatusData.list"
              @click="statusItemClickHandler(index)"
              class="status-item-text {{bookStatusData.index == index ? 'status-item-active' : ''}}"
              style="margin-left: {{index == 0 ? 0 : 50}}px"
              >{{ item.title }}</text
            >
          </div>

          <div class="classify-right-content">
            <list class="book-list" onscrollbottom="scrollBottomHandler">
              <block if="{{getDataFinished}}">
                <list-item
                class="book-item"
                type="bookItem"
                for="(index,item) in bookListInfo.list"
                style="margin-top:{{index == 0?0:33}}px"
              >
                <common-novel-classify-item
                  novel-data="{{item}}"
                  is-show-type="{{true}}"
                  oncomp-click="compClickHandler"
                ></common-novel-classify-item>
              </list-item>
              </block>
              <block else>
                <list-item
                  class="book-item"
                  type="bookItem"
                  for="(index,item) in bookEmptyList"
                >
                  <common-novel-classify-item
                    novel-data="{{null}}"
                  ></common-novel-classify-item>
                </list-item>
              </block>
              
              <list-item type="loadMore"></list-item>
              <list-item
                class="empty-container"
                type="emptyImage"
                if="{{getDataFinished && bookListInfo.list.length == 0}}"
              >
                <image
                  class="empty-image"
                  src="https://img.qdreads.com/v155/common/common-list-empty.png"
                ></image>
              </list-item>
              <list-item type="bottom">
                <div if="{{isShowAddDesktop}}" style="width:100%;height:{{addDesktopHeight + 20}}px"></div>
              </list-item>
            </list>
          </div>
        </div>
      </div>

    </div>
  </div>
</template>
<import name="common-header" src="../components/common-header/index"></import>
<import name="common-novel-classify-item" src="../../components/book-list/common-novel-classify-item/index.ux"></import>
<script>
import router from '@system.router'

export default {
  data: {
    loading: false,
    headerTabData: { //性别分类数据
      index: 0,
      list: [{
        title: '男频',
        id: 1
      },
      {
        title: '女频',
        id: 2
      }]
    },
    bookStatusData: { //书籍状态分类数据
      index: 0,
      list: [{
        title: '全部',
        id: -1
      }, {
        title: '连载',
        id: 0
      }, {
        title: '完结',
        id: 1
      }]
    },
    bookFirstCategoryData: { //一级分类数据
      index: 0,
      list: [{
        title: '全部',
        id: 0
      }]
    },
    bookSecondCategoryData: { //二级分类数据
      index: 0,
      list: [{
        title: '全部',
        id: 0
      }]
    },
    bookListInfo: { //书籍列表信息
      page: 1,
      isEnd: false,
      list: [],
      totalPage: 1
    },
    getDataFinished: false,
    bookEmptyList: ['','','','','','']
  },
  /**
   * 书籍列表滑动到底部了
   */
  scrollBottomHandler() {
    this.bookListInfo.page++
    if (this.bookListInfo.page > this.bookListInfo.totalPage) {
      $utils.showToast("已经到底了")
    } else {
      this.getCategoryData()
    }
  },
  /**
   * 组件内书籍item点击事件监听
   * @param {Object} evt 点击携带的数据 evt.detail.bookId
   */
  compClickHandler(evt) {
    console.log(evt.detail)
    // COMMON_REPORT_UTILS.page_click_report(`${evt.detail.bookName}`)
    COMMON_REPORT_UTILS.list_click_report('1',[`${evt.detail.bookId}`],`${this.bookFirstCategoryData.list[this.bookFirstCategoryData.index].title}`) //点击上报
    this.bookItemClick(evt.detail.bookId)
  },
  /**
   * 书籍item点击通用事件
   */
  bookItemClick(bookId) {
    let params = {bookId,pathUrl: "分类页"}

    this.$emit('pageJumpHandle',{pageUrl:'/pagesC/Info',pageData:params})
  },
  /**
   * 书籍状态点击切换  连载  完结 ==
   * @param {Number} index 点击的下标
   */
  async statusItemClickHandler(index) {
    if (this.bookStatusData.index == index) return
    COMMON_REPORT_UTILS.page_click_report(`${this.bookStatusData.list[index].title}`)
    // this.bookFirstCategoryData.index = 0
    this.bookSecondCategoryData.index = 0
    this.bookStatusData.index = index
    await this.getCategoryData(true)
  },

  /**
   * 二级分类item点击
   * @param {Number} index 点击的下标
   */
  async secondCategoryClickHandler(index) {
    if (this.bookSecondCategoryData.index == index) return

    COMMON_REPORT_UTILS.page_click_report('分类','', `${this.bookSecondCategoryData.list[index].title}`)
    this.bookSecondCategoryData.index = index
    await this.getCategoryData()
  },
  /**
   * 一级分类item点击
   * @param {Number} index 点击的下标
   */
  async firstCategoryClickHandler(index) {
    if (this.bookFirstCategoryData.index == index) return
    COMMON_REPORT_UTILS.page_click_report('分类','', `${this.bookFirstCategoryData.list[index].title}`)
    this.bookFirstCategoryData.index = index
    this.bookStatusData.index = 0
    // await this.getSecondCategoryList()
    await this.getCategoryData(true)
  },
  onInit() {
    this.headerTabData.index = this.$app.$def.sex - 1
    this.$watch('selectIndex', 'selectIndexChangeHandler')
    this.$watch('viewShowTimes', 'viewShowTimesChangeHandler')
  },
  selectIndexChangeHandler(newVal, oldVal) {
    if (newVal == 3 && this.$app.$def.tabListType[newVal] == 0) {
      this.headerTabData.index = this.$app.$def.sex - 1
      this.getData()
      this.$app.$def.tabListType[newVal] = 1
    }
  },
  viewShowTimesChangeHandler(newVal, oldVal) {
    if (newVal == 1) return
    // if (this.selectIndex !== 3) return
    this.selectIndexChangeHandler(3, 100)
  },
  /**
   * 获取数据总函数
   */
  async getData() {
    await this.getFirstCategoryList()
    await this.getCategoryData()
  },
  /**
   * 性别tab点击
   * @param {Number} idx 下标
   */
  async sexTabClick(idx) {
    if (this.headerTabData.index == idx) return

    COMMON_REPORT_UTILS.page_click_report(`${this.headerTabData.list[idx].title}`)

    this.headerTabData.index = idx
    this.bookStatusData.index = 0
    this.bookFirstCategoryData.index = 0
    this.bookSecondCategoryData.index = 0
    let sexId = this.headerTabData.list[idx].id
    this.$app.$def.sex = sexId
    this.$app.$def.tabListType[1] = 0
    this.$app.$def.tabListType[0] = 0
    //向服务器更新性别偏好
    await $apis.example.uploadInfo({ sex: this.$app.$def.sex })
    await this.getFirstCategoryList()
    await this.getCategoryData(true)
  },
  /**
   * 获取一级分类列表
   */
  getFirstCategoryList() {
    let that = this
    return new Promise((resolve, reject) => {
      $apis.example.homecategory({
        sex: that.headerTabData.list[that.headerTabData.index].id
      }).then(res => {
        if (res.code == 200) {
          that.bookFirstCategoryData.index = 0
          that.bookFirstCategoryData.list = []
          that.bookFirstCategoryData.list = res.data.category
          resolve()
        } else {
          resolve()
        }
      }).catch(err => {
        resolve()
      })
    })

  },
  /**
   * 获取二级分类列表
   */
  getSecondCategoryList() {
    let that = this
    return new Promise((resolve, reject) => {
      $apis.example.categoryApi({
        categoryId: that.bookFirstCategoryData.list[that.bookFirstCategoryData.index].id
      }).then(res => {
        if (res.code == 200) {
          that.bookSecondCategoryData.index = 0
          that.bookSecondCategoryData.list = []
          that.bookSecondCategoryData.list = res.data
          resolve()
        } else {
          $utils.showToast('数据获取错误')
          resolve()
        }
      }).catch(err => {
        $utils.showToast('数据获取异常')
        resolve()
      })
    })
  },
  /**
   * 获取分类数据
   */
  getCategoryData(isInit = false) {
    if (isInit) {
      // 分页的page置为1
      this.bookListInfo.page = 1;
    }
    if (this.bookListInfo.page === 1) this.getDataFinished = false

    let that = this
    return new Promise((resolve, reject) => {

      that.loading = true
      $apis.example.stackRoomInfo({
        sex: that.headerTabData.list[that.headerTabData.index].id,
        page: that.bookListInfo.page,
        categoryId: that.bookFirstCategoryData.list[that.bookFirstCategoryData.index].id,
        secondCategoryId: that.bookSecondCategoryData.list[that.bookSecondCategoryData.index].id,
        status: that.bookStatusData.list[that.bookStatusData.index].id
      }).then(res => {

        that.loading = false
        if (res.code == 200) {
          that.bookListInfo.page = Number(res.data.page)
          that.bookListInfo.totalPage = res.data.totalPage
          if (that.bookListInfo.page > 1) {
            that.bookListInfo.list.push(...res.data.list)
          } else {
            that.getDataFinished = true
            that.bookListInfo.list = []
            if (res.data.list) that.bookListInfo.list = res.data.list
          }
          that.$app.$def.uploadListShow(that.bookListInfo.list,that.bookFirstCategoryData.list[that.bookFirstCategoryData.index].title)
          resolve()
        } else {
          resolve()
        }
      }).catch(err => {
        that.loading = false
        resolve()
        $utils.showToast('分类数据获取失败')
      })
    })
  },
  props: {
    selectIndex: {
      type: undefined,
      default: 3
    },
    viewShowTimes: {
      type: Number,
      default: 0
    },
    isShowAddDesktop: {
      default: false
    },
    addDesktopHeight: {
      default: 80
    },
  }
}
</script>

<style lang="less">
@import './index.less';
</style>
