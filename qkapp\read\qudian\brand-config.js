/**
 * 对接配置
 * 分为 单 APP 配置和 分厂商配置，如果两者都配置了同一个字段，则以app配置为准
 * @type {{app: {}, brand: {}}}
 */
const config = {
  // 系统数据
  app: {
    // 系统内部数据，都写在这里，之后会统一通过 $getAppConfig 查询
    // 支持dev和prod，如果一致，则不用写 dev prod，直接写一个即可
    appSecreteKey: {
      dev: '_CK7NrpE-NthZStO',
      prod: 'QE2wWLUB2RtZzvci'
    },
    middleAltLight: 'https://img.qdreads.com/image/zhanwei-1.png',
    middleAltDark: 'https://img.qdreads.com/image/zhanwei-2.png',
    imageOss: 'https://img.sytangshiwl.top/public/images/read/'
  },

  // 打包过程的配置
  build: {
    // 项目打包过程，替换APP路径，eg '/page' ''
    copyPathReplace: ['', '']
  },

  // manifest 配置
  manifest: {},

  // 分厂商配置，如果有相同字段，则以分厂商配置为准
  brand: {
    xiaomi: {
      manifest: {}
    },
    hw: {
      manifest: {}
    },
    oppo: {
      manifest: {}
    },
    vivo: {
      manifest: {}
    },
    honor: {
      manifest: {}
    }
  }
}

module.exports = config
