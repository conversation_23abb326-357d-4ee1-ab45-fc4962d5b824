{"READ_CLASSIFY": {"path": "/pagesA/Main", "pageCode": "READ_CLASSIFY", "pageName": "分类页", "param": {"selectIndex": 3}}, "READ_WELFARETT": {"path": "/pagesA/welfareTT", "pageCode": "READ_WELFARETT", "pageName": "头条福利页", "param": {}}, "READ_MINE": {"path": "/pagesA/Main", "pageCode": "READ_MINE", "pageName": "我的", "param": {"selectIndex": 4}}, "READ_BOOKINFO": {"path": "/pagesC/Info", "pageCode": "READ_BOOKINFO", "pageName": "小说详情页", "param": {}}, "READ_LOGIN": {"path": "/pagesB/Login", "pageCode": "READ_LOGIN", "pageName": "登录页", "param": {}}, "READ_H5_ACTIVITY": {"path": "/pagesC/Action", "pageCode": "READ_H5_ACTIVITY", "pageName": "活动页", "param": {}}, "ACTIVITY_H5": {"path": "/pagesC/ActivityH5", "pageCode": "ACTIVITY_H5", "pageName": "H5推广页", "param": {}}, "READ_READINFO": {"path": "/pagesC/Read", "pageCode": "READ_READINFO", "pageName": "阅读页", "param": {}}, "READ_WELFARE": {"path": "/pagesA/WelfareNew", "pageCode": "READ_WELFARE", "pageName": "福利页", "param": {}}, "READ_MAIN": {"path": "/pagesA/Main", "pageCode": "READ_MAIN", "pageName": "书城页", "param": {"selectIndex": 1}}, "READ_SHELF": {"path": "/pagesA/Main", "pageCode": "READ_SHELF", "pageName": "书架页", "param": {"selectIndex": 0}}, "READ_INVITE": {"path": "/pagesB/Activity", "pageCode": "READ_INVITE", "pageName": "邀请页", "param": {}}, "READ_SEX": {"path": "/pagesB/Sex", "pageCode": "READ_SEX", "pageName": "性别页", "param": {}}, "READ_SETTING": {"path": "/pagesB/Setting", "pageCode": "READ_SETTING", "pageName": "性别页", "param": {}}, "READ_SUBSCRIBE": {"path": "/pagesB/Subscribe", "pageCode": "READ_SUBSCRIBE", "pageName": "订阅页", "param": {}}, "READ_CUSTOMER_SERVICE": {"path": "/pagesB/Customer-Service", "pageCode": "READ_CUSTOMER_SERVICE", "pageName": "客服页", "param": {}}, "READ_SEARCH": {"path": "/pagesB/Search", "pageCode": "READ_SEARCH", "pageName": "客服页", "param": {}}, "READ_TOP": {"path": "/pagesB/Top", "pageCode": "READ_SEARCH", "pageName": "排行页", "param": {}}, "READ_START": {"path": "/pagesB/Start", "pageCode": "READ_START", "pageName": "启动页", "param": {}}}