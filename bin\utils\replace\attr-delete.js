const { astReplace } = require('./base/code-split')
const traverse = require('@babel/traverse').default

/*
 * 通过方法名，删除方法和方法后的逗号，反馈方法被调用的地方
 * @param content 文件代码
 * @param oldCode 需要替换的代码
 */

function attrDelete({ content, oldCode }) {
  const code = astReplace(content, (ast, scriptCode) => {
    const result = []
    traverse(ast, {
      ObjectProperty(path) {
        const attrList = oldCode.split('.')
        attrList.reverse()
        if (path.node.key.name === attrList[0] && checkParent(path.parentPath.parentPath, attrList.slice(1))) {
          const sibling = propertySiblingNode(path)
          result.push({
            start: path.node.start,
            end: sibling ? sibling.start : scriptCode[path.node.end] === ',' ? path.node.end + 3 : path.node.end + 3,
            type: 'remove'
          })
        }
      }
    })

    return result
  })
  return code
}

function checkParent(parentPath, list) {
  if (!list.length) {
    return true
  }

  let current = parentPath
  while (list.length) {
    const parentNode = current.node
    if (parentNode.type === 'ObjectProperty' && parentNode.key.name === list[0]) {
      list.shift()
      current = current.parentPath.parentPath
    } else {
      return false
    }
  }

  return true
}

function propertySiblingNode(path) {
  const properties = path.parentPath.node.properties
  const currentIndex = properties.indexOf(path.node)

  // 检查下一个兄弟节点是否存在
  if (currentIndex !== -1 && currentIndex < properties.length - 1) {
    return properties[currentIndex + 1]
  }

  return null
}

module.exports = attrDelete
