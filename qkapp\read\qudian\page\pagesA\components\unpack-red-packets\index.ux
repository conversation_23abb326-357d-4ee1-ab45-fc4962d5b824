<template>
  <div class="urp-wrapper">
    <image class="packets-title" src="https://img.qdreads.com/v155/welfare1.5.0/packets-title.png"></image>

    <div class="urp-main">
      <block for="{{(index,item) in packets}}">
        <block if="{{item.is_receive == -1}}">
          <image src="{{item.icon}}" @click="unpackHandle(item)"></image>
        </block>
        <block else>
          <div class="urp-item">
            <image class="urp-item-image" src="{{index == 2 ? unpackSpe : unpackDef}}"></image>
            <text class="urp-item-text"><span class="num">{{item.amount}}</span><span class="unit">元</span></text>
          </div>
        </block>
      </block>
    </div>

    <div class="urp-footer">
      <text class="urp-text-1">今日收益：</text>
      <text class="urp-text-2">{{packetsToday}}元</text>
      <text class="urp-text-3" @click="goToCashOut">去提现</text>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    packets: {
      type:Array,
      default:[]
    },
    packetsToday:{
      type: String | Number,
      default: '0'
    }
  },
  data: {
    unpackDef:'https://img.qdreads.com/image%2F2022-11-08%2Fpacket-unpacked-1.png',
    unpackSpe:'https://img.qdreads.com/image%2F2022-11-08%2Fpacket-unpacked-2.png',
  },
  onInit() {
    LOG('onInit===========================>',this.packets)
  },

  unpackHandle(item) {
    if(item.is_receive == 1) return
    if(!CLICK_UTILS.dom_click_vali_shake(`unpackHandle_${this.__id__}`,500)) return
    this.$emit('eventWatch', {eventName:'packetsClick',data:item})
  },
  goToCashOut(){
    if(!CLICK_UTILS.dom_click_vali_shake(`goToCashOut_${this.__id__}`,500)) return
    this.$emit('eventWatch', {eventName:'cashOutClick'})
  }
}
</script>

<style lang="less">
.urp-wrapper {
  flex-direction: column;
  width: 702px;
  height: 396px;
  background-color: #ffffff;
  border-radius: 20px;
  padding: 0 32px;
}

.packets-title {
  width: 402px;
  height: 30px;
  object-fit: contain;
  margin-top: 32px;
}

.urp-main {
  width: 100%;
  justify-content: space-between;
  margin-top: 32px;
}

.urp-main > image {
  width: 112px;
  height: 140px;
}

.urp-item {
  width: 112px;
  height: 140px;
  .urp-item-image {
    width: 100%;
    height: 100%;
  }
  .urp-item-text {
    width: 100%;
    height: 36px;
    font-weight: 500;
    text-align: center;
    color: #7a3c34;
    position: absolute;
    top: 20px;
    left: 0px;
    .num {
      font-size: 26px;
    }
    .unit{
    font-size: 20px;

    }
  }
}

.urp-footer {
  width: 100%;
  height: 92px;
  background-color: #f9f9f9;
  border-radius: 16px;
  align-items: center;
  margin-top: 40px;
}

.urp-text-1 {
  font-size: 28px;
  color: #666666;
  margin-left: 32px;
}

.urp-text-2 {
  font-size: 28px;
  color: #fd2802;
}

.urp-text-3 {
  margin-left: auto;
  margin-right: 14px;
  width: 152px;
  height: 64px;
  border-radius: 200px;
  background: linear-gradient(90deg, #fb8720, #f84a41);
  font-size: 28px;
  font-weight: 500;
  color: #ffffff;
  text-align: center;
}
</style>
