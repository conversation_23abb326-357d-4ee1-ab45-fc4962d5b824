/**
 * 小说和工具的类型配置
 * 注：小说请注明 appType: 'read'。工具类不用设置类型
 */
const pkgInfo = {
  // ============== 小说类 ===============
  read: {
    haiyushiguang: {
      pkg: 'com.haiyusg.home',
      name: '海屿时光',
      appType: 'read',
      company: 'kang'
    },
    jinghj: {
      pkg: 'com.jinghj.home',
      name: '镜花集',
      appType: 'read',
      company: 'kang'
    },
    taiguang: {
      pkg: 'com.mosslite.home',
      name: '苔光',
      appType: 'read',
      company: 'kang'
    },
    momo: {
      pkg: 'com.momoread.home',
      name: '嫼嫼追书',
      appType: 'read',
      company: 'kang'
    },
    qudian: {
      pkg: 'com.qudianread.home',
      name: '趣点阅读',
      appType: 'read',
      company: 'cy'
    },
    jiukan: {
      pkg: 'com.syyaztw.nrnovel',
      name: '九看小说',
      appType: 'read',
      company: 'kang'
    },
    cxyd: {
      pkg: 'com.tjjxxd.orangejoy',
      name: '橙欣悦读',
      appType: 'read',
      company: 'kang'
    },
    tyread: {
      pkg: 'com.jhwl.tyread',
      name: '天涯阅读',
      appType: 'read',
      company: 'cy'
    },
    sdread: {
      pkg: 'com.hnlas.tenread',
      name: '十点读书',
      appType: 'read',
      company: 'cy'
    },
    menghuan: {
      pkg: 'com.menghuanread.home',
      name: '梦郇书斋',
      appType: 'read',
      company: 'kang'
    },
    changxiang: {
      pkg: 'com.changxiangread.home',
      name: '畅庠书阁',
      appType: 'read',
      company: 'kang'
    },
    jiuyue: {
      pkg: 'com.jiuyue.home',
      name: '久阅',
      appType: 'read',
      company: 'kang'
    },
    ftreadapp: {
      pkg: 'com.feitianread.home',
      name: '飞天小说',
      appType: 'read',
      company: 'kang'
    },
    yunyang: {
      pkg: 'com.hnzcyb.yynovels',
      name: '云漾小说',
      appType: 'read'
    },
    ljreadapp: {
      pkg: 'com.nnjh.ljread',
      name: '璃璟阅读',
      appType: 'read',
      company: 'cy'
    },
    fusheng: {
      pkg: 'com.fushengread.home',
      name: '浮生故事',
      appType: 'read',
      company: 'kang'
    },
    xinghan: {
      pkg: 'com.xinghanread.home',
      name: '星瀚书阁',
      appType: 'read',
      company: 'kang'
    },
    lingxi: {
      pkg: 'com.soullink.home',
      name: '灵犀书苑',
      appType: 'read',
      company: 'kang'
    },
    moyuekan: {
      pkg: 'com.moyue.home',
      name: '墨悦看',
      appType: 'read',
      company: 'kang'
    },
    yiqukan: {
      pkg: 'com.yiqu.home',
      name: '逸趣看',
      appType: 'read',
      company: 'kang'
    },
    qingyingshe: {
      pkg: 'com.qingyingread.home',
      name: '轻影社',
      appType: 'read',
      company: 'kang'
    },
    yuyuan: {
      pkg: 'com.yuyuan.home',
      name: '芋圆小说',
      appType: 'read',
      company: 'kang'
    },
    jyread: {
      pkg: 'com.nnjh.jyread',
      name: '瑾瑜小说',
      appType: 'read',
      company: 'cy'
    },
    cwread: {
      pkg: 'com.cangwread.home',
      name: '苍梧篇章',
      appType: 'read',
      company: 'cy'
    },
    qwge: {
      pkg: 'com.qingwenread.home',
      name: '轻文阁',
      appType: 'read',
      company: 'kang'
    },
    moyunread: {
      pkg: 'com.moyunread.home',
      name: '陌韵',
      appType: 'read',
      company: 'kang'
    },
    zhijianread: {
      pkg: 'com.zhijianread.home',
      name: '指尖书',
      appType: 'read',
      company: 'kang'
    },
    ningguangread: {
      pkg: 'com.ningguang.home',
      name: '凝光看',
      appType: 'read',
      company: 'kang'
    },
    moranread: {
      pkg: 'com.moran.home',
      name: '墨染拾光',
      appType: 'read',
      company: 'kang'
    },
    weilanread: {
      pkg: 'com.weilan.home',
      name: '微澜看',
      appType: 'read',
      company: 'kang'
    },
    qingfread: {
      pkg: 'com.qingfread.home',
      name: '青枫书阁',
      appType: 'read',
      company: 'cy'
    },
    fengyinread: {
      pkg: 'com.fengyinread.home',
      name: '风吟书集',
      appType: 'read',
      company: 'cy'
    },
    biluoread: {
      pkg: 'com.biluo.home',
      name: '碧落看',
      appType: 'read',
      company: 'cy'
    }
  },
  // ============== 工具类 ===============
  // 答题类
  answers: {
    knowledge: {
      pkg: 'com.jiuchang.knowledge',
      name: '一智千金',
      company: 'kang'
    },
    xingzhidao: {
      pkg: 'com.xingzd.home',
      name: '星知岛',
      company: 'kang'
    },
    hdly: {
      pkg: 'com.huida.home',
      name: '慧答乐园',
      company: 'kang'
    },
    qdyx: {
      pkg: 'com.qudayixia.home',
      name: '趣答一黠',
      company: 'kang'
    },
    yadayuan: {
      pkg: 'com.yaday.home',
      name: '芽答园',
      company: 'kang'
    },
    qyd: {
      pkg: 'com.quyi.home',
      name: '趣益答',
      company: 'kang'
    },
    xiquhui: {
      pkg: 'com.ghf.opera',
      name: '戏曲荟',
      company: 'kang'
    },
    nlwx: {
      pkg: 'com.qmx.nlshow',
      name: '年轮微秀',
      company: 'kang'
    },
    qmd: {
      pkg: 'com.qumanduo',
      name: '趣喵喵',
      company: 'kang'
    },
    ljk: {
      pkg: 'com.gxnnyl.health',
      name: '乐健康'
    },
    ceping: {
      pkg: 'com.syyaztw.wjcp',
      name: '莬扢测评',
      company: 'kang'
    },
    jike: {
      pkg: 'com.jkknow.home',
      name: '吉壳答题',
      company: 'cy'
    },
    kukuda: {
      pkg: 'com.kktalk.home',
      name: '酷酷答',
      company: 'cy'
    },
    zhiya: {
      pkg: 'com.zhiyaknow.home',
      name: '知芽',
      company: 'kang'
    },
    dabady: {
      pkg: 'com.dabady.home',
      name: '答吧岛屿',
      company: 'kang'
    },
    wenxuest: {
      pkg: 'com.wenxuest.home',
      name: '问学社团',
      company: 'kang'
    },
    miaodaxw: {
      pkg: 'com.miaodaxw.home',
      name: '妙答小屋',
      company: 'kang'
    },
    huiyanyd: {
      pkg: 'com.huiyanyd.home',
      name: '茴烟一答',
      company: 'kang'
    },
    gelinnl: {
      pkg: 'com.gelinnl.home',
      name: '格林年轮',
      company: 'kang'
    },
    tuganly: {
      pkg: 'com.tuganly.home',
      name: '图感流影',
      company: 'kang'
    },
    jingxhy: {
      pkg: 'com.jingxhy.home',
      name: '镜湘绘影',
      company: 'kang'
    },
    xinjigh: {
      pkg: 'com.xinjigh.home',
      name: '心迹光绘',
      company: 'kang'
    },
    zhixiangxh: {
      pkg: 'com.zhixiangxh.home',
      name: '知象星河',
      company: 'kang'
    },
  },
  // 省电节能
  battery: {
    qujieneng: {
      pkg: 'com.qujieneng.home',
      name: '节能助手',
      company: 'kang'
    },
    xiaolu: {
      pkg: 'com.xiaolusd.home',
      name: '小鹿省电',
      company: 'cy'
    },
    shandian: {
      pkg: 'com.jieng.jng',
      name: '闪电节能',
      company: 'cy'
    },
    shengdian: {
      pkg: 'com.zywl.battery',
      name: '亟时省电',
      company: 'kang'
    },
    guanjia: {
      pkg: 'com.battery.home',
      name: '电池管家',
      company: 'kang'
    },
    ydsd: {
      pkg: 'com.dxh.ydpower',
      name: '优道省电助手',
      company: 'kang'
    },
    qiezi: {
      pkg: 'com.njnd.qzhelper',
      name: '源宝节能',
      company: 'cy'
    },
    kdsd: {
      pkg: 'com.syyaztw.battery',
      name: '敤玓省电',
      company: 'kang'
    },
    yxsd: {
      pkg: 'com.cqdxh.battery',
      name: '宎呺省电',
      company: 'kang'
    },
    xiaonengshou: {
      pkg: 'com.xiaonengshou.home',
      name: '省电小能手',
      company: 'kang'
    },
    youdian: {
      pkg: 'com.youdian.home',
      name: '优电精灵',
      company: 'kang'
    },
    jieguangcang: {
      pkg: 'com.lightvault.home',
      name: '节光仓',
      company: 'cy'
    },
    lingdong: {
      pkg: 'com.lingdjn.home',
      name: '灵动节能',
      company: 'kang'
    },
    qsjieneng: {
      pkg: 'com.qsjieneng.home',
      name: '轻松节能',
      company: 'kang'
    },
    qsjieyuan: {
      pkg: 'com.easyjieneng.home',
      name: '轻松节源',
      company: 'kang'
    },
    jielvqi: {
      pkg: 'com.powertune.home',
      name: '节律器',
      company: 'cy'
    },
    lingyuanqi: {
      pkg: 'com.voltcore.home',
      name: '灵源器',
      company: 'cy'
    },
    zhidian: {
      pkg: 'com.zhidian.home',
      name: '智电灵',
      company: 'kang'
    },
    zdzs: {
      pkg: 'com.zdzhushou.home',
      name: '智电助手',
      company: 'kang'
    },
    dianbaobao: {
      pkg: 'com.dianbao.home',
      name: '电保宝',
      company: 'kang'
    },
    youdianhudun: {
      pkg: 'com.ydhudun.home',
      name: '优电护盾',
      company: 'kang'
    },
    shengxinmiao: {
      pkg: 'com.shengxin.home',
      name: '省芯喵',
      company: 'kang'
    },
    qingnengjiedian: {
      pkg: 'com.qingneng.home',
      name: '轻能节电',
      company: 'kang'
    },
    jieyuan: {
      pkg: 'com.jieyuan.home',
      name: '节源卫士',
      company: 'kang'
    },
    yinghuoliu: {
      pkg: 'com.yinghuo.home',
      name: '萤火流',
      company: 'kang'
    },
    lvjingneng: {
      pkg: 'com.lvjingn.home',
      name: '绿鲸能',
      company: 'kang'
    },
    nengfanghe: {
      pkg: 'com.nengcube.home',
      name: '能方盒',
      company: 'cy'
    },
    jieyuanhe: {
      pkg: 'com.sourcebox.home',
      name: '节源盒',
      company: 'cy'
    },
    xueyy: {
      pkg: 'com.xueyy.home',
      name: '雪鸢源',
      company: 'kang'
    },
    lingqd: {
      pkg: 'com.lingqd.home',
      name: '灵雀电',
      company: 'kang'
    },
    shengnengxia: {
      pkg: 'com.ecocell.home',
      name: '省能匣',
      company: 'cy'
    },
    qiguangj: {
      pkg: 'com.qiguangj.home',
      name: '绮光居',
      company: 'kang'
    },
    yungugangxuan: {
      pkg: 'com.yunlingt.home',
      name: '芸光轩',
      company: 'kang'
    },
    qingdiange: {
      pkg: 'com.qinglightg.home',
      name: '清电阁',
      company: 'kang'
    },
    cuijieneng: {
      pkg: 'com.cuilightn.home',
      name: '萃节能',
      company: 'kang'
    },
    sunengfang: {
      pkg: 'com.fastlightf.home',
      name: '速能坊',
      company: 'kang'
    },
  },
  // 壁纸
  wallpaper: {
    qudingdang: {
      pkg: 'com.qudingdang.home',
      name: '趣叮铛',
      company: 'kang'
    },
    lutingbz: {
      pkg: 'com.ltbizhi.home',
      name: '鹿汀壁纸',
      company: 'cy'
    },
    yinghe: {
      pkg: 'com.yhbizhi.home',
      name: '樱鹤壁纸',
      company: 'cy'
    },
    mengtu: {
      pkg: 'com.mengtuxiaozhu.home',
      name: '甍图小筑',
      company: 'kang'
    },
    tangdou: {
      pkg: 'com.zywl.tdwallpaper',
      name: '闿廒壁纸',
      company: 'kang'
    },
    bzdq: {
      pkg: 'com.njnd.wallpaper',
      name: '壁纸大全',
      company: 'cy'
    },
    xuechameihua: {
      pkg: 'com.xcmeih.home',
      name: '雪茶梅画',
      company: 'cy'
    },
    caomei: {
      pkg: 'com.ltc.cmwallpaper',
      name: '草莓高清壁纸'
    },
    qlx: {
      pkg: 'com.tykj.qywallper',
      name: '轻乐秀',
      company: 'kang'
    },
    qimeng: {
      pkg: 'com.qimeng.home',
      name: '绮梦壁语',
      company: 'kang'
    },
    xingqiong: {
      pkg: 'com.xingqiong.home',
      name: '星穹壁纸',
      company: 'kang'
    },
    liuyibizhi: {
      pkg: 'com.liuyibz.home',
      name: '流逸壁纸',
      company: 'kang'
    },
    xinglianmeng: {
      pkg: 'com.xinglianm.home',
      name: '星莲梦',
      company: 'kang'
    },
    slbihua: {
      pkg: 'com.songlinbh.home',
      name: '松林壁画',
      company: 'kang'
    },
    baiguangbz: {
      pkg: 'com.baiguangbz.home',
      name: '柏光壁纸',
      company: 'kang'
    },
    nuanxingbz: {
      pkg: 'com.nuanxingbz.home',
      name: '暖杏壁纸',
      company: 'kang'
    },
    qingdaibh: {
      pkg: 'com.qingdaibh.home',
      name: '青黛壁画',
      company: 'kang'
    },
    mufenbh: {
      pkg: 'com.mufenbh.home',
      name: '暮粉壁画',
      company: 'kang'
    },
    zhizhibz: {
      pkg: 'com.zhizhibz.home',
      name: '栀栀壁纸',
      company: 'kang'
    }

  },
  // 购物
  shopping: {
    haoshi: {
      pkg: 'com.haoshiduo.home',
      name: '好市多',
      company: 'kang'
    },
    tiantian: {
      pkg: 'com.dairyshopping.home',
      name: '立刻省',
      company: 'cy'
    },
    baobao: {
      pkg: 'com.bargain.tianyaoguan',
      name: '宝宝砍价',
      company: 'cy'
    },
    qsg: {
      pkg: 'com.ztxs.qsshop',
      name: '瑢燚购物',
      company: 'cy'
    },
    rabbit: {
      pkg: 'com.kang.home',
      name: '兔子领券',
      company: 'kang'
    },
    letaohui: {
      pkg: 'com.letaohui.home',
      name: '乐淘惠',
      company: 'kang'
    },
    wool: {
      pkg: 'com.wool.home',
      name: '羊毛优品',
      company: 'kang'
    },
    taotaoquan: {
      pkg: 'com.taotaoquan.home',
      name: '淘淘好券',
      company: 'kang'
    },
    duotaotao: {
      pkg: 'com.taotaoquan.home',
      name: '多淘淘',
      company: 'cy'
    },
    hwd: {
      pkg: 'com.haowd.hwd',
      name: '好物多',
      company: 'cy'
    },
    smshop: {
      pkg: 'com.xaty.smshop',
      name: '殳忟惠购',
      company: 'kang'
    },
    ysg: {
      pkg: 'com.yishenggou.home',
      name: '易省购',
      company: 'kang'
    },
    hkg: {
      pkg: 'com.haokg.hkg',
      name: '好快购',
      company: 'kang'
    },
    quanlexiang: {
      pkg: 'com.quanlexiang.home',
      name: '券乐享',
      company: 'kang'
    },
    shangoumiao: {
      pkg: 'com.shangou.home',
      name: '闪购喵',
      company: 'kang'
    },
    huitaohui: {
      pkg: 'com.bargainbay.home',
      name: '惠淘汇',
      company: 'cy'
    },
    jibaocang: {
      pkg: 'com.jibao.home',
      name: '集宝仓',
      company: 'kang'
    },
    senwu: {
      pkg: 'com.senwu.home',
      name: '森物集',
      company: 'cy'
    },
    shenggoucheng: {
      pkg: 'com.savetown.home',
      name: '省购城',
      company: 'cy'
    },
    legoubaoku: {
      pkg: 'com.legou.home',
      name: '乐购宝库',
      company: 'kang'
    },
    ltlq: {
      pkg: 'com.letaolingquan.home',
      name: '乐淘领券',
      company: 'kang'
    },
    mupinjie: {
      pkg: 'com.mochalane.home',
      name: '沐品街',
      company: 'cy'
    },
    yxzhp: {
      pkg: 'com.youxiang.home',
      name: '优享杂货铺',
      company: 'kang'
    },
    yuexuanpu: {
      pkg: 'com.yuexuan.home',
      name: '悦选铺',
      company: 'kang'
    },
    yunshangji: {
      pkg: 'com.ysji.home',
      name: '云尚集',
      company: 'cy'
    },
    baopincang: {
      pkg: 'com.boomdeals.home',
      name: '爆品仓',
      company: 'cy'
    },
    zwpu: {
      pkg: 'com.zwpu.home',
      name: '知物铺',
      company: 'kang'
    },
    youhuipu: {
      pkg: 'com.yhpu.home',
      name: '优荟铺',
      company: 'kang'
    },
    miaoqiangjie: {
      pkg: 'com.buystreet.home',
      name: '秒抢街',
      company: 'cy'
    },
    suxuan: {
      pkg: 'com.suxuan.home',
      name: '素选仓',
      company: 'kang'
    },
    quhuomt: {
      pkg: 'com.quhuo.home',
      name: '趣货码头',
      company: 'kang'
    },
    jixuan: {
      pkg: 'com.jixuan.home',
      name: '极选城',
      company: 'kang'
    }
  },
  // 短剧
  playlet: {
    qukankan: {
      pkg: 'com.qukankan.home',
      name: '趣看看',
      company: 'kang'
    },
    jushuang: {
      pkg: 'com.jushuang.home',
      name: '剧爽',
      company: 'kang'
    },
    shuyingge: {
      pkg: 'com.yuchengna.syg',
      name: '书影阁',
      company: 'cy'
    },
    wenchangge: {
      pkg: 'com.tjjxxd.wcgshort',
      name: '莬昶阁',
      company: 'kang'
    },
    diyuge: {
      pkg: 'com.hnzcyb.kygshort',
      name: '玓鹬阁'
    },
    youlekan: {
      pkg: 'com.youlekan.home',
      name: '优乐看',
      company: 'kang'
    },
    shiguang: {
      pkg: 'com.shiguanglekan.home',
      name: '时光乐看',
      company: 'kang'
    },
    juhai: {
      pkg: 'com.jh.juhai',
      name: '剧嗨',
      company: 'cy'
    }
  },
  // 漫画
  cartoon: {
    jingyue: {
      pkg: 'com.jingyueread.home',
      name: '鲸阅',
      company: 'kang'
    },
    maoyan: {
      pkg: 'com.maoyanread.home',
      name: '猫沇漫画',
      company: 'kang'
    },
    akmanhua: {
      pkg: 'com.jhwl.akcomic',
      name: '爱看漫画',
      company: 'cy'
    }
  },
  // 百科
  cyclopedia: {
    qcf: {
      pkg: 'com.funkitchen.home',
      name: '趣厨房',
      company: 'kang'
    },
    youlek: {
      pkg: 'com.xxqmx.ylkys',
      name: '悠乐康',
      company: 'kang'
    },
    chufangsg: {
      pkg: 'com.chufangsg.home',
      name: '厨坊食光',
      company: 'kang'
    },
    shuqup: {
      pkg: 'com.shuqup.home',
      name: '薯趣铺',
      company: 'kang'
    }
  },
  // 农场
  farm: {
    ccfarm: {
      pkg: 'com.jgy.ccfarm',
      name: '菜菜农场',
      company: 'cy'
    },
    ssfarm: {
      pkg: 'com.caicai.home',
      name: '偲偲农场',
      company: 'kang'
    },
    yuncaicai: {
      pkg: 'com.yuncc.ycc',
      name: '云菜菜',
      company: 'cy'
    },
    guoduoduo: {
      pkg: 'com.hnltw.guodd',
      name: '果多多'
    },
    cayuansp: {
      pkg: 'com.cayuansp.home',
      name: '菜园食谱',
      company: 'kang'
    },
    maitianj: {
      pkg: 'com.maitianj.home',
      name: '麦田记',
      company: 'kang'
    },
    shenchuxf: {
      pkg: 'com.shenchuxf.home',
      name: '神厨小福',
      company: 'kang'
    },

  },
  // 宠物日记
  petShopping: {
    miaowu: {
      pkg: 'com.gmkj.mwpet',
      name: '喵呜星球',
      company: 'kang'
    },
    mengbao: {
      pkg: 'com.ghfkj.mbpet',
      name: '萌宝联盟',
      company: 'kang'
    },
    qumiaowu: {
      pkg: 'com.qumiaowu.home',
      name: '趣喵呜',
      company: 'kang'
    },
    sanqi: {
      pkg: 'com.yhyq.sqpet',
      name: '三七萌宠',
      company: 'cy'
    }
  },
  // 走步
  exercise: {
    kbz: {
      pkg: 'com.tyg.quickwalk',
      name: '快步走',
      company: 'cy'
    },
    xiongmao: {
      pkg: 'com.xiongmao.home',
      name: '芎猫来了',
      company: 'kang'
    },
    qingsong: {
      pkg: 'com.zywl.qswalk',
      name: '轻松记步',
      company: 'kang'
    },
    yunsmb: {
      pkg: 'com.yunsmb.home',
      name: '云上漫步',
      company: 'cy'
    },
    yunxie: {
      pkg: 'com.yxwalk.home',
      name: '云鞋记步',
      company: 'cy'
    },
    qingying: {
      pkg: 'com.qingying.home',
      name: '轻盈漫步',
      company: 'kang'
    },
    qubu: {
      pkg: 'com.qubu.home',
      name: '趣步轻扬',
      company: 'kang'
    },
    tiantian: {
      pkg: 'com.szzy.ttianzoubu',
      name: '天天走步',
      company: 'cy'
    },
    huali: {
      pkg: 'com.hlwalk.home',
      name: '花狸走步',
      company: 'cy'
    },
    lsjl: {
      pkg: 'com.njyl.lswalk',
      name: '林深见鹿'
    },
    bubusheng: {
      pkg: 'com.walkingup.bbs',
      name: '步步升',
      company: 'cy'
    },
    zhuyingxing: {
      pkg: 'com.zhuyingx.home',
      name: '竹影行',
      company: 'kang'
    },
    chengfeng: {
      pkg: 'com.chengfwalk.home',
      name: '橙风走步',
      company: 'kang'
    },
    bubulian: {
      pkg: 'com.bblwalk.home',
      name: '步步莲',
      company: 'cy'
    },
    youxingg: {
      pkg: 'com.youxingg.home',
      name: '悠行馆',
      company: 'kang'
    },
    meiriwalk: {
      pkg: 'com.meiriwalk.home',
      name: '每日行走',
      company: 'kang'
    },
    jianbujl: {
      pkg: 'com.jianbujl.home',
      name: '健步记录',
      company: 'kang'
    },
    qingkuaix: {
      pkg: 'com.qingkuaix.home',
      name: '轻快行',
      company: 'kang'
    },
    kuairun: {
      pkg: 'com.kuairun.home',
      name: '酷爱跑',
      company: 'kang'
    },
  }
}

module.exports = pkgInfo
