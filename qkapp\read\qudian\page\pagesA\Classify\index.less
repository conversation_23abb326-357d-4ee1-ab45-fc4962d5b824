.classify-wrapper {
    height: 100%;
    width: 750px;
    flex-direction: column;
    background-color: #f5f5f5;

    .classify-header {
        width: 750px;
        height: 100px;
        padding: 30px 30px 0;
        .tab-container {
            flex: 1;
            .tab-item {
                width: 82px;
                margin-right: 44px;
                align-items: center; 
                flex-direction: column;

                .tab-title-wrapper {
                    height: 40px;
                    width: 82px;
                    flex-direction: column-reverse;
                    align-items: flex-start;
                }

                .tab-title-normal {
                    text-align: center;
                    font-size: 32px;
                    font-weight: 400;
                    color:  #666666;
                    height: 32px;
                }
                .tab-title-select {
                    width: 82px;
                    text-align: center;
                    font-size: 40px;
                    font-weight: bold;
                    color: #333333;
                    height: 40px;
                }
            }
        }
    }

}

.empty-container {
    width: 100%;
    height: 100%;
    justify-content: center;
    align-items: center;
    .empty-image {
        width: 348px;
        height: 348px;
    }
}

.male-selected-img {
    width: 90px;
    height: 44px;
    object-fit: contain;
}

.male-img {
    width: 66px;
    height: 34px;
    margin-top: 10px;
    object-fit: contain;
}

.classify-left-content {
    width: 140px;
    height: 100%;

    .first-classify-item {
        width: 100%;
        height: 110px;
        align-items: center;
        justify-content: center;

        .first-classify-text-item {
            height: 58px;
            font-size: 26px;
            color: #333333;
            text-align: center;
        }
        .first-classify-text-active {
            color: #f11212;
            font-weight: bold;
        }
    }

    .first-classify-margin {
        margin-bottom: 30px;
    }
}

.classify-status-list {
    margin: 30px 40px;
}

.status-item-text {
    width: 148px;
    height: 58px;
    font-size: 26px;
    background-color: #f8f8f9;
    color: #666666;
    border-radius: 30px;
    text-align: center;
}

.status-item-active {
    color: #f11212;
    background-color: #ffeded;
    font-weight: bold;
}

.classify-right-content {
    flex: 1;
    height: 100%;
    flex-direction: column;

    .classify-common-title {
        height: 64px;
        padding: 15px 0 24px;
        font-size: 24px;
        color: #999999;
    }
    .second-classify-list {
        flex-wrap: wrap;
        justify-content: flex-start;
        .second-classify-item {
            height: 56px;
            margin-bottom: 24px;
            margin-right: 24px;
            .second-classify-text-item {
                text-align: center;
                background-color:  #f9f9f9;
                color: #000000;
                height: 56px;
                padding: 0 32px;
                border-radius: 28px;
                font-size: 24px;
            }
            .second-classify-text-active {
                background-color: #f9f6ef;
                color:  #c0a56f;
                font-weight: bold;
            }
        }
    }
    .book-list {
        flex: 1;
        padding: 0 30px 0 23px;
        
        .book-item {
            margin-top: 40px;
        }
    }
}