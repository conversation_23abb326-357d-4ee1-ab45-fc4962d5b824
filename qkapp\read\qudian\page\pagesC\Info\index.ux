<template>
  <!-- template里只能有一个根节点 -->
  <div class="detail-wrapper">
    <div class="detail-top-bg"></div>
    <div class="flex-column" style="align-items: center">
      <div>
        <common-back-header back-icon-show="{{false}}"></common-back-header>
      </div>

      <div class="detail-info-wrapper">
        <image
          src="{{bookInfo && bookInfo.bookIconUrl}}"
          style="
            width: 188px;
            height: 268px;
            border-radius: 10px;
            margin-right: 30px;
          "
        ></image>
        <div class="dt-container">
          <text class="dt-name {{bookInfo ? '' : 'gray-bg'}}">{{
            bookInfo ? bookInfo.bookName : '                           '
          }}</text>
          <text class="dt-author {{bookInfo ? '' : 'gray-bg'}}">{{
            bookInfo ? bookInfo.bookAuthor : '                '
          }}</text>
          <text class="dt-tag">{{
            bookInfo
              ? bookInfo.finish +
                '·' +
                Math.floor(bookInfo.wordNum / 10000) +
                '万字'
              : '                '
          }}</text>
        </div>
      </div>
      <div class="dt-rank">
        <image
          class="dt-rank-icon"
          src="https://img.qdreads.com/v163/<EMAIL>"
        ></image>
        <text class="dt-rank-text">热门好书排行榜</text>
        <text class="dt-rank-arrow"
          >{{ bookInfo ? bookInfo.rankNum : '  ' }}名</text
        >
      </div>
      <div
        style="
          border-top-left-radius: 24px;
          border-top-right-radius: 24px;
          background-color: #ffffff;
          padding: 0 30px;
          width: 750px;
        "
        class="flex-column"
      >
        <div class="detail-bottom-count-content">
          <div class="count-item">
            <div>
              <text class="novel-score" style="margin-right: 6px">{{
                bookInfo && bookInfo.score
              }}</text>
              <block for="{{(index, item) in starArr}}">
                <image
                  if="{{bookInfo && (bookInfo.score / 2) >= (index + 1)}}"
                  class="detail-bottom-star"
                  src="https://img.qdreads.com/v163/<EMAIL>"
                ></image>
                <image
                  else
                  class="detail-bottom-star"
                  src="https://img.qdreads.com/v163/<EMAIL>"
                ></image>
              </block>
            </div>
            <text class="novel-score-desc">评分</text>
          </div>
          <div
            style="width: 2px; height: 70px; background-color: #e9e9e9"
          ></div>
          <div class="count-item" style="align-items: center">
            <div class="flex-column">
              <text class="novel-score">
                {{ bookInfo && bookInfo.bookNum | readNumHandle }}+
              </text>
              <text class="novel-score-desc">已阅读</text>
            </div>
          </div>
        </div>

        <div class="desc-contents">
          <text class="book-info-title">书籍简介</text>
        </div>
        <text class="desc-text {{bookInfo ? '' : 'skeleton'}}">{{
          bookInfo && bookInfo.bookContenUrl
        }}</text>
        <div if="{{bookInfo && bookInfo.bookType}}">
          <text
            style="
              height: 56px;
              font-size: 26px;
              background-color: #f6f6f6;
              border-radius: 14px;
              padding: 20px;
              margin-top: 20px;
            "
            >{{ bookInfo.bookType }}</text
          >
        </div>
        <div class="splice-line"></div>
        <div class="contents-wrapper" @click="catalogueClick2Read">
          <text class="book-info-check-cate">目录</text>
          <block if="{{bookInfo}}">
            <text
              style="
                height: 30px;
                font-size: 28px;
                color: #999999;
                lines: 1;
                text-overflow: ellipsis;
                margin-left: 10px;
                width: 180px;
              "
              >{{ bookInfo.lastChapter.title }}</text
            >
            <text
              style="
                width: 56px;
                height: 30px;
                background-color: #ffe3e3;
                border-radius: 5px;
                color: #ff1414;
                font-size: 20px;
                margin-left: 10px;
                text-align: center;
              "
              >最新</text
            >
          </block>
          <text class="contents-more"
            >{{ bookInfo && bookInfo.lastChapter.updateTime }}更新</text
          >
          <image
            class="contents-more-icon"
            src="https://img.qdreads.com/v163/<EMAIL>"
          ></image>
        </div>
      </div>
      <div class="detail-line-div"></div>
      
      <div class="dt-main flex-column">
        <block if="{{bookInfo}}">
          <text
            style="
              height: 36px;
              font-size: 36px;
              color: #333333;
              font-weight: bold;
              margin-top: 40px;
              margin-left: 30px;
            "
            >快速阅读</text
          >
          <div class="detail-content">
            <text class="novel-chapter-title">{{
              bookInfo.content.title
            }}</text>
            <text
              class="novel-chapter-content {{isChapterContentMask ? 'lines-12' : ''}}"
              >{{ bookInfo.content.cpContent | formatContent }}</text
            >
            <div
              class="novel-chapter-mask"
              if="{{isChapterContentMask}}"
              @click="startReadBtnClickHandler('goon')"
            >
              <div
                style="
                  margin-top: 90px;
                  width: 345px;
                  height: 80px;
                  border: 1px solid #d8d8d8;
                  border-radius: 40px;
                  justify-content: center;
                  align-items: center;
                "
              >
                <text style="height: 30px; font-size: 30px; color: #f11212"
                  >抢先阅读本章节</text
                >
                <image
                  style="margin-top: 2px"
                  src="https://img.qdreads.com/v163/<EMAIL>"
                ></image>
              </div>
            </div>
          </div>
        </block>
        <block else>
          <div
            style="
              background-color: #efefef;
              height: 265px;
              width: 690px;
              margin: 30px;
            "
          ></div>
        </block>
        <div class="detail-line-div"></div>
        <recommend-novel-item
          book-id="{{bookId}}"
          onrecommend-action="recommendActionClick"
        ></recommend-novel-item>
        <div class="detail-empty-div"></div>
      </div>
      <div if="{{bookInfo}}" class="info-bottom-wrapper">
        <div
          class="flex-column"
          style="
            height: 96px;
            width: 345px;
            align-items: center;
            justify-content: center;
          "
          @click="addShelf"
        >
          <image
            class="info-bottom-image"
            if="{{bookInfo.is_shelf}}"
            src="https://img.qdreads.com/v163/<EMAIL>"
          ></image>
          <image
            class="info-bottom-image"
            else
            src="https://img.qdreads.com/v163/<EMAIL>"
          ></image>
          <text
            style="margin-top:6px;font-size: 24px;height: 24px;font-weight:bold;color:{{bookInfo.is_shelf ? '#999999': '#333333'}}"
            >{{ bookInfo.is_shelf ? '已加书架' : '加入书架' }}</text
          >
        </div>
        <text class="info-bottom" @click="startReadBtnClickHandler"
          >开始阅读</text
        >
      </div>
    </div>
    <div
      style="background-color:rgba(255, 255, 255, {{bgOpacity}});position:fixed;top:0;left:0;"
    >
      <common-back-header onback-click="backClickHandler"></common-back-header>
    </div>
    <book-catelogue
      if="{{catalogueConfig.show}}"
      catalogue-data="{{catalogueConfig.curData}}"
      current-id="{{bookInfo.content.chapterId}}"
      book-name="{{bookInfo.content.bookName}}"
      current-index="{{bookInfo.content.index}}"
      page="{{catalogueConfig.page}}"
      total-page="{{catalogueConfig.totalPage}}"
      view-scroll-type="{{catalogueConfig.viewScrollType}}"
      oncomp-click="catalogueClickHandler"
    ></book-catelogue>
    <block if="{{customToastShow}}">
      <my-toast toast-text="{{customToastText}}"></my-toast>
    </block>
  </div>
</template>
<import name="common-back-header" src="../../components/common-back-header/index"></import>
<import name="book-catelogue" src="../components/book-catalogue-info/index.ux"></import>
<import name="recommend-novel-item" src="../components/recommend-novel-item/index.ux"></import>
<import name="my-toast" src="../../components/my-toast"></import>

<script>
export default pageMixin({
  private: {
    loading: false,
    bookInfo: '',
    isChapterContentMask: true,//是否章节内容遮罩
    isJump: false,
    pageDetail: {
      pageRoute: '/pagesC/Info',
      pageType: '二级页面',
      pageName: '小说详情页',
      pageUrl: '小说详情页',
      pageCode: 'READ_BOOKINFO',
      pageOrigin: ''
    },
    catalogueConfig: { //目录配置表
      show: false,
      page: 1,
      curData: [],
      allData: {},
      viewScrollType: 'open',
      totalPage: 1,
      minPage: 1,
      maxPage: 1,
      scrollTimes: 1
    },
  },
  public: {
    bookId: 119,
    pathUrl: '',
    intent: 0,
    isAutoPublic: false,//是否自动订阅
    listScrollY: 0,
    bgOpacity: 0,
    customToastText: '',
    customToastShow: false,
    starArr: ['', '', '', '', ''], // 评分星星数组
  },
  async onInit() {
    let that = this
    //更新页面来源
    //页面展示上报
    that.getBookInfo()
  },
  /**
   * 章节遮罩按钮点击
   */
  contentMaskClickHandler() {
    COMMON_REPORT_UTILS.page_click_report('继续阅读', '', `${this.bookId}`, `${this.bookInfo.content.chapterId}`) //点击上报
    this.isChapterContentMask = false
  },
  /**
   * 获取书籍详情
   */
  getBookInfo() {
    let that = this
    that.loading = true
    $apis.example.bookDetails({
      bookId: this.bookId
    }).then(res => {
      that.loading = false
      if (res.code == 200) {
        that.bookInfo = res.data
      }
    }).catch(err => {
      that.loading = false
    })
  },
  /**
   * 开始阅读按钮点击
   */
  startReadBtnClickHandler(btn) {
    COMMON_REPORT_UTILS.page_click_report('开始阅读', '', `${this.bookId}`, `${this.bookInfo.content.chapterId}`) //点击上报
    let params = {
      bookId: this.bookInfo.bookId,
      chapterId: this.bookInfo.content.chapterId,
      pathUrl: "小说详情页"
    }
    this.isJump = true
    $utils.routetheUrl('/pagesC/Read', params, false)
  },
  /**
   * 跳转到排行榜
   */
  novelRankItemClickHandler() {
    this.isJump = true
    COMMON_REPORT_UTILS.page_click_report('榜单', '', `${this.bookId}`, `${this.bookInfo.content.chapterId}`) //点击上报
    $utils.routetheUrl('/pagesB/Top', { pathUrl: "小说详情" }, false)
  },
  async onShow() {
    const that = this
    if (that.isJump) {
      that.isJump = false
      
      that.getBookInfo()
      that.catalogueConfig = { //目录配置表
        show: false,
        page: 1,
        curData: [],
        allData: {},
        viewScrollType: 'open',
        totalPage: 1,
        minPage: 1,
        maxPage: 1,
        scrollTimes: 1
      },
      $apis.example.subscribeCheckApi({ book_id: that.bookId }).then(res => {
        if (res.code == 200) {
          that.isAutoPublic = res.data == 1 ? true : false
        }
      })
    }
    
  },
  onHide() {
    
  },
  onPageScroll({scrollTop}) {
    this.bgOpacity = Math.min(scrollTop / 300, 1)
  },
  formatContent(content) {
    if (!content) return ''
    return content.replace('\n\n', '').replace(/\n\n/g, '\n').split(/#@&\d+&@#/g).join('')
  },

  /**
   * 目录按钮点击
   */
  async catalogueClick2Read() {
    this.catalogueConfig.page = Math.ceil((this.bookInfo.content.index + 1) / 100)
    this.catalogueConfig.curData = []
    await this.setCatalogueConfig(this.catalogueConfig.page, 'open')
    this.catalogueTypeChange()
  },
  /**
   * 获取目录弹窗组件所需的数据
   * @param {Number} page 目录当前页数
   * @param {String} type 获取的数据如何放置  open 目录打开 push 滚动到下一页 pop 滚动到上一页
   */
  async setCatalogueConfig(page, type = 'push') {
    let that = this
    switch (type) {
      case 'push':
        page = Number(page) + 1
        break
      case 'pop':
        page = Number(page) - 1
        break
      default:
        page = Number(page)
        break
    }
    if ((page < 1 || page > this.catalogueConfig.totalPage) && type !== 'open') return
    let catalogData = await that.getCatalogInfo(that.bookId, page)
    if (catalogData) {
      switch (type) {
        case 'pop':
          that.catalogueConfig.curData = catalogData.concat(that.catalogueConfig.curData)
          that.catalogueConfig.minPage = page
          break
        case 'push':
          that.catalogueConfig.curData = that.catalogueConfig.curData.concat(catalogData)
          that.catalogueConfig.maxPage = page
          break
        default:
          that.catalogueConfig.curData = that.catalogueConfig.curData.concat(catalogData)
          that.catalogueConfig.maxPage = page
          that.catalogueConfig.minPage = page
          break
      }
      that.catalogueConfig.page = page
      that.catalogueConfig.scrollTimes++
      that.catalogueConfig.viewScrollType = `${type}_${that.catalogueConfig.scrollTimes}`
    } else {
      $utils.showToast("目录数据获取失败，请稍后重试")
    }
  },

  /**
 * 目录组件监听回调
 * @param {Object} evt 事件回到 evt.detail.name = chapterClick | scrollBottom | empty （name==chapterClick has evt.detail.chapterId）
 */
  catalogueClickHandler(evt) {
    let that = this
    switch (evt.detail.name) {
      case 'chapterClick':
        that.catalogueTypeChange()
        let params = {
          bookId: this.bookInfo.bookId,
          chapterId: evt.detail.curItem.chapterId,
          pathUrl: "小说详情页"
        }
        if (evt.detail.curItem.lock) {
          that.getReadInfo(evt.detail.curItem.chapterId, '')
        } else {
          this.isJump = true
          $utils.routetheUrl('/pagesC/Read', params, false)
        }
        break
      case 'empty':
        that.catalogueTypeChange()
        break
      case 'scrollBottom':
        that.setCatalogueConfig(that.catalogueConfig.maxPage, 'push')
        break
      case 'scrollTop':
        that.setCatalogueConfig(that.catalogueConfig.minPage, 'pop')
        break
    }
  },
  /**
   * 目录组件状态切换
   */
  catalogueTypeChange() {
    this.catalogueConfig.show = !this.catalogueConfig.show
    this.viewType = 0
  },
  /**
 * 获取章节信息
 * @param {Number} bookId 书籍Id
 * @param {Number} page 页数 min=1
 */
  getCatalogInfo(bookId, page) {
    let that = this
    return new Promise((resolve, reject) => {
      let curData = null
      curData = that.catalogueConfig.allData[`page_${page}`]
      if (curData) {
        resolve(curData)
      } else {
        $apis.example.getCatalog({ page, bookId }).then(res => {
          if (res.code == 200) {
            that.catalogueConfig.allData[`page_${page}`] = res.data
            that.catalogueConfig.totalPage = res.totalPage
            resolve(res.data)
          } else {
            resolve()
          }
        }).catch(err => {
          resolve()
        })
      }

    })
  },

  /**
   * 小说订阅
   * @param {Boolean} isSubscribe 是否自动订阅 true false
   */
  bookSubscribe(isSubscribe) {
    if (isSubscribe) {
      // 开启订阅
      $apis.example.subscribeAddApi({ book_id: this.bookId }).then(res => {
        this.isAutoPublic = true
      })
    } else {
      // 关闭订阅
      $apis.example.subscribeCancelApi({ book_id: this.bookId }).then(res => {
        this.isAutoPublic = false
      })
    }
  },
  /**
   * 获取阅读页数据
   * @param {String} chapterId 章节id
   */
  async getReadInfo(chapterId, requestScene) {
    let that = this
    chapterId = Number(chapterId)
    // 如果缓存有从缓存取
    let curBookInfo
    //获取书籍基础信息
    curBookInfo = await that.getChapterDetail(chapterId)

    // 判断是否可以看下一章
    let { isEnough, userCoin, needCoin, userCoupon } = curBookInfo.content.deductTips
    if (needCoin) {
      if (requestScene !== 'purchase') {
        if (!that.isAutoPublic || !isEnough) {
          
          return
        }
      }

      // 扣费,
      let res = await that.processChapterDeduction(chapterId)
      if (!res) {
        return
      } else {
        curBookInfo.content.deductTips.needCoin = 0
      }
    }
    let params = {
      bookId: this.bookInfo.bookId,
      chapterId: chapterId,
      pathUrl: "小说详情页"
    }
    this.isJump = true
    $utils.routetheUrl('/pagesC/Read', params, false)
  },
  /**
    * 获取章节信息
    * @param {String} chapterId 章节id
    */
  getChapterDetail(chapterId) {
    let that = this
    return new Promise((resolve, reject) => {
      // that.isRequestValue = true
      $apis.example.getChapterContentV3({
        bookId: that.bookId,
        chapterId: chapterId,
        intent: that.intent,
      }).then(res => {
        switch (Number(res.code)) {
          case 200:
            resolve(res.data)
            break
          case 409:
            $utils.showToast('内容获取异常，请重试')
            that.isJump = true
            $utils.routeReplacetheUrl('/pagesA/Main', { pathUrl: "小说详情页", isNeedLogin: true })
            resolve()
            break
          default:
            $utils.showToast(res.msg)
            resolve()
            break
        }
      }).catch(errs => {
        $utils.showToast('获取章节内容异常,请重新选择小说 跳转至首页')
        resolve()
      })
    })
  },
  /**
    * 章节扣费
    * @param {Number} chapterId 章节id
    */
  processChapterDeduction(chapterId) {
    let that = this
    return new Promise((resolve, reject) => {
      $apis.example.chapterDeductionCoin({
        bookId: that.bookId,
        chapterId: chapterId,
        intent: that.intent,
      }).then(res => {
        switch (Number(res.code)) {
          case 200:
            resolve(true)
            break
          default:
            $utils.showToast(res.msg)
            resolve()
            break
        }
      }).catch(errs => {
        $utils.showToast(errs)
        resolve()
      })
    })
  },

  onBackPress() {
    this.goBackView()
    return true
  },
  backClickHandler() {
    LOG('点击了返回按钮')
    COMMON_REPORT_UTILS.page_click_report('返回', '', `${this.bookId}`) //点击上报
    this.goBackView()
  },
  /**
   * 返回
   * @param {Boolean} isNeedReportBackPress 是否上报物理返回 默认false
   */
  async goBackView() {
    let that = this
    if (that.catalogueConfig.show) { //优先关闭目录弹窗
      that.catalogueConfig.show = false
      return true
    }

    $utils.goBack()

    return true
  },

  recommendActionClick(evt) {
    this.isJump = true
    if (evt.detail.eventName == 'bookClick') {
      //替换当前阅读页
      $utils.routeReplacetheUrl('/pagesC/Read', {
        bookId: evt.detail.data.bookId,
        pathUrl: "小说详情页"
      })
    } else {
      COMMON_REPORT_UTILS.page_click_report('更多', '', `${this.bookId}`) //点击上报
      $utils.routetheUrl('/pagesB/Recommend', { pathUrl: this.pageDetail.pageName })
    }
  },

  async addShelf() {
    if (this.bookInfo.is_shelf) {
      $utils.showToast('请去首页的书架移除！')
      return
    }
    let res = await $apis.example.addBookshelfApi({ bookId: this.bookInfo.bookId})
    if (res.code == 200) {
      this.bookInfo.is_shelf = 1
      this.$app.$def.tabListType[0] = 0
    }
  },
  showMyToast(content, duration = 2500) {
    if (this.customToastShow) return
    this.customToastText = content
    this.customToastShow = true
    let timer = setTimeout(() => {
      this.customToastShow = false
      this.customToastText = ''
      clearTimeout(timer)
    }, duration);
  },
  //阅读人数处理 万级 处理成小数点
  readNumHandle(readNum) {
    if (Number(readNum) > 10000) {
      return (Number(readNum) / 10000).toFixed(1) + '万'
    }
    return readNum
  },
})
</script>

<style lang="less">
@import './index.less';
</style>
