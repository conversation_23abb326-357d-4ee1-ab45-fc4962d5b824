<template>
  <div class="wrap {{bgColor + switchClassSuffix}}" onclick="handleClick">
    <stack>
      <div class="box"></div>
      <div class="circle-box {{activeClass}}">
        <div class="circle {{checked?'':circleCustom}}"></div>
      </div>
    </stack>
  </div>
</template>

<style lang="less">
@import "../styles/base.less";

.wrap {
  width: @SwitchWidth;
  height: @SwitchHeight;
  border-radius: @SwitchCircleSize;
  .open-text {
    color: #ffffff;
    font-size: 20px * @ratio;
    padding-left: 10px * @ratio;
  }
  .close-text {
    color: #ffffff;
    font-size: 20px * @ratio;
    padding-left: 45px * @ratio;
  }
}

stack {
  align-items: center;
}

.box {
  width: 100%;
  height: 100%;
  border-radius: @SwitchHeight + 0px;
}

.switch-on {
  background-color: @SwitchOnColor;
}

.switch-off {
  background-color: @SwitchOffColor;
}

.switch-off-less-ad {
  background-color: rgba(0,0,0,0.50);
}

.switch-off-less-ad-2 {
  background-color: rgba(255,255,255,0.80);
}

.switch-off-less-ad-3 {
  background-color: rgba(255,255,255,0.80);
}

.switch-on-less-ad {
  background-color: rgb(241, 18, 18);
}

.switch-on-less-ad-2 {
  background-color: rgb(29, 46, 66);
}

.switch-on-less-ad-3 {
  background-color: rgb(65, 49, 41);
}

.switch-disabled-on {
  background-color: @SwitchDisabledOnColor;
}

.switch-disabled-on-less-ad {
  background-color: rgba(241, 18, 18, 0.3);
}

.switch-disabled-on-less-ad-2 {
  background-color: rgba(29, 46, 66, 0.3);
}

.switch-disabled-on-less-ad-3 {
  background-color: rgba(65, 49, 41, 0.3);
}

.switch-disabled-off {
  background-color: @SwitchDisabledOffColor;

  .circle {
    background-color: #cccccc;
  }
}

.switch-disabled-off-less-ad {
  background-color: @SwitchDisabledOffColor;

  .circle {
    background-color: #cccccc;
  }
}

.switch-disabled-off-less-ad-2 {
  background-color: @SwitchDisabledOffColor;

  .circle {
    background-color: #cccccc;
  }
}

.switch-disabled-off-less-ad-3 {
  background-color: @SwitchDisabledOffColor;

  .circle {
    background-color: #cccccc;
  }
}

.circle-box {
  padding-left: @SwitchPadding + 0px;
  padding-right: @SwitchPadding + 0px;
  width: 100%;
}

.circle-checked {
  animation-name: checked;
  animation-duration: 100ms;
  animation-iteration-count: 1;
  animation-fill-mode: forwards;
  transform: translateX(@SwitchCircleTranslateX);
}

.circle-dischecked {
  animation-name: dischecked;
  animation-duration: 100ms;
  animation-iteration-count: 1;
  animation-fill-mode: forwards;
  transform: translateX(0);
}

.circle {
  width: @SwitchCircleSize;
  height: @SwitchCircleSize;
  border-radius: @SwitchCircleSize;
  background-color: @SwitchCircleColor;
}

@keyframes checked {
  0% {
    transform: translateX(0px);
  }
  100% {
    transform: translateX(@SwitchCircleTranslateX + 0px);
  }
}

@keyframes dischecked {
  0% {
    transform: translateX(@SwitchCircleTranslateX + 0px);
  }
  100% {
    transform: translateX(0px);
  }
}

.circle-4 {
  background-color: #274260;
}

.circle-5 {
  background-color: #634B3F;
}
</style>

<script>
export default {
  data() {
    return {
      activeClass: "circle-checked",
      bgColor: "circle-checked",
      checked: this.value,
      circleClass: '',
    };
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    open: {
      default: ""
    },
    close: {
      default: ""
    },
    disabled: {
      default: false
    },
    name: {
      default: ""
    },
    switchClassSuffix: {
      default: ""
    },
    circleCustom: {
      default: ""
    }
  },

  onInit() {
    this.isChecked();
    this.$watch("value", "checkedChange");
    this.$watch("disabled", "configChange");
  },
  checkedChange() {
    this.checked = this.value;
    this.isChecked();
  },
  configChange() {
    this.isChecked();
  },
  handleClick() {
    let disabled = this.disabled;
    if (!disabled) {
      this.$emit("change", { checked: !this.checked, name: this.name });
    }
  },
  isChecked() {
    if (this.disabled) {
      if (this.checked) {
        this.activeClass = "circle-checked";
        this.bgColor = "switch-disabled-on";
      } else {
        this.activeClass = "circle-dischecked";
        this.bgColor = "switch-disabled-off";
      }
    } else {
      if (this.checked) {
        this.activeClass = "circle-checked";
        this.bgColor = "switch-on";
      } else {
        this.activeClass = "circle-dischecked";
        this.bgColor = "switch-off";
      }
    }
  }
};
</script>
