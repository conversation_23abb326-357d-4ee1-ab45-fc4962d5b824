// 工具页面
module.exports.toolPages = [
  ['qkpub/pages/Action', 'src/pages/Action'],
  ['qkpub/pages/Index', 'src/pages/Index'],
  ['qkpub/pages/UnionAd', 'src/UnionAd'],
  ['qkpub/pages/Video', 'src/pages/Video'],
  ['qkpub/components', 'src/components'],
  ['qkpub/common', 'src/common'],
  ['qkpub/utils', 'src/utils'],
  ['qkpub/pages/complaintUnit', 'src/subPages/complaintUnit'],
  ['qkpub/pages/CommonWebview', 'src/pages/CommonWebview'],
  ['qkpub/pages/Start', 'src/pages/Start'],
  ['qkpub/pages/YlhAds', 'src/YlhAds'],
  ['qkpub/pages/Back', 'src/pages/Back'],
  ['qkpub/pages/Revive', 'src/pages/Revive']
]

// 小说页面
module.exports.storyPages = [
  ['qkpub/pages/Action', 'src/pagesC/Action'],
  ['qkpub/pages/Read', 'src/pagesC/Read'],
  ['qkpub/pages/ReadEmpty', 'src/pagesC/Read'],
  ['qkpub/pages/Index', 'src/pages/Index'],
  ['qkpub/pages/Read', 'src/pages/Read'],
  ['qkpub/pages/Action', 'src/pages/Action'],
  ['qkpub/pages/UnionAd', 'src/UnionAd'],
  ['qkpub/pages/Video', 'src/pages/Video'],
  ['qkpub/components', 'src/components'],
  ['qkpub/common', 'src/common'],
  ['qkpub/utils', 'src/utils'],
  ['qkpub/pages/complaintUnit', 'src/subPages/complaintUnit'],
  ['qkpub/pages/CommonWebview', 'src/pages/CommonWebview'],
  ['qkpub/pages/YlhAds', 'src/YlhAds'],
  ['qkpub/pages/Back', 'src/pages/Back'],
  ['qkpub/pages/Revive', 'src/pages/Revive']
]

module.exports.sdks = [
  ['src/cy-sdk', 'src/cy-sdk'],
  ['src/_config.json', 'src/_config.json'],
  ['./release.js', './release.js']
]
