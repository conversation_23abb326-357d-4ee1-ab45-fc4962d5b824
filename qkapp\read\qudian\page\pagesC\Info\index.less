.circle-boost-wrap{
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
}

.detail-top-bg {
    width: 750px;
    height: 784px;
    position: absolute;
    top: 0;
    left: 0;
    background-color: #ffefe9;
}

.detail-wrapper {
    flex-direction: column;
}

.detail-info-wrapper {
    padding: 0 30px;
    width: 750px;
}

.dt-container {
    margin-top: 20px;
    flex-direction: column;
    align-items: flex-start;

    .dt-name {
        font-size: 34px;
        font-weight: bold;
        color: #333333;
        line-height: 44px;
        lines: 2;
        text-overflow: ellipsis;
    }
    .dt-author {
        font-size: 28px;
        color: #666666;
        height: 28px;
        margin-top: 30px;
        lines: 1;
        text-overflow: ellipsis;
    }
    .dt-tag {
        height: 28px;
        font-size: 28px;
        color: #666666;
        margin-top: 30px;
    }
}

.flex-column {
    flex-direction: column;
}

.detail-info-outter {
    width: 750px;
}

.dt-main {
    width: 750px;
    background-color: #ffffff;
}

.book-info-title {
    height: 36px;
    font-size: 36px;
    font-weight: bold;
    color: #333333;
}

.book-info-check-cate {
    font-weight: bold;
    color: #333333;
    line-height: 34px;
    font-size: 34px;
    flex-shrink: 0;
}


    .detail-bottom-count-content {
        align-items: center;

        .count-item {
            flex-direction: column;
            flex: 1;
            justify-content: center;
            height: 150px;

            .novel-score {
                font-size: 46px;
                font-weight: bold;
                color: #333333;
                font-size: 46px;
                height: 46px;
                .novel-score-small-font {
                    font-size: 20px;
                    color: #333333;
                    font-weight: normal;
                }
            }
            .novel-score-desc {
                font-size: 24px;
                color: #999999;
                margin-top: 20px;
                height: 24px;
            }
        }
    }
    .novel-desc {
        width: 702px;
        padding-bottom: 32px;
        border-bottom: 2px solid #f6f6f6;
        color: #666666;
        font-size: 28px;
        line-height: 43px;
        font-weight: 300;
    }
    .novel-drawer {
        height: 100px;
        align-items: center;
        flex-direction: row;
        border-bottom: 2px solid #F6F6F6;
        .drawer-text-1 {
            flex: 1;
            font-size: 32px;
            color: #333333;
            font-weight: bold;
        }
        .drawer-text-2 {
            font-size: 24px;
            color: #999;
        }
        .drawer-icon-1 {
            width: 18px;
            height: 18px;
            object-fit: fill;
        }
    }

.detail-bottom-star {
    width: 27px;
    height: 27px;
    margin-top: 13px;
}

.dt-rank {
    margin-top: 40px;
    align-items: center;
    padding: 0 30px;
    width: 690px;
    height: 66px;
    border-top-left-radius: 24px;
    border-top-right-radius: 24px;
    background: linear-gradient(90deg, #FE5C01 0%, #FE9901 100%);

    .dt-rank-icon {
        width: 34px;
        height: 34px;
    }
    .dt-rank-text {
        font-size: 28px;
        color: #ffffff;
        height: 28px;
        margin-left: 10px;
        font-weight: bold;
    }
    .dt-rank-arrow {
        margin-left: 10px;
        font-size: 34px;
        font-weight: bold;
        height: 34px;
        color: #ffffff;
    }
}

.desc-contents {
    margin: 10px 0 20px 0px;
}

.desc-text {
    font-size: 28px;
    color: #666666;
    line-height: 44px;
}

.splice-line {
    width: 690px;
    height: 1px;
    background-color: #f0f0f0;
    margin-top: 20px;
}

.contents-wrapper {
    padding: 40px 0px;
    align-items: center;

    .contents-more {
        font-size: 28px;
        color: #999999;
        height: 28px;
        margin-left: auto;
        margin-right: 2px;
        // width: 265px;
        flex-shrink: 0;
        lines:1;
        
    }
    .contents-more-icon {
        width: 24px;
        height: 24px;
        margin-top: 4px;
    }
}

.detail-content {
    width: 690px;
    background-color: #fafafa;
    border-radius: 24px;
    flex-direction: column;
    padding: 30px;
    padding-bottom: 110px;
    margin-left:30px;
    margin-bottom:30px;
    margin-top: 40px;

    .novel-chapter-mask {
        width: 690px;
        height: 200px;
        background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #fafafa  45%, #fafafa  100%);
        position: absolute;
        left: 0;
        bottom: 0;
        justify-content: center;
        border-bottom-left-radius: 24px;
        border-bottom-right-radius: 24px;

        image {
            width: 24px;
            height: 24px;
        }
    }
    .novel-chapter-title {
        height: 26px;
        margin-bottom: 24px;
        font-size: 26px;
        color: #666666;
        font-weight: bold;
    }
    .novel-chapter-content {
        font-size: 26px;
        line-height: 48px;
        color: #666666;
    }
    .lines-12 {
        lines: 12;
        text-overflow: ellipsis;
    }
}

.info-bottom {
    width: 345px;
    height: 96px;
    background-color: #f11212;
    border-radius: 48px;
    font-size: 38px;
    font-weight: bold;
    color: #ffffff;
    text-align: center;
}

.detail-empty-div {
    width: 750px;
    height: 120px;
    background-color: #F6F6F6;
}
.detail-line-div {
    width: 750px;
    height: 16px;
    background-color: #F4F5F7;
}

.add-desktop-container {
    position: fixed;
    bottom: 120px;
    left: 30px;
    .add-desktop-close {
        width: 30px;
        height: 30px;
        position: absolute;
        top: 25px;
        left: 15px;
    }
    .add-desktop-tip {
        color: #ffffff;
        margin-left: 50px;
    }
    .add-desktop-button {
        background-color: #ffef9d;
        border-radius: 74px;
        margin-right: 20px;
        text {
            font-size: 26px;
            color: #826d00;
            font-weight: 400;
            line-height: 26px;
            text-align: center;
            padding: 8px 17px;
        }
    }
}

.info-bottom-wrapper {
    position: fixed;
    bottom: 0px;
    left: 0px;
    background-color: #ffffff;
    padding-bottom: 20px;
    padding-top: 24px;
    width: 100%;
    padding-left: 30px;
}

.boots-hide {
    width: 750px; 
    padding: 0 0 0 24px;
    opacity:0;
}

.boots-show {
    width: 750px;
    padding: 0 0 0 30px;
}

.gray-bg {
    background-color: #EFEFEF;
}

.skeleton {
    background-color: #EFEFEF;
    height: 265px;
}

.info-bottom-image {
    width: 54px;
    height: 54px;
}
.native-boost-group{
    position: absolute;
    top: 0px;
    left: 0px;
    width: 750px;
    height: 500px;
}
