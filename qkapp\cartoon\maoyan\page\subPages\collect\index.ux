<import name="header" src="../../components/header"></import>
<import name="book-item" src="../../components/book-detail/book-item.ux"></import>
<import name="my-load-more" src="apex-ui/components/load-more/index"></import>

<template>
  <div class="page-wrapper">
    <header title="追更收藏"></header>
    <list class="list-container collect-list">
      <list-item type="item" for="{{collectList}}">
        <book-item item="{{$item}}" @item-click="itemClick">
          <text class="cacel-collect" @click="cacelCollect($idx)">取消收藏</text>
        </book-item>
      </list-item>
      <list-item type="noMore" class="load-status">
        <my-load-more tip="没有更多内容了" loading="{{ false }}"></my-load-more>
      </list-item>
    </list>
  </div>
</template>

<script>
export default {
  private: {
    collectList: [],
  },

  onInit() {
    $utils.getStorage('collect').then(value => {
      if (value) {
        this.collectList = JSON.parse(value);
      }
    });
  },

  cacelCollect(index, evt) {
    console.log(evt)
    evt && evt.stopPropagation();
    this.collectList.splice(index, 1);
    $utils.setCollectList(this.collectList, true);
  },

  itemClick(evt) {
    $utils.routetheUrl('subPages/book-detail', {
      info: JSON.stringify(evt.detail.item)
    })
  },
}
</script>

<style lang="less">
@import '../../assets/styles/index.less';

.cacel-collect {
  height: 50px;
  color: @text-grey;
  font-size: 24px;
  margin-top: 20px;
}

.collect-list {
  padding: 0 @app-padding;
}

</style>