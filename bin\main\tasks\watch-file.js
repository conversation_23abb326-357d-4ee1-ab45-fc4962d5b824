const path = require('path')
const appCopyDes = require('../../config/app-copy-des')
const { storyPages, toolPages } = require('../../config/tool-copy-des')
const process = require('process')
const fs = require('fs')
const watch = require('node-watch')
const { copyFile } = require('../../utils')
const { projectPath } = require('../constant')
const { handleBrandFile } = require('./comp-brand-code')

function watchAppFile(appInfo, brand) {
  const { category, code } = appInfo
  const workAPPPath = path.resolve(projectPath, 'qkapp', `${category}/${code}`)

  watchFile(appCopyDes, workAPPPath, brand)
}

function watchPublicFile(appType, brand) {
  const copyDes = appType === 'tool' ? toolPages : storyPages
  const workPath = path.resolve(process.cwd())

  watchFile(copyDes, workPath, brand)
}

function watchFile(copyDes, workPath, brand) {
  copyDes.forEach(([source, target]) => {
    const from = path.resolve(workPath, source)

    if (!fs.existsSync(from)) {
      return
    }

    watch(from, { recursive: true }, (evt, name) => {
      // 处理厂商文件
      handleBrandFile(name, brand)

      if (evt === 'remove') {
        console.log('\n删除文件', name)
        const refactorPath = pathReplace(name, source, target, workPath)
        fs.rmSync(refactorPath, { recursive: true })
        console.log('删除成功', refactorPath)
        return
      }

      if (evt === 'update') {
        console.log('\n更新文件', name)
        const refactorPath = pathReplace(name, source, target, workPath)
        copyFile(name, refactorPath)
        console.log('更新成功', refactorPath)
      }
    })
  })
}

function pathReplace(chanPath, source, target, workPath) {
  return chanPath.replace(path.resolve(workPath, source), path.resolve(projectPath, target))
}

module.exports = { watchAppFile, watchPublicFile }
