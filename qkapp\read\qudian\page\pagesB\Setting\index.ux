/**
* page : 设置页面
* author: yangyang
* date: 2022-03-04
*/
<import name="common-header" src="../../components/common-back-header/index"></import>
<import name="common-dialog" src="../../components/common-dialog/index"></import>
<template>
  <div class="setting-wrapper">
    <!-- 状态栏 -->
    <common-header
      title="设置"
      text-center="{{backTitleIsCenter}}"
      onback-click="pageBack"
    ></common-header>
    <!-- 设置 -->
    <div class="setting-menu-section">
      <div
        class="setting-menu-item"
        for="(index,item) in menuList"
        @click="menuItemClick(item)"
      >
        <text class="setting-menu-name">{{ item.name }}</text>
        <text style="flex: 1"></text>
        <image
          class="setting-menu-icon"
          src="https://img.qdreads.com/v163/user/jump-normal.png"
        ></image>
      </div>
    </div>
    <div style="flex: 1"></div>
    <text class="logout-btn" @click="logout" if="{{loginStatus}}"
      >退出登录</text
    >
    <!-- 注销弹窗 start-->
    <common-dialog 
      if = "{{showModal}}"
      title = "注销后所有数据将清空，是否确认注销？"
      cancel-btn-text = "仍要注销"
      confirm-btn-text = "放弃注销"
      ondialog-close = 'writeOffHandler'
      ondialog-confirm = "writeOff"
    >
    </common-dialog>
    <!-- 注销弹窗 end-->
    <!-- 返回腾讯系App的button -->
    <back-app-button
      if="{{$app.$def.isTencentBackButtonVisible}}"
      btn-text="{{$app.$def.tencentBackName}}"
      back-url="{{$app.$def.tencentBackUrl}}"
      package-name="{{$app.$def.tencentBackPkg}}"
    ></back-app-button>
  </div>
</template>
<import name="back-app-button" src="../../components/back-app-button"></import>

<script>
export default pageMixin({
  data: () => ({
    backTitleIsCenter: false, // 状态栏标题是否居中
    pathUrl: "",
    sexData: "女",
    loginStatus: 0, //
    menuList: [{
      name: '偏好设置',
      path: '/pagesB/Sex',
      param: {}
    }, {
      name: '订阅记录',
      path: '/pagesB/Subscribe',
      param: {}
    }, {
      name: '用户协议',
      path: '/pagesB/Privacy',
      param: {
        type: 1
      }
    }, {
      name: '隐私政策',
      path: '/pagesB/Privacy',
      param: {
        type: 2
      }
    },{
      name: '隐私设置',
      path: '/pagesB/Privacy-Setting',
      param: {}
    }],
    pageDetail: {
      pageUrl: '我的设置页',
      pageName: '我的设置页',
      pageCode: 'READ_COMMON',
      pageOrigin: ''
    },
    showModal: false,
  }),
  onInit() {
    this.loginStatus = this.$app.$def.loginStatus;
    if (this.loginStatus == 1) {
      this.menuList.push({ name: '账号注销' })
    }
    
  },
  onShow() {
    // 更新页面来源
    this.pathUrl = curObj.pathUrl
    if (this.$app.$def.sex == 1) {
      this.sexData = "男频"
    } else {
      this.sexData = "女频"
    }
    

  },
  onHide() {
    
  },
  onReady() {

  },
  onDestroy() {

  },
  /**
  * 设置中心-菜单点击
  * @param item: 菜单信息
  */
  menuItemClick(item) {
    COMMON_REPORT_UTILS.page_click_report(item.name) //点击上报
    if (item.name == '账号注销') {
      this.writeOff()
      return
    }
    $utils.routetheUrl(item.path, item.param, false)
  },
  /**
   * 退出登录
   */
  logout() {
    $apis.example.logout({}).then(data => {
      if (data.code == 200) {
        $utils.deleteStorage("token")
        $utils.deleteStorage("userInfo")
        this.loginStatus = 0
        this.$app.$def.login().then(res => {
          if (res.code == 200) {
            this.$app.$def.tabListType = [0, 0, 0, 0, 0]
            $utils.routetheUrl('/pagesA/Main', { selectIndex: 4, pathUrl: "设置页" }, true)
          }
        })
      }
    })
  },
  // 注销账号-取消
  writeOff() {
    this.showModal = !this.showModal
  },
  /**
   * 注销账户-提交
   */
  writeOffHandler() {
    console.log(this.$app.$def)
    $apis.example.writeOffApi({ androidId: this.$app.$def.android }).then(res => {
      if (res.code == 200) {
        this.$app.$def.loginStatus = 0;
        $utils.clearStorage().then(() => {
          var params = {
            isWriteOff: true,
            pathUrl: '我的设置页'
          }
          $utils.showToast("注销成功！")
          $utils.routetheUrl('pagesB/Sex', params, false)
        }).catch(() => {
          $utils.showToast("注销失败,请稍后重试")
        })
      } else {
        $utils.showToast("注销失败,请稍后重试")
      }
    }).catch(err => {
      $utils.showToast("注销失败,请稍后重试")
    })
  },
  /**
   * 状态栏返回功能
   */
  pageBack() {
    COMMON_REPORT_UTILS.page_click_report('返回') //点击上报
    $utils.goBack()
  },
  /**
   * 物理返回
   */
  onBackPress() {
    COMMON_REPORT_UTILS.back_click_report('','','跳转页面')
  }
})
</script>

 <style lang="less">
@import './index.less';
</style>
