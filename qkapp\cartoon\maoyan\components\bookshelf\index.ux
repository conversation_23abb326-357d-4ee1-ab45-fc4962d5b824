<import name="header" src="../header"></import>

<template>
  <div class="page-column">
    <header show-icon="{{false}}" title="我的书架"></header>
    <div class="title-bar" show="{{!editStatus}}">
      <text class="text-black">已加入{{ shelfList.length }}本</text>
      <div @click="switchToEdit" if="{{ shelfList.length }}">
        <text class="text-primary iconfont">&#xe641;</text>
        <text class="text-primary">编辑</text>
      </div>
    </div>
    <div class="title-bar" show="{{editStatus}}">
      <text class="text-black">已删除{{ deleteList.length }}本</text>
      <div>
        <text class="text-cancel" @click="cancelEdit">取消</text>
        <text class="text-primary" @click="finishEdit">完成</text>
      </div>
    </div>
    <div class="bookshelf-list">
      <div
        class="bookshelf-item"
        for="{{shelfList}}"
        show="{{deleteList.indexOf($item.id)===-1}}"
      >
        <stack class="bookshelf-img">
          <div class="item-wrapper" @click="clickImg($item)">
            <div class="list-image">
              <image src="{{$item.image}}"></image>
            </div>
          </div>
          <div class="del-bg" if="{{editStatus}}"></div>
          <div class="delete-icon" if="{{editStatus}}" @click="deleteBook($item)">
            <image
              src="../../assets/images/cancel.png"
            ></image>
          </div>
        </stack>
        <text class="book-title">{{ $item.title }}</text>
        <text class="book-author">{{ $item.author }}</text>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: ['shelfList'],
  data: {
    img: '/assets/images/pic_1.jpg',
    editStatus: false,
    deleteList: []
  },
  onInit() {},
  /* -------------------SelfCustomEvent------------------ */
  clickImg(item) {
    if (!this.editStatus) {
      this.gotoBookContent(item)
    } else {
      this.deleteBook(item)
    }
  },
  gotoBookContent(item) {
    this.$emit('updatePageHide', { data: true });
    COMMON_REPORT_UTILS.page_click_report('漫画书');
    $utils.routetheUrl('subPages/book-detail', {
      info: JSON.stringify(item)
    })
  },
  deleteBook(item) {
    COMMON_REPORT_UTILS.page_click_report('删除');
    this.deleteList.push(item.id)
  },
  switchToEdit() {
    this.editStatus = true
    COMMON_REPORT_UTILS.page_click_report('编辑');
  },
  finishEdit() {
    let shelfList = this.shelfList.filter(book => {
      return this.deleteList.indexOf(book.id) === -1
    })
    this.$emit('updateShelf', { shelfList })
    this.editStatus = false
    this.deleteList = []
    COMMON_REPORT_UTILS.page_click_report('完成');
  },
  cancelEdit() {
    this.editStatus = false
    this.deleteList = []
    COMMON_REPORT_UTILS.page_click_report('取消');
  }
}
</script>

<style lang="less">
@import '../../assets/styles/index.less';

.text-cancel {
  color: @text-grey;
  margin-right: @app-padding;
}
.bookshelf-list {
  width: 100%;
  padding: 20px;
  padding-top: 0;
  padding-left: 6px;
  flex-wrap: wrap;

  .bookshelf-item {
    margin-top: 20px;
    width: 224px;
    margin-left: 14px;
    .flex-box-mixins(column, flex-start, flex-start);
  }
  .bookshelf-img {
    width: 224px;
    height: 300px;
    .flex-box-mixins(row, flex-end, flex-end);

    .delete-icon {
      width: 60px;
      height: 60px;
      padding: @gap-1;
    }
  }
}

.item-wrapper {
  flex-direction: column;
}

.list-image {
  width: 224px;
  height: 300px;
  border: 1px solid #f0f0f0;
  border-radius: 14px;

  image {
    width: 100%;
    height: 100%;
    border-radius: 14px;
  }
}

.book-title {
  color: #333333;
  height: 30px;
  font-size: 30px;
  margin-top: @gap-2;
  lines: 1;
  text-overflow: ellipsis;
}

.book-author {
  height: 24px;
  color: #999999;
  font-size: 24px;
  margin-top: 14px;
  lines: 1;
  text-overflow: ellipsis;
}

.del-bg {
  width: 224px;
  height: 300px;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 14px;
}
</style>
