<template>
    <div class="wrapper" onclick="goNumber">
        <image class="icon" src="https://img.sytangshiwl.top/public/del.png" alt="电话图标"></image>
        <div class="content">
            <div class="content-top">
                <text class="title">快应用投诉热线</text>
                <text class="subtitle">7*24小时为您解决问题</text>
            </div>
            <div class="content-bottom">
                <text class="phone-number">{{formatPhoneNumber(number)}}</text>
                <text class="link" onclick="onClickCall">[点击拨打]</text>
            </div>
        </div>
    </div>
    
</template>
<script>
export default {
    data:{
        number:'15319455054',
    },
    onInit() {
      this.getNumber()
    },
    goNumber(){
        COMMON_REPORT_UTILS.page_click_report(`点击投诉电话`)
        require('@system.router').push({
            uri:`tel:${this.number}`
        })
    },
    getNumber(){
        this.number = this.commonGetConfig('complaint_number') || '15319455054'
    },
    formatPhoneNumber(phoneNumber) {
    // 确保输入是字符串
    phoneNumber = String(phoneNumber);
    
    // 检查手机号是否为11位
    // if (phoneNumber.length !== 11 || !/^\d+$/.test(phoneNumber)) {
    //     throw new Error("手机号必须是11位数字");
    // }

    // 格式化手机号
    let formattedNumber = ''
    if(phoneNumber.length === 10){
        formattedNumber = `${phoneNumber.slice(0, 3)}-${phoneNumber.slice(3, 6)}-${phoneNumber.slice(6)}`;
    }
    else{
    formattedNumber = `${phoneNumber.slice(0, 3)}-${phoneNumber.slice(3, 7)}-${phoneNumber.slice(7)}`;
    }
    
    return formattedNumber;
    },
    //读取配置
    commonGetConfig(key) {
        let getValue = ''
        try {
            getValue = sdk.tactics.getCustomParams(key)
        } catch (error) {

        }
        return getValue
    },

}
</script>

<style lang='less'>
.wrapper {
    display: flex;
    background-color: #fff;
    padding: 30px;
    width: 686px;
    height: 162px;
    border-radius:20px;
    flex-direction: row;
    align-items: center;
}

.icon {
    width: 88px;
    height: 88px;
    margin-right: 18px;
}

.content {
    display: flex;
    flex-direction: column;
    .content-top{
        .title{
            color: #333333;
            font-size:28px;
            font-weight:600;
            margin-right: 10px;
        }
        .subtitle{
            color:#999999;
            font-size:26px;
            font-weight:400;
        }
    }
    .content-bottom{
        
        .phone-number{
            font-size: 40px;
            line-height: 40px;
            font-weight: 600;
            border-bottom: 2px solid #2388FF;
            color: #2388FF;
            margin-right: 10px;
        }
        .link{
            font-size: 28px;
            font-weight: 600;
            line-height: 28px;
            color: #2388FF;
        }
    }
}


</style>
