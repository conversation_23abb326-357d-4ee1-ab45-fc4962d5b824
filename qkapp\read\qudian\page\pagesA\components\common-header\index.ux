<template>
  <!-- template里只能有一个根节点 -->
  <div>
    <div class="store-wrapper">
      <div if="{{title}}"
        class="main-common-header"
        style="padding-top:{{deviceInfo.statusBarHeight}}px;height:{{deviceInfo.statusBarHeight + 88}}px"
      >
        <text class="main-common-name">{{ title }}</text>
      </div>
      <div else
        class="main-common-header"
        style="padding-top:{{deviceInfo.statusBarHeight}}px;height:{{deviceInfo.statusBarHeight}}px"
      >
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data: {
    pageName: '书城',
    deviceInfo: {
      statusBarHeight: 40
    }
  },
  onReady() {
    this.deviceInfo.statusBarHeight = sdk.env.device.statusBarHeight
  },
  props: {
    title: {
      type: String,
      default: ''
    }
  }
}
</script>

<style lang="less">
.main-common-header {
  width: 750px;
  flex-direction: row;
  .main-common-name {
    font-size: 44px;
    color: #333333;
    font-weight: 600;
    margin-left: 30px;
    height: 44px;
  }
}
</style>
