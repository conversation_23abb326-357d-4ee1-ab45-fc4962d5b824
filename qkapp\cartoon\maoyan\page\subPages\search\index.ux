<import
  name="search-input-bar"
  src="../../components/search-book/search-input-bar"
></import>
<import
  name="book-item"
  src="../../components/book-detail/book-item.ux"
></import>
<import name="header" src="../../components/header"></import> 

<template>
  <div class="page-wrapper">
    <header title="搜索页面" @back-click="backIconHandle"></header>
    <search-input-bar
      onsearch="search"
      init-value="{{initValue}}"
    ></search-input-bar>
    <list class="list-container pd" if="{{searchText}}">
      <list-item type="title">
        <text class="list-title">搜到{{ resultList.length }}条结果</text>
      </list-item>
      <list-item class="book-list" for="{{resultList}}" type="book-item">
        <book-item item="{{$item}}" @item-click="goToDetail"></book-item>
      </list-item>
    </list>
  </div>
</template>

<script>
import { bookListData } from '../../assets/data/book-list.js'
export default {
  public: {
    initValue: ''
  },
  private: {
    resultList: [],
    searchText: ''
  },
  onReady() {
    this.searchText = this.initValue
    this.queryFn()
  },
  /* -------------------SelfCustomEvent------------------ */
  search(info) {
    this.searchText = info.detail
    this.queryFn()
  },
  queryFn() {
    // 根据关键字，查询接口，返回resultList。此处模拟接口查询，只能查询到bookListData中的图书。
    this.resultList = this.searchText
      ? bookListData.filter(item => {
          return item.title.indexOf(this.searchText) !== -1
        })
      : []
  },
  goToDetail(evt) {
    // 根据id查询详情
    $utils.routetheUrl('subPages/book-detail', { info: evt.detail.item })
  },
  backIconHandle() {
    $utils.goBack();
  },
}
</script>

<style lang="less">
@import '../../assets/styles/index.less';
.book-item {
  height: 300px;
  width: 100%;
  padding: @gap-3 0;
  .flex-box-mixins(row, space-between, flex-start);
  .list-text {
    flex: 1;
    height: 100%;
    .flex-box-mixins(column, center, flex-start);
    .book-title {
      lines: 1;
    }
    .book-description {
      lines: 2;
    }
    text {
      margin-bottom: @gap-2;
    }
  }
  .g22kjdgy {
    color: #ffffff;
  }
  .list-image {
    width: 180px;
    height: 240px;
    margin-right: @gap-3;
    background-color: @white-grey;
    border-radius: 8px;
    image {
      width: 100%;
      height: 100%;
      border-radius: 8px;
    }
  }
}

.book-list {
  width: 100%;
}

.pd {
  padding: 0 @app-padding;
}
</style>
