#!/usr/bin/env node

// 初始化 配置文件
const fs = require('fs')
const path = require('path')
const process = require('process')
const { consoleSplit, consoleTips } = require('../utils/console')

// 读取配置文件路径
const projectPath = path.resolve(__dirname)
const configPath = path.resolve(projectPath, 'brand-config.js')

// 检查配置文件是否存在
if (fs.existsSync(configPath)) {
  consoleTips('配置文件已存在，请勿重复初始化')
  process.exit(1)
}

fs.createReadStream(path.resolve(__dirname, '../template/global.config.js')).pipe(fs.createWriteStream(configPath))

consoleSplit('配置文件初始化完成')
