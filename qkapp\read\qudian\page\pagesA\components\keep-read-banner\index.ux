<template>
  <div class="keep-read-wrapper" style="bottom:{{keepReadHeight}}px;">
      <image class="book-image" src="{{bookInfo.public}}"></image>
      <div class="book-detail">
          <text class="book-title">{{bookInfo.title}}</text>
          <text class="book-chapter">已阅读：{{bookInfo.chapter}}</text>
      </div>
      <text class="book-read-btn" onclick="keepReadClick">继续阅读</text>
      <image class="close-img" src="https://img.qdreads.com/v155/comon/keep-read-close.png" onclick="closeClick"></image>
  </div>
</template>
<script>
export default {
    props: {
      bookInfo:{
          type:Object,
          default:null
      },
      desktopHeight:{
        type: Number,
        default: 80
      },
      addDesktop:{
        type: Number,
        default: 0
      },
    },
    data: {
      keepReadHeight: 126
    },
    onInit() {
      if(this.addDesktop){
        this.keepReadHeight = 126 + 14 + this.desktopHeight
      }
      this.$watch('addDesktop','addDesktopChangeHandler')
    },
    addDesktopChangeHandler(n,o){
      if(this.addDesktop){
        this.keepReadHeight = 126 + 14 + this.desktopHeight
      }else{
        this.keepReadHeight = 126
      }
    },
    keepReadClick(){
        this.$emit('keepRead',{eventName:'keepRead'})
    },
    closeClick(){
        this.$emit('keepRead',{eventName:'close'})
    }
}
</script>
<style lang="less">
.keep-read-wrapper {
  position: absolute;
  left: 30px;
  width: 690px;
  height: 152px;
  background-color: rgba(45, 50, 67, 0.93);
  border-radius: 24px;
  padding: 20px;
  align-items: center;
  .book-image {
    width: 78px;
    height: 112px;
    background-color: #c4c4c4;
    border-radius: 8px;
  }
  .book-detail {
    flex: 1;
    height: 100%;
    flex-direction: column;
    margin-left: 20px;
    padding: 20px 0px;
    justify-content: space-between;
    .book-title {
      width: 100%;
      font-size: 28px;
      font-weight: 500;
      color: #ffffff;
      text-overflow: ellipsis;
      lines: 1;
      height: 28px;
    }
    .book-chapter {
      width: 100%;
      font-size: 24px;
      font-weight: 400;
      color: #c5c6cb;
      height: 24px;
      text-overflow: ellipsis;
      lines:1;
    }
  }
  .book-read-btn {
    width: 144px;
    height: 60px;
    background: linear-gradient(90deg, #ffe8cc, #ffe7c9 100%);
    border-radius: 200px;
    font-size: 24px;
    font-weight: 600;
    text-align: center;
    color: #68522b;
    margin-left: 30px;
  }
  .close-img {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 16px;
    height: 16px;
  }
}
</style>
