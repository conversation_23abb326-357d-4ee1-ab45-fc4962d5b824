.novel-item {
    width: 100%;
    height: 192px;
    flex-direction: row;
    margin-top: 20px;
    margin-bottom: 20px;
    .novel-pic {
        width: 128px;
        height: 181px;
        margin-right: 24px;
        object-fit: fill;
        border-radius: 8px;
    }
    .novel-detail {
        flex: 1;
        flex-direction: column;
        .novel-title {
            lines: 1;
            text-overflow: ellipsis;
            height: 34px;
            font-size: 34px;
            color: #333;
        }
        .novel-desc {
            height: 73px;
            line-height: 36px;
            font-size: 24px;
            color: #adadad;
            lines: 2;
            text-overflow:ellipsis;
        }
        .novel-bottom-detail {
            flex-direction: row;
            height: 51px;
            padding-top: 15px;
            .novel-author {
                flex: 1;
                height: 36px;
                font-size: 22px;
                color: #ADADAD;
                font-weight: 500;
                align-items: center;
            }
            .novel-type {
                height: 28px;
                margin-top: 4px;
                padding: 0 7px;
                border-radius: 6px;
                font-size: 20px;
                font-weight: 500;
                background-color: #f6f6f6;
                color: #999999;
                text-align: center;
            }
            .novel-type-1{
                background-color: #edf9ff;
                color: #0989FF;
            }
            .novel-type-2{
                background-color: #F5FBE3;
                color: #8BB80C;
            }
            .novel-type-3{
                background-color: #F6F1FF;
                color: #9157FF;
            }
            .novel-type-4{
                background-color: #F5F5F5;
                color: #7C7C7C;
            }
            .novel-type-5{
                background-color: #FFF3F5;
                color: #FF5E5E;
            }
        }
    }
}

.nd-name-wrapper {
    justify-content: space-between;
    align-items: center;
    margin-top: 4px;
    margin-bottom: 20px;
    height: 40px;
    .ly-ily {
        height: 38px;
        font-size: 38px;
        font-weight: bold;
        color: #FF3D4F;
        flex-shrink: 0;
    }
}