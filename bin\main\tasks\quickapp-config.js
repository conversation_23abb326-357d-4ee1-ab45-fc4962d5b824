const fs = require('fs')
const path = require('path')
const { editFile } = require('../../utils/file-edit')
const commonModification = [
  {
    oldValue: `//slot_defineplugin_1`,
    newValue: `const pluginConfig = require('./src/company-config')`
  },
  {
    oldValue: `//slot_defineplugin_2`,
    newValue: `new webpack.DefinePlugin({
    ...pluginConfig.pro,
    ...pluginConfig.dev
  }),`
  },
  {
    oldValue: `//slot_defineplugin_3`,
    newValue: `new webpack.DefinePlugin({
    ...pluginConfig.pro
  }),`
  }
]

const Modification_1 = [
  {
    oldValue: `//slot_obfuscator_1`,
    newValue: `const WebpackObfuscator = require('webpack-obfuscator-plugin')  //小米使用`
  },
  {
    oldValue: `//slot_obfuscator_3`,
    newValue: `new WebpackObfuscator({
  stringArray: false,
  renameGlobals: false,
  renameProperties: false,
  identifierNamesGenerator: 'mangled',
}),`
  }
]

const Modification_2 = `module.exports = function (api) {
    api.cache(true)
    return {
        presets: [
            [
                '@babel/preset-env',
                {
                    targets: {
                        node: 8
                    }
                }
            ]
        ],
        plugins: [
            'babel-plugin-attr2',
            '@babel/plugin-transform-modules-commonjs',
            '@babel/plugin-proposal-optional-chaining'
        ],
        babelrcRoots: ['.', 'node_modules']
    }
}`

const babelrcConfigRabbit = `module.exports = function (api) {
  api.cache(true)
  return {
    presets: [
      [
        '@babel/preset-env',
        {
          targets: {
            node: 8
          }
        }
      ]
    ],
    plugins: [
      'babel-plugin-value',
      '@babel/plugin-transform-modules-commonjs',
      '@babel/plugin-proposal-optional-chaining',
      'babel-plugin-string'
    ],
    babelrcRoots: ['.', 'node_modules']
  }
}
`

const Modification_3 = `module.exports = function (api) {
  api.cache(true)
  return {
    presets: [
      [
        '@babel/preset-env',
        {
          targets: {
            node: 8
          }
        }
      ]
    ],
    plugins: [
      'babel-plugin-value',
      '@babel/plugin-transform-modules-commonjs',
      '@babel/plugin-proposal-optional-chaining'
    ],
    babelrcRoots: ['.', 'node_modules']
  }
}
`

/**
 *
 * @param isCompanyDivided 是否分公司
 * @param projectPath 项目路径
 * @param company 公司
 * @param brand 厂商
 * @param useStrReplace 是否使用字符串替换
 * @returns
 */
function quickappConfig(isCompanyDivided, projectPath, company, brand, useStrReplace) {
  if (!isCompanyDivided) {
    return
  }

  // const quickappConfigPath = path.resolve(projectPath, 'quickapp.config.js')
  // commonModification.forEach(item => {
  //     editFile(quickappConfigPath, item.oldValue, item.newValue)
  // })                                           // 公共配置
  resetConfigContent(projectPath, company) // 重置配置
  editConfigContent(projectPath, company, brand, useStrReplace) // 分公司配置
}

function resetConfigContent(projectPath, company) {
  const quickappConfigPath = path.resolve(projectPath, 'quickapp.config.js')
  const babelConfigPath = path.resolve(projectPath, 'babel.config.js')
  Modification_1.forEach(item => {
    editFile(quickappConfigPath, item.newValue, item.oldValue)
  })
  if (fs.existsSync(babelConfigPath)) {
    fs.unlinkSync(babelConfigPath)
  }
}

function editConfigContent(projectPath, company, brand, useStrReplace) {
  switch (company) {
    case 'cy':
      return editCyConfig(projectPath)
    case 'rabbit':
      return editRabbitConfig(projectPath, brand, useStrReplace)
    case 'kang':
      return editKangConfig(projectPath)
    default:
      return
  }
}

function editRabbitConfig(projectPath, brand, useStrReplace) {
  const quickappConfigPath = path.resolve(projectPath, 'quickapp.config.js')
  const babelConfigPath = path.resolve(projectPath, 'babel.config.js')
  if (brand === 'xiaomi') {
    Modification_1.forEach(item => {
      editFile(quickappConfigPath, item.oldValue, item.newValue)
    })
  }
  if (useStrReplace) {
    fs.writeFileSync(babelConfigPath, babelrcConfigRabbit)
  }
}

function editKangConfig(projectPath) {
  const babelConfigPath = path.resolve(projectPath, 'babel.config.js')
  if (!fs.existsSync(babelConfigPath)) {
    fs.writeFileSync(babelConfigPath, Modification_2)
  }
}

function editCyConfig(projectPath) {
  const babelConfigPath = path.resolve(projectPath, 'babel.config.js')
  if (!fs.existsSync(babelConfigPath)) {
    fs.writeFileSync(babelConfigPath, Modification_3)
  }
}

module.exports = {
  quickappConfig
}
