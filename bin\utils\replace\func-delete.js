const { astReplace } = require('./base/code-split')
const traverse = require('@babel/traverse').default

/*
 * 通过方法名，删除方法和方法后的逗号，反馈方法被调用的地方
 * @param content 文件代码
 * @param oldCode 需要替换的代码
 */

function funcDelete({ content, oldCode }) {
  const code = astReplace(content, (ast, scriptCode) => {
    const result = []
    traverse(ast, {
      ObjectMethod(path) {
        const methodNode = path.node
        if (methodNode.key && methodNode.key.name === oldCode) {
          result.push({
            start: methodNode.start,
            end: scriptCode[methodNode.end] === ',' ? methodNode.end + 1 : methodNode.end,
            type: 'remove'
          })
        }
      }
    })

    return result
  })
  return code
}

module.exports = funcDelete
