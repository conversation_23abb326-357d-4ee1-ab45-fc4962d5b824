export interface BusinessConfig {
  filePath: string
  modifications: Modification[]
}

export interface Modification {
  oldCode: string
  newCode: string
  desc?: string
  type: ModificationType
}

// 生成Modification type 的枚举
export enum ModificationType {
  replace = 'replace', // 字符串替换
  deleteFun = 'deleteFun', // 删除函数
  deleteAttr = 'deleteAttr', // 删除属性
  appUxReplace = 'appUxReplace', // app.ux 替换
  replaceFun = 'replaceFun', // 替换函数
  delete = 'delete', // 删除字符串
  replaceTag = 'replaceTag', // 标签替换
  addPageMixin = 'addPageMixin', // 添加pageMixin
  addAppMixin = 'addAppMixin' // 添加appMixin
}
