<template>
  <!-- template里只能有一个根节点 -->
  <div>
    <div class="novel-item" @click="bookItemClickHandler">
      <image src="{{novelData.bookIconUrl}}" class="novel-pic"></image>
      <text class="novel-title">{{ novelData.bookName }}</text>
      <div class="novel-detail">
        <text class="novel-type novel-type-{{randomCount}}">{{
          novelData.bookType
        }}</text>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data: {
    list: [],
    randomCount: 0
  },
  onInit() {
    this.randomCount = Math.ceil(Math.random() * 5)
  },

  bookItemClickHandler() {
    this.$emit('compClick', { bookId: this.novelData.bookId, bookName: this.novelData.bookName})
  },
  props: {
    novelData: {
      type: Object,
      default: null
    }
  }
}
</script>

<style lang="less">
@import './index.less';
</style>
