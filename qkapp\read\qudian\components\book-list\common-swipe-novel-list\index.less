.store-novel-list-type-1 {
    width: 690px;
    background-color: #ffffff;
    border-radius: 24px;
    flex-direction: column;
    padding: 30px;
    padding-left: 0;
    // height: 654px;

    .list-novel-type-1 {
        flex-direction: row;
        margin-top: 40px;
        margin-left: 23px;
        padding: 0;

        .novel-pic {
            width: 128px;
            height: 181px;
            border-radius: 6px;
        }
        .novel-detail {
            flex: 1;
            height: 181px;
            padding: 6px 0;
            flex-direction: column;

            .novel-name {
                height: 34px;
                font-size: 34px;
                color: #333333;
                lines: 1;
                text-overflow: ellipsis;
                flex-grow: 1;
            }
            .novel-desc {
                // height: 73px;
                line-height: 36px;
                font-size: 24px;
                color: #adadad;
                lines: 2;
                text-overflow: ellipsis;
                margin-top: 20px;
            }
            .novel-author {
                height: 24px;
                font-size: 22px;
                color: #adadad;
                margin-top: 22px;
                align-items: center;
            }
            .nd-name-wrapper {
                align-items: center;
                .ly-ily {
                    height: 38px;
                    font-size: 38px;
                    font-weight: bold;
                    color: #ff3d4f;
                    flex-shrink: 0;
                }
            }
        }
    }

}

.title-desc {
    height: 36px;
    width: 146px;
    font-size: 36px;
    font-weight: 600;
    color: #333333;
}

.title-underline-default {
    background: linear-gradient(90deg,#ffd1d1 0%, #ffffff 100%);
    background-repeat: no-repeat;
    height: 8px;
    width: 146px;
    margin-top: 28px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}
.title-underline-vip {
    background: linear-gradient(90deg,#e6b598, #f4dcc2 100%);
    background-repeat: no-repeat;
    height: 8px;
    width: 146px;
    margin-top: 28px;
    border-top-left-radius: 8px;
    border-bottom-right-radius: 8px;
}

.title-underline-male {
    background: linear-gradient(90deg,#B0D9FF 0%, #ffffff 100%);
    background-repeat: no-repeat;
    height: 8px;
    width: 146px;
    margin-top: 28px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}

.type-1-name {
    font-size: 26px;
    color: #333333;
    line-height: 36px;
    height: 72px;
    lines: 2;
    text-overflow: ellipsis;
    margin-top: 10px;
    margin-left: 7px;
}

.type-1-score {
    height: 30px;
    color: #ff3d4f;
    margin-top: 10px;
    margin-left: 7px;
}

.novel-list {
    flex: 1;
    margin-top: 33px;
    margin-left: 23px;
}

.type-1-novel-item {
    margin-left: 33px;
    flex-direction: column;
}

.margin-ledt-0 {
    margin-left: 0;
}

.list-title {
    margin-left: 30px;
}