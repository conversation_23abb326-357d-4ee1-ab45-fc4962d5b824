<template>
  <div class="recommend-wrapper">
    <!-- 状态栏 -->
    <common-header
      title="{{pageTitle}}"
      text-color="#333333"
      text-center="{{ backTitleIsCenter }}"
      onback-click="pageBack"
    ></common-header>
    <div class="recommend-content-section">
      <list class="recommend-list" onscrollbottom="scrollBottomHandler">
        <list-item type="novel-item" for="(i, novelItem) in recommendBookList">
          <common-novel-item
            oncomp-click="compClickHandler"
            novel-data="{{novelItem}}"
            is-show-type="{{true}}"
          ></common-novel-item>
        </list-item>
        <list-item class="load-more" if="{{loadMore}}" type="showmode">
          <progress type="circular"></progress>
          <text>加载更多！</text>
        </list-item>
        <list-item class="load-more" if="{{!loadMore}}" type="showmode">
          <text class="no-more">没有更多了</text>
        </list-item>
      </list>
    </div>
    <!-- 返回腾讯系App的button -->
    <back-app-button
      if="{{$app.$def.isTencentBackButtonVisible}}"
      btn-text="{{$app.$def.tencentBackName}}"
      back-url="{{$app.$def.tencentBackUrl}}"
      package-name="{{$app.$def.tencentBackPkg}}"
    ></back-app-button>
  </div>
</template>
<import name="back-app-button" src="../../components/back-app-button"></import>
<import name="common-header" src="../../components/common-back-header/index"></import>
<import name="common-novel-item" src="../../components/book-list/common-novel-item/index.ux"></import>
<script>

export default pageMixin({
  data: () => ({
    pageTitle: '猜你喜欢',
    loading: false,
    backTitleIsCenter: false, // 状态栏文字居左
    activeType: 1,// 分类高亮  1：男频 2： 女频
    loadMore: true,
    page: 1,
    totalPage: 0,
    recommendBookList: [],
    pathUrl: '',
    pageDetail: {
      pageUrl: '猜你喜欢',
      pageName: '猜你喜欢',
      pageCode: 'READ_COMMON',
      pageOrigin: ''
    }
  }),
  onInit() {
    this.activeType = this.$app.$def.sex;
    this.getRecommendBook()
    
  },
  onShow() {
    // 更新页面来源
    
  },
  onHide() {
    
  },
  getRecommendBook() {
    this.loading = true
    $apis.example.bookRecommendApi({
      type: 1,
      page: this.page,
      page_size: 10
    }).then(res => {
      this.loading = false
      if (res.code == 200) {
        this.recommendBookList = this.recommendBookList.concat(res.data)
        this.loadMore = res.data.length < 10 ? false : true
        this.$app.$def.uploadListShow(res.data,'猜你喜欢')
      } else {
        this.page--
      }
    }).catch(err => {
      this.loading = false
      this.page--
    })
  },
  /**
   * list加载更多（上拉加载）
   */
  scrollBottomHandler() {
    this.page++
    if (!this.loadMore) {
      $utils.showToast('没有更多数据了！', 0)
      return
    } else {
      this.getRecommendBook()
    }
  },
  compClickHandler(evt){
    var params = {
      bookId: evt.detail.bookId,
      pathUrl: this.pageDetail.pathUrl
    }
    COMMON_REPORT_UTILS.list_click_report('1',[`${evt.detail.bookId}`],'猜你喜欢') //点击上报
    $utils.routetheUrl('/pagesC/Read', params, false)
  },
  /**
   * 返回
   * 
   */
  pageBack() {
    this.goBackclick()
    COMMON_REPORT_UTILS.page_click_report(`返回`)
  },
  onBackPress() {
    COMMON_REPORT_UTILS.back_click_report('', '', '跳转页面')
    this.goBackclick()
    return true
  },
  /**
   * 返回
   * @param {Boolean} isNeedReportBackPress 是否上报物理返回 默认false
   */
  goBackclick() {
    $utils.goBack()
  }

})
</script>

<style lang="less">
@import './index.less';
</style>
