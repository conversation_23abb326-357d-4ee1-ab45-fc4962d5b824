const colors = require('colors')

module.exports.consoleSplit = text => {
  const len = getTextLength(text)
  const total = 80

  if (len > total) {
    console.log(colors.yellow(text))
  } else {
    const slitCount = Math.ceil((total - len) / 2)

    console.log(colors.yellow('-'.repeat(slitCount) + ` ${text} ` + '-'.repeat(slitCount)))
  }
}

module.exports.consoleTips = text => {
  console.log(colors.yellow(text))
}

function getTextLength(text) {
  let length = 0
  for (let char of text) {
    if (char.match(/[^\x00-\xff]/)) {
      // 中文字符占2个长度
      length += 2
    } else {
      // 英文字符占1个长度
      length += 1
    }
  }
  return length
}
