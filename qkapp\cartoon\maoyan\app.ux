<script>
import commonApp from "./utils/app_ux_public.js"

const hook2global = global.__proto__ || global
hook2global.$shelfList = []
hook2global.$collectList = []

export default appMixin({
  $shelfList: hook2global.$shelfList,
  $collectList: hook2global.$collectList,
  selectChapter: '',
  ...commonApp,
  onCreate: commonApp.onCreatePublic,
  onShow: function () {
    commonApp.onShowPublic(this)
  },
  onHide: function () {
    commonApp.onHidePublic(this)
  },
  onError: function (err) {
    commonApp.onErrorPublic(this, err)
  },
  onDestroy: function () {
    commonApp.onDestroyPublic(this)
  }
})

</script>

