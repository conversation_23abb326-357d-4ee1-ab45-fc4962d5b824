<import name="common-header" src="../../components/common-back-header/index"></import>
<template>
  <div class="login-wrapper">
    <!-- 状态栏 -->
    <common-header title="" onback-click="pageBack"></common-header>
    <!-- 登录部分 -->
    <div class="login-section">
      <text class="des">你好，</text>
      <text class="des">欢迎来到趣点</text>
      <!-- <image class="login-name" src="https://img.qdreads.com/v155/common-pagesb/login-title.png"></image> -->
      <div class="login-content">
        <!-- 手机号 -->
        <div class="login-input-box">
          <text class="label">手机号</text>
          <input
            class="login-input-phone"
            type="number"
            placeholder="请输入您的手机号"
            maxlength="11"
            onchange="phoneChange"
            value="{{phone}}"
          />
        </div>
        <!-- 验证码 -->
        <div class="login-input-box">
          <text class="label">验证码</text>
          <input
            id="inputCode"
            type="number"
            class="login-input-code"
            placeholder="请输入验证码"
            maxlength="4"
            onchange="userCodeChange"
            value="{{userCode}}"
          />
          <text class="login-code-label" @click="getUserCode">{{
            codeLabel > 0 ? codeLabel + 'S' : codeLabel
          }}</text>
        </div>
        <!-- 登录 -->
        <text
          class="{{isAgreePrivacy ? 'login-btn':'login-btn dis'}}"
          @click="login"
          >登录</text
        >
        <!-- 隐私政策 -->
        <div class="login-privacy-section">
          <div>
            <image
              @click="privacyIsSelected"
              class="login-privacy-icon"
              src="{{ isAgreePrivacy ? 'https://img.qdreads.com/v163/user/selected.png' : 'https://img.qdreads.com/v163/user/unselected.png'}}"
            >
            </image>
          </div>
          <text>已阅读并同意</text>
          <text @click="privacyLink(1)">《用户协议》</text>
          <text @click="privacyLink(2)">《隐私政策》</text>
        </div>
      </div>
    </div>
    <!-- 返回腾讯系App的button -->
    <back-app-button
      if="{{$app.$def.isTencentBackButtonVisible}}"
      btn-text="{{$app.$def.tencentBackName}}"
      back-url="{{$app.$def.tencentBackUrl}}"
      package-name="{{$app.$def.tencentBackPkg}}"
    ></back-app-button>
  </div>
</template>
<import name="back-app-button" src="../../components/back-app-button"></import>

<script>
import device from '@system.device'
export default pageMixin({
  data: () => ({
    pathUrl: '',  // pageUrl
    phone: '',  // 手机号
    userCode: '', // 验证码
    codeLabel: "获取验证码", // 验证码lable
    isAgreePrivacy: false, // 是否同意隐私政策  默认不选中
    pageDetail: {
      pageUrl: '登录页',
      pageName: '登录页',
      pageCode: 'READ_LOGIN',
      pageOrigin: ''
    }
  }),
  onInit() {
    
  },
  onShow() {
    // 更新页面来源
    this.pathUrl = curObj.pathUrl
    
    
  },
  onHide() {
    
  },
  /**
   * 手机输入-chagne回调
   */
  phoneChange(data) {
    this.phone = data.value;
    if (this.phone.length == 11) {
      COMMON_REPORT_UTILS.page_click_report('输入手机号') //点击上报
    }
  },
  /**
   * 清除手机号
   */
  clearPhone() {
    this.phone = "";
  },
  /**
   * 验证码输入 -change回调
   */
  userCodeChange(data) {
    this.userCode = data.value;
    if (this.userCode.length == 4) {
      this.$element('inputCode').focus({ focus: false })
    }
  },
  // 获取验证码
  getUserCode() {
    if (!$utils.phone(this.phone)) {
      const TIME_COUNT = 60;
      if (!this.timer) {
        COMMON_REPORT_UTILS.page_click_report('获取验证码') //点击上报
        this.codeLabel = TIME_COUNT;
        this.timer = setInterval(() => {
          if (this.codeLabel > 0 && this.codeLabel <= TIME_COUNT) {
            this.codeLabel--;
          } else {
            this.codeLabel = "获取验证码"
            clearInterval(this.timer);
            this.timer = null;
          }
        }, 1000)
      }
      if (this.codeLabel == TIME_COUNT) {
        $apis.example.getCodeApi({
          phone: this.phone,
        }).then((res) => {
          if (res.code == 200) {
          }
        })
      }
    } else {
      $utils.showToast("请输入正确的手机号码！", 0)
    }
  },
  /**
  * 隐私政策  是否选中
  */
  privacyIsSelected() {
    this.isAgreePrivacy = !this.isAgreePrivacy
    COMMON_REPORT_UTILS.page_click_report('已阅读并同意隐私政策和用户协议') //点击上报
  },
  /**
   * 跳转隐私政策
   * @param i 1:用户协议  2：隐私政策
   */
  privacyLink(i) {
    $utils.routetheUrl('/pagesB/Privacy', { type: i }, false)
  },
  /**
   * 用户登录
   */
  login() {
    if (!$utils.phone(this.phone)) {
      if (!this.phone) {
        $utils.showToast("请输入正确的手机号码！", 0);
        return;
      }       
      if (!this.userCode) {
        $utils.showToast("请输入验证码！", 0);
        return;
      }
      if (this.isAgreePrivacy) {
        COMMON_REPORT_UTILS.page_click_report('登录') //点击上报
        $apis.example.register({
          androidId: this.$app.$def.android,
          phone: this.phone,
          validate: this.userCode,
          sex: this.$app.$def.sex,
          channelId: this.$app.$def.channelId,
          versionCode: this.$app.$def.versionCode,
          versionName: this.$app.$def.versionName,
          brand: this.$app.$def.brand
        }).then(res => {
          var code = res.code
          if (code === 200) {
            let userInfo = res.data
            this.$app.$def.auditDesk = userInfo.auditDesk
            this.$app.$def.isPay = userInfo.isPay
            this.$app.$def.loginStatus = userInfo.loginStatus
            $utils.setStorage('token', userInfo.online_token)
            $utils.setStorage('userInfo', userInfo)
            this.$app.$def.tabListType = [0, 0, 0, 0, 0]

            if(!this.$page.query.isNormalBack){
              $utils.routetheUrl('/pagesA/Main', { selectIndex: 4, pathUrl: "登录页" }, true)
            }else{
              $utils.goBack()
            }
            // $utils.clear()
          } else if (code === 4041003) {
            $utils.showToast(res.msg, 0)
          } else if (code === 4041001) {
            $utils.showToast(res.msg, 0)
          }
        })
      } else {
        $utils.showToast("请先勾选用户协议和隐私政策", 0);
      }
    } else {
      $utils.showToast("请输入正确的手机号码！", 0);
    }
  },
  /**
   *  页面回退
   */
  pageBack() {
    COMMON_REPORT_UTILS.page_click_report('返回') //点击上报
    $utils.goBack()
  },
  onBackPress() {
    COMMON_REPORT_UTILS.back_click_report('','','跳转页面')
  }
})
</script>

<style lang="less">
@import './index.less';
</style>
