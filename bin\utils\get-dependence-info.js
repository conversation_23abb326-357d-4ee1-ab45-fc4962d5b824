const { execSync } = require('child_process')

// 获取依赖包本地 commit hash值
function getLocalCommitId(dependence) {
  try {
    // 执行命令并获取输出
    const output = execSync(`npm list ${dependence}`, { encoding: 'utf-8' })

    // 正则表达式匹配 commit ID（#后面的部分）
    const commitIdMatch = output.match(/#([a-f0-9]{7,40})/)

    if (commitIdMatch) return commitIdMatch[1]
    return ''
  } catch (error) {
    console.error('Error executing command:', error.message)
  }
}

async function fetchSDKGitLabCommitHash(SDKVersion) {
  try {
    const response = await fetch(
      `https://gitlab.ghfkj.cn/api/v4/projects/112/repository/branches/release-v${SDKVersion}`,
      {
        headers: {
          'PRIVATE-TOKEN': '**************************'
        }
      }
    )
    const data = await response.json()

    return data.commit.id
  } catch (e) {
    console.log(e)
    return ''
  }
}

async function fetchInterfaceGitLabCommitHash() {
  try {
    const response = await fetch(`https://gitlab.ghfkj.cn/api/v4/projects/143/repository/branches/master`, {
      headers: {
        'PRIVATE-TOKEN': '**************************'
      }
    })
    const data = await response.json()

    return data.commit.id
  } catch (e) {
    console.log(e)
    return ''
  }
}

module.exports = { getLocalCommitId, fetchSDKGitLabCommitHash, fetchInterfaceGitLabCommitHash }
