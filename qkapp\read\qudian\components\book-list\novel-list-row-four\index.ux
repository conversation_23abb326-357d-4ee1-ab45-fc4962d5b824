<template>
  <!-- 4+4横板排列 -->
    <div class="outer-wrapper">
      <div style="margin:0 30px;">
        <stack>
          <div class="title-underline-{{ itemType }}"></div>
          <text class="title-desc">{{ title }}</text>
        </stack>
      </div>
      <div style="flex-wrap:wrap;margin:0 23px;">
        <div for="{{(index, item) in subList}}" style="margin-left: {{index % 4 == 0 ? '0' : '24'}}px;width:142px;
          margin-top: 45px;flex-direction:column;" @click="compClickHandler(item)">
          <stack>
            <image src="https://img.qdreads.com/v173/sjyy_dcc.png" style="width:142px;height:189px;margin-top:7px;"></image>
            <div style="margin-left:7px;"> 
              <image
                src="{{item.bookIconUrl}}"
                style="object-fit: fill;width:128px;height:181px;border-radius: 6px;"
              ></image>
              <!-- <book-status xstype="{{item.xstype}}"></book-status> -->
            </div>
          </stack>
          <div style="height:76px;margin-top:10px;align-items:flex-start;">
            <text style="color: #333333;font-size: 26px;line-height: 36px;lines: 2;text-overflow: ellipsis;margin-left:7px;">{{item.bookName}}</text>
          </div>
          <block>
            <text if="{{itemType == 'vip'}}" class="title-book-type">{{item.bookType}}</text>
            <text else style="color: #ff3d4f;font-size: 22px;height: 22px;margin-top: 10px;flex-shrink: 0;margin-left:7px;">{{ item.score | formatScore }}分</text>
          </block>
        </div>
      </div>
    </div>
</template>

<!-- <import name="book-status" src="../book-status/index.ux"></import> -->

<script>
export default {
  props: {
    novelList: {
      type: Array,
      default: []
    },
    title: {
      type: String,
      default: ''
    },
    itemType:{
      type:String,
      default:'default'
    }
  },
  data: {

  },
  computed: {
    subList() {
      return this.novelList.slice(0, 8)
    }
  },
  compClickHandler(item) {
    if(!CLICK_UTILS.dom_click_vali_shake(`novelListRowFour_${this.__id__}`,500)) return
    if (item === '') return;
    this.$emit('compClick', { bookId: item.bookId ,bookName: item.bookName })
  },
  formatScore(value) {
    if (!value) return ''
    return value / 10
  },
}
</script>

<style lang="less">
.outer-wrapper {
  width: 690px;
  background-color: #ffffff;
  border-radius: 24px;
  flex-direction: column;
  padding: 30px 0;
}

.title-desc {
  height: 36px;
  width: 146px;
  font-size: 36px;
  font-weight: 600;
  color: #333333;
}
.title-book-type {
  width: 100%;
  height: 22px;
  font-size: 22px;
  color: #adadad;
  lines: 1;
  text-overflow: ellipsis;
  margin-top: 10px;
  margin-left:7px;
}

.title-underline-default {
  background: linear-gradient(90deg,#ffd1d1 0%, #ffffff 100%);
  background-repeat: no-repeat;
  height: 8px;
  width: 146px;
  margin-top: 28px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}
.title-underline-vip {
  background: linear-gradient(90deg, #e6b598, #f4dcc2 100%);
  background-repeat: no-repeat;
  height: 8px;
  width: 146px;
  margin-top: 28px;
  border-top-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

.title-underline-male {
    background: linear-gradient(90deg,#B0D9FF 0%, #ffffff 100%);
    background-repeat: no-repeat;
    height: 8px;
    width: 146px;
    margin-top: 28px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}
</style>

