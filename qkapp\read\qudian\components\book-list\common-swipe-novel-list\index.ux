<template>
  <!-- template里只能有一个根节点 -->
  <!-- 上面一个大的横向排列，底下4个小的 -->
  <div class="store-novel-list-type-1" @appear="startAnimation">
    <div class="list-title">
      <stack>
        <div class="title-underline-{{ itemType }}"></div>
        <text class="title-desc">{{ title }}</text>
      </stack>
    </div>
    <div
      @appear="emitAppaear"
      if="{{recommandBooks.length}}"
      class="list-novel-type-1"
      @click="firstItemClick"
    >
      <stack style="margin-right: 33px; width: 142px; height: 196px">
        <stack>
          <image
            src="https://img.qdreads.com/v173/sjyy_dcc.png"
            style="width: 142px; height: 189px; margin-top: 7px;"
          ></image>
          <div style="margin-left:7px;margin-right: 7px;width:128px;height:181px;">
            <image
              src="{{recommandBooks[0].bookIconUrl}}"
              style="
                object-fit: cover;
                width: 128px;
                height: 181px;
                border-radius: 6px;
              "
            ></image>
            <!-- <book-status xstype="{{recommandBooks[0].xstype}}"></book-status> -->
          </div>
        </stack>
        <stack
          for="(index, item) in reverseRecommandBooks"
          style="width: 128px;height: 181px;margin-left:7px;margin-right: 7px;"
        >
          <div style="background-color:#ffffff;border-radius: 6px;" id="loop-{{item.bookId}}">
            <image
              src="{{item.bookIconUrl}}"
              style="
                object-fit: cover;
                width: 128px;
                height: 181px;
                border-radius: 6px;
              "
            ></image>
            <!-- <book-status xstype="{{item.xstype}}"></book-status> -->
          </div>
        </stack>
      </stack>
      <div class="novel-detail">
        <div class="nd-name-wrapper">
          <text class="novel-name">{{ recommandBooks[0].bookName }}</text>
          <image
            src="https://img.qdreads.com/v173/sc_hm.png"
            style="width: 26px; height: 26px; flex-shrink: 0; margin-top: 4px"
          ></image>
          <div style="align-items: flex-end; flex-shrink: 0; margin-left: 8px">
            <text class="ly-ily" if="{{recommandBooks[0].score}}">{{
              recommandBooks[0].score | formatScore
            }}</text>
            <text
              style="
                color: #ff3d4f;
                font-size: 22px;
                height: 22px;
                margin-bottom: 2px;
                margin-left: 4px;
              "
              >分</text
            >
          </div>
        </div>
        <text class="novel-desc">{{
          recommandBooks[0].bookContenUrl.replace('\r\n', '')
        }}</text>
        <div class="novel-author">
          <text style="height: 22px; font-size: 22px; color: #c8ab80">{{
            recommandBooks[0].fcate
          }}</text>
          <div
            style="
              width: 4px;
              height: 4px;
              border-radius: 50%;
              background-color: #adadad;
              margin: 2px 4px 0;
            "
          ></div>
          <text style="height: 22px; font-size: 22px; color: #adadad"
            >{{ recommandBooks[0].bookNum | readNumHandle }}人阅读</text
          >
        </div>
      </div>
    </div>
    <div class="novel-list">
      <div
        style="width: 142px"
        class="type-1-novel-item {{i === 0 ? 'margin-ledt-0' : ''}}"
        for="(i, novelItem) in subList"
        @click="compClickHandler(novelItem)"
      >
        <stack>
          <image
            src="https://img.qdreads.com/v173/sjyy_dcc.png"
            style="width: 142px; height: 189px; margin-top: 7px"
          ></image>
          <div style="margin-left: 7px">
            <image
              src="{{novelItem.bookIconUrl}}"
              style="
                object-fit: fill;
                width: 128px;
                height: 181px;
                border-radius: 6px;
              "
            ></image>
            <!-- <book-status xstype="{{novelItem.xstype}}"></book-status> -->
          </div>
        </stack>
        <text class="type-1-name">{{ novelItem.bookName }}</text>
        <text class="type-1-score" if="{{novelItem.score}}"
          ><span style="font-size: 30px; font-weight: bold">{{
            novelItem.score | formatScore
          }}</span
          ><span style="font-size: 20px"> 分</span></text
        >
      </div>
    </div>
  </div>
</template>

<!-- <import name="book-status" src="../book-status/index.ux"></import> -->

<script>
export default {
  data() {
    return {
      recommandBooks: [],
      reverseRecommandBooks: [],
      subList: [],
      hasRecommand: false,
      timer: '',
      appearTimes: 0,
    }
  },
  props: {
    novelList: {
      type: Array,
      default: []
    },
    title: {
      type: String,
      default: ''
    },
    itemType: {
      type: String,
      default: 'default'
    }
  },
  onInit() {
    let bookArr = this.novelList.filter(item => item.isCarousel == 1)
    if (bookArr.length > 0) {
      this.hasRecommand = true
      this.recommandBooks = bookArr
      for (let index = bookArr.length - 1; index >= 0; index--) {
        this.reverseRecommandBooks.push(bookArr[index])
        /* this.reverseRecommandBooks.forEach((element) => {
          element.isShow = true
        }) */
      }
      // this.reverseRecommandBooks = [].concat(bookArr).reverse()
      LOG('reverseRecommandBooks', this.reverseRecommandBooks)
      this.subList = this.novelList.filter(item => item.isCarousel == 0).slice(0, 4)
    } else {
      this.recommandBooks.push(this.novelList[0])
      this.reverseRecommandBooks.push(this.novelList[0])
      // this.reverseRecommandBooks[0].isShow = true
      this.subList = this.novelList.slice(1, 5)
    }
  },
  onReady() {
    
  },
  onDestroy() {
    LOG('onDestroy playAnimations')
    // clearInterval(this.timer)
    // this.timer = ''
  },
  startAnimation() {
    if (this.appearTimes > 0) return
    this.appearTimes ++
    if (this.recommandBooks.length > 1) {
      this.playAnimations()
    }
  },
  compClickHandler(item) {
    if (!CLICK_UTILS.dom_click_vali_shake(`swipeNovelList_${this.__id__}`, 500)) return
    this.$emit('compClick', { bookId: item.bookId, bookName: item.bookName })
  },
  toRecommandPage() {
    if (!CLICK_UTILS.dom_click_vali_shake(`toRecommandPage_${this.__id__}`, 500)) return
    let bookIds = []
    this.recommandBooks.forEach(item => bookIds.push(item.bookId))
    this.$emit('toRecommandPage', { bookIds: bookIds.join(',') })
  },
  firstItemClick() {
    if (this.hasRecommand) {
      this.toRecommandPage()
    } else {
      this.compClickHandler(this.recommandBooks[0])
    }
  },
  formatScore(value) {
    if (!value) return ''
    return value / 10
  },
  //阅读人数处理 万级 处理成小数点
  readNumHandle(readNum) {
    if (Number(readNum) > 10000) {
      return (Number(readNum) / 10000).toFixed(1) + '万'
    }
    return readNum
  },
  playAnimations() {
    for (let index = 0; index < this.reverseRecommandBooks.length; index ++) {
      let element = this.reverseRecommandBooks[index], length = this.reverseRecommandBooks.length
      if (element.animation || element.animationEnd) {
        element.animation && element.animation.cancel()
        element.animation = null
        element.animationEnd && element.animationEnd.cancel()
        element.animationEnd = null
      }
      // let duration = length * 600 + 5000
      // let endTime = 600 / duration * 100
      let anOptions = {
        keyFrames: [
          {
            transform: { translateX: '0px' },
            time: 0,
            opacity: 1
          },
          {
            transform: { translateX: '-128px' },
            time: 99,
            opacity: 1
          },
          {
            time: 100,
            opacity: 0
          }
        ],
        options: {
          duration: 1000,
          easing: 'linear',
          fill: 'forwards',
          delay: (length - index - 1) * 1000,
          iterations: 1
        },
        elementId: "loop-" + element.bookId,
      }
      let cAnimationNode = this.$element(anOptions.elementId)
      element.animation = cAnimationNode.animate(anOptions.keyFrames, anOptions.options)
      element.animation.play()
    }
    let timer1 = setTimeout(() => {
      this.animationEnd()
      clearTimeout(timer1)
    }, 1100 * this.reverseRecommandBooks.length)
  },
  animationEnd() {
    for (let index = 0; index < this.reverseRecommandBooks.length; index++) {
      let element = this.reverseRecommandBooks[index], length = this.reverseRecommandBooks.length
      if (element.animation) {
        let anOptions1 = {
          keyFrames: [
            {
              time: 0,
              transform: { translateX: '0px' },
              opacity: 0
            },
            {
              time: 99,
              transform: { translateX: '0px' },
              opacity: 0
            },
            {
              time: 100,
              opacity: 1
            }
          ],
          options: {
            duration: 0,
            easing: 'linear',
            fill: 'forwards',
            delay: (length - index - 1) * 50,
            iterations: 1
          },
          elementId: "loop-" + element.bookId,
        }
        let cAnimationNode1 = this.$element(anOptions1.elementId)
        element.animationEnd = cAnimationNode1.animate(anOptions1.keyFrames, anOptions1.options)
        element.animationEnd.play()
      }
    }
    let timer2 = setTimeout(() => {
      this.playAnimations()
      clearTimeout(timer2)
    }, 5000)
  }
}
</script>

<style lang="less">
@import './index.less';
</style>
