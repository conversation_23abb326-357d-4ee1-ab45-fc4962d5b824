const path = require('path')
const { projectPath } = require('../constant')
const { consoleSplit } = require('../../utils/console')
const packageJson = require(path.resolve(projectPath, 'package.json'))
const colors = require('colors')
const { fetchInterfaceGitLabCommitHash, getLocalCommitId } = require('../../utils/get-dependence-info')
const { execSync } = require('child_process')
async function checkInterfaceVersion() {
  if (packageJson.name === 'ad-interface') {
    return true
  }
  const interfaceCommitHash = getLocalCommitId('ad-interface')
  const lastCommitHash = await fetchInterfaceGitLabCommitHash()
  const isSame = interfaceCommitHash === lastCommitHash

  console.log('对接层本地commithash', interfaceCommitHash, '远程commithash', lastCommitHash)
  if (!isSame) {
    console.log(colors.red('对接层版本不一致，请更新对接层，执行以下命令\nnpm update ad-interface'))
  } else {
    console.log('对接层 已更新到最新版本')
  }

  return isSame
}

function updateAdInterface() {
  console.log(colors.red('正在执行更新命令：npm update ad-interface'))
  execSync('npm update ad-interface', { stdio: 'inherit' })
}

async function checkVersion() {
  consoleSplit('检查对接版本')
  const result = await checkInterfaceVersion()
  if (!result) {
    updateAdInterface()
    // process.exit()
  }
}

module.exports = { checkVersion }
