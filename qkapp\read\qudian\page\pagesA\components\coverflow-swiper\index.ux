<template>
  <div class="outer-wrapper">
    <!-- <swiper
      style="width: 100%; height: 100%"
      indicator="{{false}}"
      @change="swiperChangeHandle"
    >
      <div for="{{(index, item) in novelList}}" style="justify-content: center">
        <div
          @click="compClick<PERSON><PERSON><PERSON>(item)"
          style="
            width: 690px;
            height: 272px;
            border-radius: 24px;
            background-image: url(https://img.qdreads.com/v163/sc_gftj.png);
            padding: 30px;
            justify-content: space-between;
          "
        >
          <div style="flex-direction: column; width: 422px">
            <text
              style="
                height: 26px;
                font-size: 26px;
                color: rgba(255, 255, 255, 0.5);
              "
            >
              {{ title }}
            </text>
            <text
              style="
                height: 32px;
                font-size: 32px;
                font-weight: bold;
                color: #ffffff;
                margin-top: 30px;
                lines: 1;
                text-overflow: ellipsis;
              "
            >
              {{ item.bookContenUrl }}
            </text>
            <text
              style="
                height: 28px;
                font-size: 28px;
                font-weight: bold;
                color: #ffffff;
                lines: 1;
                text-overflow: ellipsis;
                margin-top: 56px;
              "
            >
              {{ item.bookName }}
            </text>
            <text
              style="
                height: 24px;
                font-size: 24px;
                color: #cfcdd7;
                lines: 1;
                text-overflow: ellipsis;
                margin-top: 20px;
              "
            >
              {{ item.bookAuthor }}·{{ item.bookType }}
            </text>
          </div>
          <image
            style="width: 128px; height: 181px; border-radius: 6px"
            src="{{item.bookIconUrl}}"
          ></image>
          <div class="indi-wrapper">
            <block for="{{(index, item) in novelList}}">
              <div
                if="{{swiperIndex != index}}"
                class="indicator"
                style="margin-left: 8px"
              ></div>
              <block else>
                <div style="margin-left: 8px">
                  <div
                    style="
                      width: 4px;
                      height: 6px;
                      border-top-left-radius: 100%;
                      background-color: #ffffff;
                    "
                  ></div>
                  <div
                    style="width: 8px; height: 6px; background-color: #ffffff"
                  ></div>
                  <div
                    style="
                      width: 4px;
                      height: 6px;
                      border-bottom-right-radius: 100%;
                      background-color: #ffffff;
                    "
                  ></div>
                </div>
              </block>
            </block>
          </div>
        </div>
      </div>
    </swiper> -->
    <div style="margin:40px 0 0 30px;">
      <stack>
        <div class="title-underline-default"></div>
        <text class="title-desc">{{ title }}</text>
      </stack>
    </div>
    <text style="margin-top:20px;height: 26px;font-size: 26px;color: rgba(153,153,153,0.50);margin-left:30px;">平凡的日常中的史诗级瞬间</text>
      <list style="width:100%;height:264px;flex-direction:row;margin-top:32px;" @scroll="onListScroll">
      <list-item type="book" for="{{(index, item) in novelList}}" 
        @click="compClickHandler(item)"
        style="flex-direction:column;"
        class="{{(scrollXPosition>=(index*133))&&(((index+1)*133)>scrollXPosition)?'bigger-wrapper':'normal-wrapper'}}"
      >
          <stack style="border-radius: 6px;width:124px;height:176px;align-items:center;justify-content:center;">
              <image
                class="normal-item {{(scrollXPosition>=(index*133))&&(((index+1)*133)>scrollXPosition)?'bigger-item':''}}"
                src="{{item.bookIconUrl}}"
                style="border-radius: 6px;"
              ></image>
              <div class="bigger-tag {{(scrollXPosition>=(index*133))&&(((index+1)*133)>scrollXPosition)?'':'normal-tag'}}">
                <!-- <book-status xstype="{{item.xstype}}"></book-status> -->
              </div>
          </stack>
        <text 
          style="width: 103px;height: 74px;font-size: 26px;color: #333333;line-height: 37px;margin-top:10px;lines:2;text-overflow:ellipsis;"
        >{{item.bookName}}</text>
      </list-item>
      <list-item type="empty-right" style="width:30px;height:2px;"></list-item>
    </list>
    <div class="left-border"></div>
    <div class="right-border"></div>
  </div>
</template>

<!-- <import name="book-status" src="../../../components/book-list/book-status/index.ux"></import> -->

<script>
  export default {
    props: {
      novelList: {
        type: Array,
        default: []
      },
      title: {
        type: String,
        default: ''
      },
    },
    data: {
      swiperIndex: 0,
      listScrollX: 0,
      scrollXPosition: 0,
    },
    computed: {

    },
    compClickHandler(item) {
      if(!CLICK_UTILS.dom_click_vali_shake(`coverFlowSwiper_${this.__id__}`,500)) return
      if (item === '') return;

      this.$emit('compClick', { bookId: item.bookId, bookName: item.bookName })
    },
    swiperChangeHandle({ index }) {
      this.swiperIndex = index
    },
    onListScroll(evt) {
      this.listScrollX += evt.scrollX
      this.scrollXPosition = Math.max(this.listScrollX, 0)
    }
  }
</script>

<style lang="less">
  .outer-wrapper {
    width: 690px;
    height: 448px;
    background-color: #ffffff;
    border-radius: 24px;
    flex-direction: column;
  }

  .indicator-selected {
  }

  .indicator {
    background-color: #ffffff;
    border-radius: 50%;
    width: 6px;
    height: 6px;
  }

  .indi-wrapper {
    position: absolute;
    right: 30px;
    bottom: 30px;
    /* width: 100px; */
    height: 8px;
  }

  .bigger-item {
    transform: scale(1.2)
  }

  .normal-item {
    width: 103px;
    height: 146px;
  }

  .item-basic {
    transition-property: width;
    transition-property: height;
    transition-duration: 300ms;
    transition-timing-function: ease-in;
  }

  .bigger-tag {
    position: absolute;
    top: 0;
    right: 0;
    width:48px;
    height:24px;
  }

  .normal-tag {
    top: 14px;
    right: 10px;
  }

  .normal-wrapper {
    margin-left: 20px;
  }

  .bigger-wrapper {
    margin-left: 30px;
    margin-right: 10px;
  }

.title-underline-default {
  background: linear-gradient(90deg,#ffd1d1 0%, #ffffff 100%);
  background-repeat: no-repeat;
  height: 8px;
  width: 146px;
  margin-top: 28px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.title-desc {
  height: 36px;
  width: 146px;
  font-size: 36px;
  font-weight: 600;
  color: #333333;
}

.right-border {
  position: absolute;
  right: 0;
  bottom: 20px;
  width: 30px;
  height: 400px;
  background: linear-gradient(90deg,rgba(255,255,255,0) 0%, #ffffff 100%);
}

.left-border {
  position: absolute;
  left: 0;
  bottom: 20px;
  width: 30px;
  height: 400px;
  background: linear-gradient(90deg,#ffffff 0%, rgba(255,255,255,0) 100%);
}
</style>

