<template>
  <!-- 1个横板排列 -->
    <div class="outer-wrapper" @appear="startAnimation">
      <div style="margin:0 30px;">
        <stack>
          <div class="title-underline-{{ itemType }}"></div>
          <text class="title-desc">{{ title }}</text>
        </stack>
      </div>
      <div
        style="margin-top:33px;"
        class="list-novel-type-1"
        if="{{recommandBooks.length}}"
        @click="toRecommandPage"
      >
        <stack style="margin-right:33px;width:142px;height:196px;">
          <stack style="width:142px;">
            <image src="https://img.qdreads.com/v173/sjyy_dcc.png" style="width:142px;height:189px;margin-top:7px;"></image>
            <div style="margin-left:7px;margin-right: 7px;width:128px;height:181px;">
              <image
                src="{{recommandBooks[0].bookIconUrl}}"
                style="object-fit: cover;width:128px;height:181px;border-radius: 6px;"
              ></image>
              <!-- <book-status xstype="{{recommandBooks[0].xstype}}"></book-status> -->
            </div>
          </stack>
          <stack for="(index, item) in reverseRecommandBooks" 
            style="width: 128px;height: 181px;margin-left:7px;margin-right: 7px;"
          >
            <div style="background-color:#ffffff;border-radius: 6px;" id="loop-{{item.bookId}}"> 
              <image
                src="{{item.bookIconUrl}}"
                style="object-fit: cover;width:128px;height:181px;border-radius: 6px;"
              ></image>
              <!-- <book-status xstype="{{item.xstype}}"></book-status> -->
            </div>
          </stack>
        </stack>
        <div class="novel-detail">
          <div class="nd-name-wrapper">
            <text class="novel-name">{{ recommandBooks[0].bookName }}</text>
            <div style="align-items: flex-end;flex-shrink: 0;margin-left:10px;">
              <text class="ly-ily" if="{{recommandBooks[0].score}}"
                >{{ recommandBooks[0].score | formatScore }}</text
              >
              <text style="color: #ff3d4f;font-size: 22px;height: 22px;margin-bottom:2px;margin-left:4px;">分</text>
            </div>
          </div>
          <text class="novel-desc">{{ recommandBooks[0].bookContenUrl.replace('\r\n','') }}</text>
          <div class="novel-author">
            <text style="height: 22px;font-size: 22px;color: #c8ab80;">{{recommandBooks[0].fcate}}</text>
            <div style="width:4px;height:4px;border-radius:50%;background-color:#adadad;margin:2px 4px 0;"></div>
            <text style="height: 22px;font-size: 22px;color: #adadad;">{{ recommandBooks[0].bookNum | readNumHandle }}人阅读</text>
          </div>
        </div>
      </div>
      <div
        class="list-novel-type-1"
        for="{{(index, item) in subList}}"
        @click="compClickHandler(item)"
      >
        <stack style="margin-right:33px;width:142px;height:196px;">
          <image src="https://img.qdreads.com/v173/sjyy_dcc.png" style="width:142px;height:189px;margin-top:7px;"></image>
          <div style="margin-left:7px;"> 
            <image
              src="{{item.bookIconUrl}}"
              style="object-fit: fill;width:128px;height:181px;border-radius: 6px;"
            ></image>
            <!-- <book-status xstype="{{item.xstype}}"></book-status> -->
          </div>
        </stack>
        <div class="novel-detail">
          <div class="nd-name-wrapper">
            <text class="novel-name">{{ item.bookName }}</text>
            <div style="align-items: flex-end;flex-shrink: 0;margin-left:10px;">
              <text class="ly-ily" if="{{item.score}}"
                >{{ item.score | formatScore }}</text
              >
              <text style="color: #ff3d4f;font-size: 22px;height: 22px;margin-bottom:2px;">分</text>
            </div>
          </div>
          <text class="novel-desc">{{ item.bookContenUrl.replace('\r\n','') }}</text>
          <div class="novel-author">
            <text style="height: 22px;font-size: 22px;color: #c8ab80;">{{item.fcate}}</text>
            <div style="width:4px;height:4px;border-radius:50%;background-color:#adadad;margin:2px 4px 0;"></div>
            <text style="height: 22px;font-size: 22px;color: #adadad;">{{ item.bookNum | readNumHandle }}人阅读</text>
          </div>
        </div>
      </div>
    </div>
</template>

<!-- <import name="book-status" src="../book-status/index.ux"></import> -->

<script>
export default {
  props: {
    novelList: {
      type: Array,
      default: []
    },
    title: {
      type: String,
      default: ''
    },
    itemType:{
      type:String,
      default:'default'
    }
  },
  data() {
    return {
      recommandBooks: [],
      reverseRecommandBooks: [],
      subList: [],
      timer: '',
      appearTimes: 0,
    }
  },
  onInit() {
    let bookArr = this.novelList.filter(item => item.isCarousel == 1)
    if (bookArr.length > 0) {
      this.recommandBooks = bookArr
      for (let index = bookArr.length - 1; index >= 0; index --) {
        this.reverseRecommandBooks.push(bookArr[index])
        /* this.reverseRecommandBooks.forEach((element) => {
          element.isShow = true
        }) */
      }
      this.subList = this.novelList.filter(item => item.isCarousel == 0)
    } else {
      this.subList = this.novelList
    }
  },
  onReady() {
    
  },
  onDestroy() {
    LOG('onDestroy row one')
  },
  startAnimation() {
    if (this.appearTimes > 0) return
    this.appearTimes ++
    if (this.recommandBooks.length > 1) {
      this.playAnimations()
    }
  },
  compClickHandler(item) {
    if (item === '') return;
    this.$emit('compClick', { bookId: item.bookId ,bookName: item.bookName })
  },
  toRecommandPage() {
    if(!CLICK_UTILS.dom_click_vali_shake(`toRecommandPage_${this.__id__}`,500)) return
    let bookIds = []
    this.recommandBooks.forEach(item => bookIds.push(item.bookId))
    this.$emit('toRecommandPage', { bookIds: bookIds.join(',') })
  },
  formatScore(value) {
    if (!value) return ''
    return value / 10
  },
    //阅读人数处理 万级 处理成小数点
  readNumHandle(readNum) {
    if (Number(readNum) > 10000) {
      return (Number(readNum) / 10000).toFixed(1) + '万'
    }
    return readNum
  },
  playAnimations() {
    for (let index = 0; index < this.reverseRecommandBooks.length; index ++) {
      let element = this.reverseRecommandBooks[index], length = this.reverseRecommandBooks.length
      if (element.animation || element.animationEnd) {
        element.animation && element.animation.cancel()
        element.animation = null
        element.animationEnd && element.animationEnd.cancel()
        element.animationEnd = null
      }
      // let duration = length * 600 + 5000
      // let endTime = 600 / duration * 100
      let anOptions = {
        keyFrames: [
          {
            transform: { translateX: '0px' },
            time: 0,
            opacity: 1
          },
          {
            transform: { translateX: '-128px' },
            time: 99,
            opacity: 1
          },
          {
            time: 100,
            opacity: 0
          }
        ],
        options: {
          duration: 1000,
          easing: 'linear',
          fill: 'forwards',
          delay: (length - index - 1) * 1000,
          iterations: 1
        },
        elementId: "loop-" + element.bookId,
      }
      let cAnimationNode = this.$element(anOptions.elementId)
      element.animation = cAnimationNode.animate(anOptions.keyFrames, anOptions.options)
      element.animation && element.animation.play()
    }
    let timer1 = setTimeout(() => {
      this.animationEnd()
      clearTimeout(timer1)
    }, 1100 * this.reverseRecommandBooks.length)
  },
  animationEnd() {
    for (let index = 0; index < this.reverseRecommandBooks.length; index++) {
      let element = this.reverseRecommandBooks[index], length = this.reverseRecommandBooks.length
      if (element.animation) {
        let anOptions1 = {
          keyFrames: [
            {
              time: 0,
              transform: { translateX: '0px' },
              opacity: 0
            },
            {
              time: 99,
              transform: { translateX: '0px' },
              opacity: 0
            },
            {
              time: 100,
              opacity: 1
            }
          ],
          options: {
            duration: 0,
            easing: 'linear',
            fill: 'forwards',
            delay: (length - index - 1) * 50,
            iterations: 1
          },
          elementId: "loop-" + element.bookId,
        }
        let cAnimationNode1 = this.$element(anOptions1.elementId)
        element.animationEnd = cAnimationNode1.animate(anOptions1.keyFrames, anOptions1.options)
        element.animationEnd && element.animationEnd.play()
      }
    }
    let timer2 = setTimeout(() => {
      this.playAnimations()
      clearTimeout(timer2)
    }, 5000)
  }
  
}
</script>

<style lang="less">
.outer-wrapper {
  width: 690px;
  background-color: #ffffff;
  border-radius: 24px;
  flex-direction: column;
  padding: 30px 0;
}

.title-desc {
    height: 36px;
    width: 146px;
    font-size: 36px;
    font-weight: 600;
    color: #333333;
}

.title-underline-default {
    background: linear-gradient(90deg,#ffd1d1 0%, #ffffff 100%);
    background-repeat: no-repeat;
    height: 8px;
    width: 146px;
    margin-top: 28px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}
.title-underline-vip {
    background: linear-gradient(90deg,#e6b598 0%, #f4dcc2 100%);
    background-repeat: no-repeat;
    height: 8px;
    width: 146px;
    margin-top: 28px;
    border-top-left-radius: 8px;
    border-bottom-right-radius: 8px;
}

.title-underline-male {
    background: linear-gradient(90deg,#B0D9FF 0%, #ffffff 100%);
    background-repeat: no-repeat;
    height: 8px;
    width: 146px;
    margin-top: 28px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}

.list-novel-type-1 {
        flex-direction: row;
        margin-top: 33px;
        padding: 0 23px;

        .novel-pic {
            width: 128px;
            height: 180px;
            object-fit: fill;
            border-radius: 6px;
        }
        .novel-detail {
            flex: 1;
            height: 180px;
            padding: 6px 0;
            flex-direction: column;

            .novel-name {
                height: 34px;
                font-size: 34px;
                color: #333333;
                lines: 1;
                text-overflow: ellipsis;
            }
            .novel-desc {
                line-height: 36px;
                font-size: 24px;
                color: #adadad;
                lines: 2;
                text-overflow: ellipsis;
                margin-top: 16px;
            }
            .novel-author {
                height: 22px;
                align-items: center;
                margin-top: 16px;
            }
            .nd-name-wrapper {
                justify-content: space-between;
                align-items: center;
                height: 40px;
                .ly-ily {
                    height: 38px;
                    font-size: 38px;
                    font-weight: bold;
                    color: #ff3d4f;
                    flex-shrink: 0;
                }
            }
        }
    }
</style>

