{"config": {"is_activity_allow_back_time": {"op": "is", "val": "1000"}, "bqt_btn_bgcolor": {"op": "is", "val": "#ff9900"}, "bqt_btn_width": {"op": "is", "val": "600"}, "bqt_btn_height": {"op": "is", "val": "100"}, "bqt_btn_font": {"op": "is", "val": "100"}, "c_wake_interstitial_id": {"op": "is", "val": ""}, "c_wake_video_id": {"op": "is", "val": ""}, "c_wake_times": {"op": "is", "val": "0"}, "c_ad_wake_sequence": {"op": "is", "val": "0"}, "c_is_open_wake": {"op": "is", "val": "0"}, "wake_self_times": {"op": "is", "val": "1"}, "is_reviewer": {"op": "is", "val": "2"}, "boost_group_ad_count": {"op": "is", "val": "2"}, "ad_btn_text_type": {"op": "is", "val": "1"}, "is_used_ad_button": {"op": "is", "val": "1"}, "max_show_times": {"op": "is", "val": "10000"}, "boost_request_type": {"op": "is", "val": "1"}, "back_app_button": {"op": "is", "val": "2"}, "is_show_desk_float": {"op": "is", "val": "1"}, "is_vivo_back_button": {"op": "is", "val": "2"}, "ads_show_interval_min": {"op": "is", "val": "2000"}, "is_bqt_can_show_dialog": {"op": "is", "val": "0"}, "native_ad_click_interval": {"op": "is", "val": "0"}, "text_ad_trigger_timing": {"op": "is", "val": "1"}, "lock_vivo_back_times": {"op": "is", "val": "0"}, "applicationInstall": {"op": "is", "val": "1"}, "view_advs_max_count": {"op": "is", "val": "2"}, "layer_request_enter_pool_interval_times": {"op": "is", "val": "3000"}, "lower_show_times": {"op": "is", "val": "500"}, "max_count_by_scene": {"op": "is", "val": "3"}, "time_difference": {"op": "is", "val": "1"}, "is_show_source": {"op": "is", "val": "1"}, "is_stop_exposure": {"op": "is", "val": "1"}, "use_data_pool_advertisers": {"op": "is", "val": ""}, "agd_abandon_probit": {"op": "is", "val": "100"}, "no_agd_abandon_probit": {"op": "is", "val": "100"}, "boost_request_loop_time": {"op": "is", "val": "10000"}, "boost_max_count": {"op": "is", "val": "1"}, "boost_delay_time": {"op": "is", "val": "1000"}, "ad_disable_in_xiaomi": {"op": "is", "val": "1"}, "action_entry_show": {"op": "is", "val": "0"}, "target_actId": {"op": "is", "val": ""}, "isBackAppButtonShow": {"op": "is", "val": "-1"}, "wz_ad_circle": {"op": "is", "val": "0"}, "action_ad_circle": {"op": "is", "val": "0"}, "main_ad_circle": {"op": "is", "val": "0"}, "read_ad_circle": {"op": "is", "val": "0"}, "detail_ad_circle": {"op": "is", "val": "0"}, "read_unlock_timer": {"op": "is", "val": ""}, "cartoon_id": {"op": "is", "val": "1"}, "chapter_id": {"op": "is", "val": "1"}, "cartoon_publish_id": {"op": "is", "val": "1,2,3,4,5,6,7,8,10"}, "isBdBackAppButtonShow": {"op": "is", "val": "0", "desc": "百度悬浮按钮是否展示"}}, "appCode": "jingyue", "configName": "默认本地配置数据"}