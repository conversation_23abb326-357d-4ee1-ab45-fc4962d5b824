.book-item {
    width: 100%;
    height: 144px;
    .book-pic {
        margin-right: 33px;
        object-fit: fill;
    }
    .book-item-detail {
        flex: 1;
        flex-direction: column;
        padding: 6px 0;
    }
    .book-name {
        height: 30px;
        font-size: 30px;
        color: #333333;
        lines: 1;
        text-overflow: ellipsis;
    }
    .book-desc {
        lines: 2;
        text-overflow: ellipsis;
        margin-top: 18px;
        margin-bottom: auto;
        font-size: 24px;
        color: #adadad;
        line-height: 36px;
    }
    .book-type {
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        .book-author {
            font-size: 22px;
            height: 22px;
            color: #adadad;
            margin-right: 24px;
        }
        .book-type-detail {
            height: 28px;
            padding: 0 7px;
            font-size: 20px;
            border-radius: 6px;
        }
        .novel-type-1{
            background-color: #edf9ff;
            color: #0989FF;
        }
        .novel-type-2{
            background-color: #F5FBE3;
            color: #8BB80C;
        }
        .novel-type-3{
            background-color: #F6F1FF;
            color: #9157FF;
        }
        .novel-type-4{
            background-color: #F5F5F5;
            color: #7C7C7C;
        }
        .novel-type-5{
            background-color: #FFF3F5;
            color: #FF5E5E;
        }
    }
}

