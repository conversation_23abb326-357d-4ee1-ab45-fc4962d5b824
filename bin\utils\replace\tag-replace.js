const { templateReplace } = require('./base/code-split')

function tagReplace({ content, oldCode, newCode, failCodeInfo, file, desc }) {
  const tagPathList = oldCode.split('.')
  // 有部分没有
  if (tagPathList.some(v => content.indexOf(v) === -1)) {
    return content
  }

  // 已经有新代码了，直接返回了
  if (newCode && content.indexOf(newCode) !== -1) {
    return content
  }

  const list = []
  const code = templateReplace(content, ast => {
    findNode(ast, oldCode, list)
    return list.map(({ start, end }) => ({ start, end, content: newCode, type: 'replace' }))
  })

  return code
}

function findNode(node, selector, result = []) {
  // 根据 tagName 检查节点
  const [tag, childTag] = selector.split('.')
  if (node.tagName === tag) {
    const preChild = node.childNodes?.find(child => child.tagName === childTag)
    if (preChild) {
      result.push({
        start: node.sourceCodeLocation.startOffset,
        end: node.sourceCodeLocation.endOffset
      })
    }
  }

  // 遍历子节点
  if (node.childNodes) {
    for (let child of node.childNodes) {
      findNode(child, selector, result)
    }
  }
}

module.exports = tagReplace
