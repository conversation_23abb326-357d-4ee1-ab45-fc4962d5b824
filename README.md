# 广告 SDK 对接层

文档：https://alidocs.dingtalk.com/i/nodes/XPwkYGxZV3yKYbjLUYZxqAlG8AgozOKL

## 开发

```
npm install
npm link
ad run
# 选择 趣点阅读 或者 猫眼漫画
```

## 支持的命令
```angular2html
# 业务对接
ad run

# 业务代码升级
ad up

# sdk 执行
ad sdk
```

### 参数
```
# 忽略 sdk 升级
ad up --ignoreSDK
```

## 文件结构

```angular2html
bin
├── actions # 命令执行入口
├── config # 所有配置文件
├── constants # 所有静态变量
├── index.js # 命令配置文件
├── main # run 命令逻辑
├── server # 简单web服务，支持对接信息输出
├── store # 文件存储
├── template # 配置模板
├── test # 代码逻辑自测
├── typings # 全局类型定义，供 jsDoc 使用
└── utils # 工具函数
```