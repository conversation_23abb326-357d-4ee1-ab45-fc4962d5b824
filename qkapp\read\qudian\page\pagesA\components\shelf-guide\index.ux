<template>
  <div class="shelf-guide" @click="closeGuide">
    <div class="guide-wrapper" style="top: {{560}}px;left:30px;">
      <div class="guide-first-step">
        <block if="{{book}}">
          <image
          class="shelf-boook-item-image"
          src="{{book.bookIconUrl}}"
        ></image>
        <!-- <div style="height: 128px; flex-direction: column;justify-content: space-between;"> -->
          <text class="shelf-boook-item-title">{{ book.bookName }}</text>
          <text class="shelf-boook-item-author">{{ book.bookAuthor }}</text>
        <!-- </div> -->
        </block>
        <block else>
          <div class="shelf-boook-item-image" style="background-color: #EFEFEF"></div>
          <div style="height: 128px; flex-direction: column;justify-content: space-between;">
          <text class="shelf-boook-item-title" style="background-color: #EFEFEF;height: 36px;"></text>
          <text class="shelf-boook-item-author" style="background-color: #EFEFEF;"></text>
        </div>
        </block>
      </div>
      <div style="align-items: center;margin-left: 20px;">
        <div style="flex-direction: column;">
          <div class="triangle1"></div>
          <div class="triangle2"></div>
        </div>
        <div style="width: 346px;height: 190px;background-color: #ffffff;
          border-radius: 14px;padding: 30px;flex-direction: column;justify-content: space-between;">
          <div style="align-items: center">
            <text style="height: 30px;font-size: 30px;font-weight: 600;color: #000000;">Hi!</text>
            <image style="width: 40px;height: 40px;" src="https://img.qdreads.com/v163/<EMAIL>"></image>
          </div>
          <text style="font-size: 24px;color: #000000;line-height: 36px;">添加到书架的书籍会在这里展示哦～</text>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data: {
    
  },
  
  props: ['book'],
  
  onInit() {},

  closeGuide() {
    this.$emit('closeGuide')
  }
}
</script>

<style>
.shelf-guide {
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.60);
    position: absolute;
}

.guide-wrapper {
    position: absolute;

}

.guide-first-step {
    width: 232px;
    height: 438px;
    background-color: #ffffff;
    border-radius: 12px;
    margin-left: 8px;
    padding: 20px;
    flex-direction: column;

}

.shelf-boook-item-image {
    width: 192px;
    height: 270px;
    border-radius: 6px;
}
.shelf-boook-item-del {
    width: 36px;
    height: 36px;
    position: absolute;
    bottom: 0px;
    right: 0px;
}
.shelf-boook-item-title {
    font-size: 26px;
    lines: 1;
    text-overflow: ellipsis;
    line-height: 36px;
    font-weight: 400;
    color: #333333;
    margin-top: 20px;
}
.shelf-boook-item-author {
    font-size: 24px;
    color: #adadad;
    font-weight: 400;
    margin-top: 10px;
    height: 24px;
    lines: 1;
    text-overflow: ellipsis;
}

.triangle1 {
  width: 20px;
  height: 15px;
  background-repeat: no-repeat;
  background:  linear-gradient(-37deg, #ffffff 50%, rgba(255, 255, 255, 0) 50%);
  background-size: 100% 50%;
}

.triangle2 {
  width: 20px;
  height: 15px;
  background-repeat: no-repeat;
  background:  linear-gradient(37deg, rgba(255, 255, 255, 0) 50%,  #ffffff 50%);
  background-size: 100% 50%;
}
</style>
