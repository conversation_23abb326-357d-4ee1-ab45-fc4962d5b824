<template>
  <div class="activity-wrapper" onclick="activityClick">
    <div
      style="width: 100%; height: 100%; justify-content: center"
      if="{{currentInfo}}"
    >
      <image class="activity-icon scale-down-up-center" src="https://img.qdreads.com/v155/images/{{currentInfo.icon}}"></image>
      <div class="activity-btn">
        <image style="width: 100%; height: 100%" src="https://img.qdreads.com/v155/images/{{currentInfo.btnIcon}}"></image>
        <image class="acticity-light translateX" src="https://img.qdreads.com/v155/images/welfare-activity-light.png"></image>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    activityType: {
      default: 'turntable',
      type: String
    }
  },
  data: {
    activityInfo: {
      turntable: {
        type: "turntable",
        icon: "welfare-turntable.png",
        btnIcon: 'welfare-turntable-btn.png'
      },
      shake: {
        type: "shake",
        icon: "welfare-shake.png",
        btnIcon: 'welfare-shake-btn.png'
      }

    },
    currentInfo: null
  },
  onInit() {
    if (this.activityInfo.hasOwnProperty(this.activityType)) {
      this.currentInfo = this.activityInfo[`${this.activityType}`]
    }
    if (!this.currentInfo) {
      console.error('活动类型异常，请检查')
    }
  },
  activityClick(evt) {
    evt.stopPropagation()
    this.$emit('eventWatch', { eventName: 'activityClick', data: this.currentInfo })
  },
}
</script>
<style lang="less">
.activity-wrapper {
  width: 118px;
  height: 118px;
}
.activity-icon {
  width: 100px;
  height: 100px;
}
.activity-btn {
  width: 116px;
  height: 44px;
  position: absolute;
  top: 74px;
  left: 0px;
}
.acticity-light {
  width: 54px;
  height: 40px;
  position: absolute;
  top: 0px;
  left: 0px;
}
.scale-down-up-center {
  animation-name: scaleDownUpCenter;
  animation-duration: 1500ms;
  animation-timing-function: linear;
  animation-fill-mode: forwards;
  animation-iteration-count: infinite;
}

@keyframes scaleDownUpCenter {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
  }
}

.translateX {
  animation-name: translateX;
  animation-duration: 1500ms;
  animation-iteration-count: infinite;
  animation-timing-function: ease;
}

@keyframes translateX {
  0% {
    transform: translateX(0px);
    opacity: 1;
  }
  59% {
    opacity: 1;
  }
  80% {
    opacity: 0;
    transform: translateX(50px);
  }
  100% {
    transform: translateX(50px);
    opacity: 0;
  }
}
</style>
