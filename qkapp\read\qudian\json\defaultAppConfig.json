{"config": {"is_reviewer": {"op": "is", "val": "2"}, "boost_group_ad_count": {"op": "is", "val": "2"}, "ad_btn_text_type": {"op": "is", "val": "1"}, "is_used_ad_button": {"op": "is", "val": "1"}, "max_show_times": {"op": "is", "val": "10000"}, "boost_request_type": {"op": "is", "val": "1"}, "is_read_first_adscroll_equal_adclick": {"op": "is", "val": "2"}, "ad_first_close_icon_show_type": {"op": "is", "val": "2"}, "ad_read_first_button_animation_type": {"op": "is", "val": "2"}, "ad_read_middle_button_animation_type": {"op": "is", "val": "2"}, "ad_read_first_adscroll_times": {"op": "is", "val": "0"}, "read_view_back_icon_show_type": {"op": "is", "val": "2"}, "ad_dialog_bottom": {"op": "is", "val": ""}, "first_ad_bottom": {"op": "is", "val": ""}, "back_app_button": {"op": "is", "val": "2"}, "page_read_trigger_unlock_pop": {"op": "is", "val": ""}, "read_enable_tags": {"op": "is", "val": ""}, "read_custom_ad_code": {"op": "is", "val": ""}, "read_tags_adscroll_times": {"op": "is", "val": ""}, "ad_read_middle_amount": {"op": "is", "val": ""}, "exceed_ad_read_middle_amount": {"op": "is", "val": ""}, "ad_read_middle_custom_page": {"op": "is", "val": ""}, "style_ad_middle_custom_a": {"op": "is", "val": ""}, "style_ad_middle_custom_b": {"op": "is", "val": ""}, "is_show_desk_float": {"op": "is", "val": "1"}, "is_read_ad_custom_config": {"op": "is", "val": "0"}, "is_first_ad_custom_config": {"op": "is", "val": "0"}, "custom_ad_material_category_code": {"op": "is", "val": ""}, "is_vivo_back_button": {"op": "is", "val": "2"}, "is_open_welfare_menuBar": {"op": "is", "val": "1"}, "main_list_ad": {"op": "is", "val": "3"}, "info_list_ad": {"op": "is", "val": "3"}, "welfare_list_ad": {"op": "is", "val": "3"}, "read_list_ad": {"op": "is", "val": "3"}, "holdup_ad_middle_custom_a": {"op": "is", "val": ""}, "holdup_ad_middle_custom_b": {"op": "is", "val": ""}, "middle_ad_hold_up_style": {"op": "is", "val": "1"}, "middle_ad_lock_times": {"op": "is", "val": "0"}, "middle_lock_toast": {"op": "is", "val": "查看广告阅读更多精彩内容"}, "ads_show_interval_min": {"op": "is", "val": "2000"}, "vivo_queue_request": {"op": "is", "val": "1"}, "banner_afresh_request": {"op": "is", "val": "3"}, "is_hidden_banner": {"op": "is", "val": "0"}, "is_open_advert": {"op": "is", "val": "1"}, "read_bgpic": {"op": "is", "val": "1"}, "read_font": {"op": "is", "val": "54"}, "is_open_banner": {"op": "is", "val": "1"}, "is_open_homeScreen_advert": {"op": "is", "val": "0"}, "is_exempt_ads": {"op": "is", "val": "0"}, "cross_advert": {"op": "is", "val": "0"}, "is_banner_ad": {"op": "is", "val": "0"}, "is_bqt_can_show_dialog": {"op": "is", "val": "0"}, "native_ad_click_interval": {"op": "is", "val": "0"}, "text_ad_trigger_timing": {"op": "is", "val": "1"}, "lock_vivo_back_times": {"op": "is", "val": "0"}, "is_show_last_read_banner": {"op": "is", "val": "0"}, "is_chapter_end_recommend": {"op": "is", "val": "0"}, "add_desktop_pages": {"op": "is", "val": ""}, "is_used_icon_ad": {"op": "is", "val": "0"}, "is_show_notification": {"op": "is", "val": "0"}, "notification_title": {"op": "is", "val": ""}, "notification_text": {"op": "is", "val": ""}, "notification_url": {"op": "is", "val": ""}, "show_add_desktop_second": {"op": "is", "val": "0"}, "page_swipe_red_bag_nums": {"op": "is", "val": "0"}, "page_swipe_coin_nums": {"op": "is", "val": "0"}, "custom_content_code": {"op": "is", "val": ""}, "ad_chapter_end_switch": {"op": "is", "val": "0"}, "chapter_end_red_bag": {"op": "is", "val": "0"}, "chapter_end_reward": {"op": "is", "val": "0"}, "chapter_end_image_width": {"op": "is", "val": "0"}, "chapter_end_free_ad_time": {"op": "is", "val": "0"}, "chapter_end_image_height": {"op": "is", "val": "0"}, "chapter_end_red_bag_img": {"op": "is", "val": ""}, "chapter_end_reward_coin": {"op": "is", "val": "100"}, "is_sign_page_entry": {"op": "is", "val": "0"}, "sign_hint_second": {"op": "is", "val": "0"}, "is_recharge_login": {"op": "is", "val": "1"}, "is_open_welfare_activity_shake": {"op": "is", "val": "0"}, "is_open_welfare_activity_turntable": {"op": "is", "val": "0"}, "is_open_read_activity_shake": {"op": "is", "val": "0"}, "is_open_read_activity_turntable": {"op": "is", "val": "0"}, "applicationInstall": {"op": "is", "val": "1"}, "activity_to_side": {"op": "is", "val": "0"}, "complaint_to_side": {"op": "is", "val": "0"}, "isShowAction": {"op": "is", "val": "1"}, "is_used_hierarchy": {"op": "is", "val": "1"}, "preload_baidu_AD": {"op": "is", "val": "0"}, "ad_delayed_load_times": {"op": "is", "val": "0"}, "hierarchy_await_times": {"op": "is", "val": "1000"}, "download_ad_is_jump_2_view": {"op": "is", "val": "1"}, "read_can_swiper_time": {"op": "is", "val": "0"}, "is_activity_allow_back_time": {"op": "is", "val": "0"}, "user_guide_time": {"op": "is", "val": "3"}, "user_guide_style": {"op": "is", "val": "0"}, "read_banner_config": {"op": "is", "val": "0"}, "read_page_margin_bottom": {"op": "is", "val": "8"}, "view_advs_max_count": {"op": "is", "val": "2"}, "layer_request_enter_pool_interval_times": {"op": "is", "val": "3000"}, "lower_show_times": {"op": "is", "val": "500"}, "max_count_by_scene": {"op": "is", "val": "3"}, "read_banner_refresh_rule": {"op": "is", "val": "1"}, "read_banner_refresh_timer": {"op": "is", "val": "10000"}, "time_difference": {"op": "is", "val": "1"}, "line_space": {"op": "is", "val": "0"}, "re_request_banner": {"op": "is", "val": "0"}, "add_desktop_animation": {"op": "is", "val": "0"}, "add_desktop_width": {"op": "is", "val": "690"}, "add_desktop_height": {"op": "is", "val": "80"}, "add_desktop_background_color": {"op": "is", "val": "rgba(243, 182, 125, 1)"}, "add_desktop_background_color_opacity": {"op": "is", "val": "1"}, "add_desktop_border_radius": {"op": "is", "val": "80"}, "add_desktop_font_size": {"op": "is", "val": "28"}, "add_desktop_font_weight": {"op": "is", "val": "700"}, "add_desktop_text_color": {"op": "is", "val": "#845824"}, "add_desktop_text_value": {"op": "is", "val": "立即添加到桌面，方便看书"}, "add_desktop_logo_url": {"op": "is", "val": ""}, "add_desktop_close_icon": {"op": "is", "val": ""}, "read_bottom_text": {"op": "is", "val": ""}, "is_ad_loading_can_back_read": {"op": "is", "val": "1"}, "is_ad_loading_can_back_welfare": {"op": "is", "val": "1"}, "is_ad_loading_can_back_home": {"op": "is", "val": "1"}, "is_show_source": {"op": "is", "val": "1"}, "bqt_btn_bgcolor": {"op": "is", "val": "#ff9900"}, "bqt_btn_width": {"op": "is", "val": "600"}, "bqt_btn_height": {"op": "is", "val": "100"}, "bqt_btn_font": {"op": "is", "val": "44"}, "auto_add_shelf_page": {"op": "is", "val": "0"}, "read_turn_chapter_num": {"op": "is", "val": "0"}, "red_envelope_rain": {"op": "is", "val": ""}, "page_back_complete_2_show": {"op": "is", "val": "1"}, "is_show_less_ad": {"op": "is", "val": "0"}, "less_ad_minutes": {"op": "is", "val": "10"}, "is_stop_exposure": {"op": "is", "val": "1"}, "is_page_back_first": {"op": "is", "val": "1"}, "use_data_pool_advertisers": {"op": "is", "val": ""}, "agd_abandon_probit": {"op": "is", "val": "100"}, "no_agd_abandon_probit": {"op": "is", "val": "100"}, "boost_request_loop_time": {"op": "is", "val": "10000"}, "boost_max_count": {"op": "is", "val": "1"}, "boost_delay_time": {"op": "is", "val": "1000"}, "ad_disable_in_xiaomi": {"op": "is", "val": "1"}, "action_entry_show": {"op": "is", "val": "0"}, "target_actId": {"op": "is", "val": ""}, "video_zidingyi": {"op": "is", "val": "3,10,20"}, "video_shunxu": {"op": "is", "val": "1,2,3,4,5,6"}, "video_daojishi": {"op": "is", "val": "30"}, "read_ad_circle": {"op": "is", "val": "0"}, "info_ad_circle": {"op": "is", "val": "0"}, "action_ad_circle": {"op": "is", "val": "0"}, "welfare_ad_circle": {"op": "is", "val": "0"}, "main_ad_circle": {"op": "is", "val": "0"}, "wake_self_times": {"op": "is", "val": "1"}, "is_open_secure_page": {"op": "is", "val": "1"}, "is_complaint_above_pop": {"op": "is", "val": "0"}, "boost_onhide_type": {"op": "is", "val": "1"}, "is_rewarded_by_video": {"op": "is", "val": "1"}, "open_ad_cover_page": {"op": "is", "val": ""}, "open_ad_cover_scene": {"op": "is", "val": ""}, "count_down_trigger_time": {"op": "is", "val": ""}, "ad_cover_type": {"op": "is", "val": ""}, "ad_cover_layer_count": {"op": "is", "val": "0"}, "rewarded_video_auto_play": {"op": "is", "val": "0"}, "sourcePackageName": {"op": "is", "val": "0"}, "custom_btn_random": {"op": "is", "val": "0"}, "is_hide_menubar": {"op": "is", "val": "1"}, "t_s_type": {"op": "is", "val": "1"}, "is_show_exit_icon": {"op": "is", "val": "0"}}, "appCode": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "configName": "默认本地配置数据"}