/**
 * 您可以将常用的方法、或系统 API，统一封装，暴露全局，以便各页面、组件调用，而无需 require / import.
 */
const prompt = require('@system.prompt')
const storage = require('@system.storage')
const router = require('@system.router')
const fetch = require('@system.fetch')
const clipboard = require('@system.clipboard')


/**
 * 拼接 get url 和参数
 */
function queryString(url, query) {
  let str = []
  for (let key in query) {
    str.push(key + '=' + query[key])
  }
  let paramStr = str.join('&')
  return paramStr ? `${url}?${paramStr}` : url
}
/**
 * toast
 * @param {*} message 
 * @param {*} duration 
 */
function showToast(message = '', duration = 0) {
  if (!message) return
  prompt.showToast({
    message: message,
    duration
  })
}
/**
 * 
 * @param {String} title 标题
 * @param {String} message 内容 
 * @param {Array} buttons 按钮的数组，按钮结构：{text:'text',color:'#333333'}，color 可选：buttons 的第 1 项为 positive button；buttons 的第 2 项（如果有）为 negative button；buttons 的第 3 项（如果有）为 neutral button。最多支持 3 个 button
 * @param {Function} callback 回调函数 
 */
function showDialog(title, message, buttons, callback) {
  prompt.showDialog({
    title,
    message,
    buttons,
    success: function (data) {
      callback(data)
    },
    cancel: function () {
      console.log('handling cancel')
    },
    fail: function (data, code) {
      console.log(`handling fail, code = ${code}`)
    }
  })
}
/**
 * 判断手机号码
 * @param {} data 
 */
function phone(data) {
  var mobile_mode = /^1[34578]\d{9}$/;
  if (!mobile_mode.test(data)) {
    return true;
  }
}

/**
 * 设置storage
 * @param {*} key 
 * @param {*} value 
 */
function setStorage(key, value) {
  return new Promise((resolve, reject) => {
    storage.set({
      key,
      value: value,
      success: function(data) {
        resolve(data)
      },
      fail(data, code) {
        console.log(`setStorage fail, code = ${code}`)
      }
    })
  })
}
/**
 * 清空所有的缓存
 * @returns 
 */
function clearStorage() {
  return new Promise((resolve, reject) => {
    storage.clear({
      success: function (data) {
        resolve()
      },
      fail: function (data, code) {
        reject()
      }
    })
  })
}
/**
 * 获取storage
 * @param {*} key 
 */
function getStorage(key) {
  return new Promise((resolve, reject) => {
    storage.get({
      key,
      success(data) {
        resolve(data)
      },
      fail(data, code) {
        console.log(`getStorage fail, code = ${code}`)
        reject({ data, code })
      }
    })
  })
}

/**
 * 删除缓存信息
 */
function deleteStorage(key) {
  storage.delete({
    key,
    success: function (data) {
      console.log('handling success')
    },
    fail: function (data, code) {
      console.log(`handling fail, code = ${code}`)
    }
  })
}

/**
 * route  push页面挑转
 */
function routetheUrl(url, params, clear = false) {
  router.push({
    uri: url,
    params: { ...params, clearRouter: clear }
  })
}
/**
 * route  Replace  页面挑转
 * @param {} url 
 * @param {*} params 
 */
function routeReplacetheUrl(url, params) {
  router.replace({
    uri: url,
    params: params
  })
}
/**
 * route  判断当前要跳转的路由是否为当前页面
 * @param {} url 
 * @param {*} params 
 */
function routeCheckUrl(url) {
  let routerList = router.getPages()
  console.log(`判断当前要跳转的路由是否为当前页面routeCheckUrl==>`, routerList)
  if (routerList[routerList.length - 1].path == url) {
    return true
  }
  return false
}
/**
 * route  检测当前页面数是否大于1
 */
function routeCheckPages() {
  let routerList = router.getPages()
  if (routerList.length > 1) {
    return true
  }
  return false
}
/**
 * 返回上一级
 */
function goBack() {
  router.back();
}
/**
 * 返回指定页面
 */
function goBackTo(path) {
  router.back({ path });
}
function clear() {
  router.clear()
}

/**
 *  //判断页面是否在栈内存里面存在，如果存在直接goback，否则跳转打开新路由
 */

function stacksRouter(path) {
  let routerList = []
  var stacks = router.getPages()
  stacks.forEach(item => {
    routerList.push(item.path)
  })
  //从数组中找到指定页面，没有则返回-1
  if (routerList.indexOf(path) != -1) {
    return true
  }
  return false
}

/**
 * 消息通知点击上报
 */
function pushMessageInfo(data) {
  $apis.example.pushMessage({
    pushId: data
  })
    .then(res => { })
    .catch(err => { })
}

/**
 * 手机返回键延迟2s
 * @param {number} startTimestamp  开始时间
 */
function backTime(startTimestamp, _this) {
  return new Date().getTime() - startTimestamp < _this.$app.$def.backTime
}

/**
 * 手机返回键延迟2s
 * @param {number} startTimestamp  开始时间
 * @param {Number} times  间隔差
 */
function backTimeCommon(startTimestamp, times) {
  return new Date().getTime() - startTimestamp < times
}

// 格式化倒计时 s->HH:mm:ss
function formatTime(time) {
  let hours = Math.floor(time / 3600)
  let minute = Math.floor(Math.floor(time % 3600) / 60)
  let second = time % 60
  hours = hours.toString().length === 1 ? `0${hours}` : hours
  minute = minute.toString().length === 1 ? `0${minute}` : minute
  second = second.toString().length === 1 ? `0${second}` : second
  return hours + ':' + minute + ':' + second
}
/* 
     * 截取指定字节长度的字符串 
     * 注：半角长度为1，全角长度为2
     * str:字符串 
     * len:截取长度
     * return: 截取后的字符串及是否截取的标记（扩展用）code=1 字符串截断   code=0  字符串未截断
     */
function cutStrByte(str, len) {
  //校验参数
  if (!str || !len) {
    return { "cutStr": "", "code": 0 };
  }
  var code = "1",// 默认返回code值，已截断
    strLen = str.length,// 原字符串长度
    cutStr;
  //如果字符串长度小于截取长度的一半,则返回全部字符串
  if (strLen <= len / 2) {
    cutStr = str;
    code = "0";
  } else {
    //遍历字符串
    var strByteCount = 0;
    for (var i = 0; i < strLen; i++) {
      //中文字符字节加2  否则加1
      strByteCount += getByteLen(str.charAt(i));
      //i从0开始 截断时大于len 只截断到第i个
      if (strByteCount > len) {
        cutStr = str.substring(0, i);
        break;
      } else if (strByteCount == len) {
        cutStr = str.substring(0, i + 1);
        break;
      }
    }
  }
  //cutstr为空，没有截断字符串
  if (!cutStr) {
    cutStr = str;
    code = "0";
  }
  return { "cutStr": cutStr, "code": code };
}

/**
* 获取字节长度，全角字符两个单位长度，半角字符1个单位长度
*/
function getByteLen(val) {
  var len = 0;
  if (!val) {
    return len;
  }
  for (var i = 0; i < val.length; i++) {
    if (!val[i]) {
      continue;
    }
    // 全角
    if (val[i].match(/[^\x00-\xff]/ig) != null) {
      len += 2;
    } else {
      len += 1;
    }
  }
  return len;
};

/**
 * 判断时间捡个是否大于30分钟
 * @param {number} startTimestamp  开始时间
 * @param {Number} times  间隔差
 */
function adFreeTimeDifference(endTime) {
  let curTime = Date.parse(new Date());
  let minute = Math.floor((endTime - curTime) / 1000)
  console.log(`剩余看视频免广告时间======>${minute}秒`)
  return endTime > curTime ? true : false
}

/**
 * 计算当前时间戳和传入时间戳的差值 
 * @param {Number} timestamp 传入onShow记录的时间戳 
 * @return 单位 s
 */
function pageTimeCompute(timestamp) {
  let seconds = 0
  let cruTiemstamp = new Date().getTime()
  if (cruTiemstamp > timestamp) {
    seconds = Math.ceil((cruTiemstamp - timestamp) / 1000)
  }
  return seconds
}

/**
  * 截取字符串
  * @param {String} str 截取的字符串
  * @param {Number} n 截取的长度
  */
function cutStr(str, n) {
  var strArr = [];
  for (var i = 0, l = str.length; i < l / n; i++) {
    var a = str.slice(n * i, n * (i + 1));
    strArr.push(a);
  }
  return strArr;
}
/**
 * 检测指定value 是否满足相应条件
 * @param {Number} curVal 设置的值
 * @param {Number} defalutVal 默认的值
 * @param {String} cond 条件 is 等于   not_is 不等于  less 小于 greater 大于
 * @returns {Boolean} res 
 */
 function checkKeyValueConditionSatisfied(curVal,defalutVal,cond) {
  if(cond != 'is' && cond != 'not_is' && cond != 'less' && cond != 'greater') {
    console.error('function checkKeyValueConditionSatisfied’s param(cond) not included is or not_is or less or greater')
    return false
  }
  let res = false
  switch (cond) {
    case 'is':
      res = curVal == defalutVal
      break
    case 'not_is':
      res = curVal != defalutVal
      break
    case 'less':
      res = curVal < defalutVal
      break
    case 'greater':
      res = curVal > defalutVal
      break
  }
  return  typeof res == 'boolean' ? res : false 
}
// 获取二级来源
function getSourceType() {
  LOG('getSourceType==========>', require('@system.app').getInfo())
  return require('@system.app').getInfo().source.type
}
// 获取三级来源
function getExtraScene() {
  let scene = require('@system.app').getInfo().source.extra.scene
  return scene ? scene : ''
}

// 日期
function dateToString() {
  let nowDate = new Date()
  var o = {
    'Y': nowDate.getFullYear(), // year
    "M" : nowDate.getMonth()+1, //month
    "d" : nowDate.getDate(),    //day
    "h" : nowDate.getHours(),   //hour
    "m" : nowDate.getMinutes(), //minute
    "s" : nowDate.getSeconds(), //second
    "q" : Math.floor((nowDate.getMonth()+3)/3),  //quarter
    }
    return `${o.Y}${o.M}${o.d}`;
}

/**
 * 设置剪切板内容
 * @param {*} text 
 */
 function setClipboard(text) {
  return new Promise((resolve) => {
    clipboard.set({
      text: text,
      success: function(data) {
        resolve(true)
      },
      fail: function(data, code) {
        console.log(`setClipboard fail, code = ${code}`)
        resolve(false)
      }
    })
  })
}

/**
 * 修改小说id
 */
function handleHapDataByBookId(bookId,type='init') {
  let errorType = type === 'init' ? '应用启动参数异常' : '自定义内容'
  if (!bookId) { //小说id为空
    COMMON_REPORT_UTILS.error_log_report(errorType, `小说id为空`, '全局异常', `程序异常`)
    return 0
  }

  if (/^[0-9]+$/.test(`${bookId}`)) { // id为数字字符串
    return Number(bookId)
  }

  const matchRes = `${bookId}`.match(/\d+/g);
  if (matchRes) { //非数字字符串包含 数字
    COMMON_REPORT_UTILS.error_log_report(errorType, `小说id异常且包含数字-${bookId}`, '全局异常', `程序异常`)
    return Number(matchRes[0])
  } else { //非数字字符串不包含数字
    COMMON_REPORT_UTILS.error_log_report(errorType, `小说id异常且不包含数字-${bookId}`, '全局异常', `程序异常`)
    return 0
  }

}


export default {
  showToast,
  queryString,
  phone,
  setStorage,
  getStorage,
  routetheUrl,
  routeReplacetheUrl,
  goBack,
  goBackTo,
  clear,
  deleteStorage,
  pushMessageInfo,
  backTime,
  backTimeCommon,
  routeCheckUrl,
  formatTime,
  routeCheckPages,
  clearStorage,
  showDialog,
  cutStrByte,
  getByteLen,
  adFreeTimeDifference,
  pageTimeCompute,
  cutStr,
  stacksRouter,
  checkKeyValueConditionSatisfied,
  getSourceType,
  getExtraScene,
  dateToString,
  setClipboard,
  handleHapDataByBookId
}
