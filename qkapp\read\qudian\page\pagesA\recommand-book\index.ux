<import name="common-back-header" src="../../components/common-back-header/index.ux"></import>
<import name="common-header" src="../components/common-header/index.ux"></import>

<template>
  <div class="wrapper">
    <list style="flex: 1;">
      <list-item type="header" style="margin-top:88px;">
        <common-header></common-header>
      </list-item>
      <list-item type="book" class="book-item" for="(index, item) in bookList" style="margin-bottom:{{index==bookList.length-1?'30':'0'}}px;">
        <div style="padding: 30px 0;background-color: #FAFAFA;flex-direction:column;border-top-left-radius:24px;border-top-right-radius:24px;">
          <div style="margin:10px auto;align-items:center;">
            <div style="width: 16px;height: 4px;background-color: #e1b258;"></div>
            <text style="margin:0 10px;height:36px;font-size:36px;font-weight:bold;color:#e1b258;">{{textArr[index%6]}}</text>
            <div style="width: 16px;height: 4px;background-color: #e1b258;"></div>
          </div>
          <div style="margin-top:40px;">
            <stack style="margin-left:23px;margin-right:33px;">
              <image src="https://img.qdreads.com/v173/sjyy_dcc.png" style="width:142px;height:189px;margin-top:7px;"></image>
              <div style="margin-left:7px;"> 
                <image
                  src="{{item.bookIconUrl}}"
                  style="object-fit: fill;width:128px;height:181px;border-radius: 6px;"
                ></image>
                <book-status xstype="{{item.xstype}}"></book-status>
              </div>
            </stack>
            <div style="flex-direction:column;height:180px;">
              <text style="margin-top:6px;height: 30px;font-size: 30px;color: #333333;">{{item.bookName}}</text>
              <div style="align-items:center;margin-top:18px;">
                <text style="height: 22px;font-size: 22px;color: #adadad;">{{item.bookAuthor}}</text>
                <div style="width: 2px;height: 16px;background-color: #e9e9e9;margin:0 10px;"></div>
                <text style="height: 22px;font-size: 22px;color: #c8ab80;">{{item.fcate}}</text>
                <div style="width:4px;height:4px;border-radius:50%;background-color:#adadad;margin:2px 4px 0;"></div>
                <text style="height: 22px;font-size: 22px;color: #adadad;">{{ item.words | readNumHandle }}字</text>
              </div>
              <div style="width: 214px;height: 38px;background-image:url('https://img.qdreads.com/v173/bq.png');margin:auto 0 2px;padding:0 20px;align-items:center;background-size:100%;background-repeat:no-repeat;">
                <text style="height: 20px;font-size: 20px;color: #ffffff;">好书排行</text>
                <text style="color: #ff9802;height: 28px;margin-left:auto;">
                  <span style="font-size: 28px;font-weight:bold;">{{item.rankNum}}</span>
                  <span style="font-size: 20px;"> 名</span>
                </text>
              </div>
            </div>
          </div>
          <div class="detail-bottom-count-content">
            <div class="count-item">
              <div style="align-items:center;">
                <text class="novel-score" style="margin-right: 6px">{{item.score / 10}}</text>
                <block for="{{(indexY, itemY) in starArr}}">
                  <image
                    if="{{item && (item.score / 20) >= (indexY + 1)}}"
                    class="detail-bottom-star"
                    src="https://img.qdreads.com/v163/<EMAIL>"
                  ></image>
                  <image
                    else
                    class="detail-bottom-star"
                    src="https://img.qdreads.com/v163/<EMAIL>"
                  ></image>
                </block>
              </div>
              <text class="novel-score-desc">评分</text>
            </div>
            <div
              style="width: 2px; height: 70px; background-color: #e9e9e9"
            ></div>
            <div class="count-item" style="align-items: flex-end;">
              <div style="align-items: flex-start;flex-direction:column;">
                <text class="novel-score">
                  {{ item.bookNum | readNumHandle }}
                </text>
                <text class="novel-score-desc">已阅读</text>
              </div>
            </div>
          </div>
        </div>
        <div style="padding:30px;background-color:#ffffff;flex-direction:column;border-bottom-left-radius:24px;border-bottom-right-radius:24px;">
          <text style="height: 36px;font-size: 36px;font-weight: bold;color: #333333;">书籍简介</text>
          <text style="font-size: 28px;color: #666666;line-height: 50px;margin-top:20px;lines:4;text-overflow:ellipsis;">{{item.bookContenUrl}}</text>
          <div style="margin-top:30px;">
            <div @click="addShelf(item)" style="width: 300px;height: 80px;border: 1px solid #d1d1d1;border-radius: 40px;justify-content:center;align-items:center;">
              <image src="https://img.qdreads.com/v173/add-shelf.png" style="margin-right:4px;width:36px;height:36px;"></image>
              <text style="font-size: 26px;color: #999999;height: 26px;font-weight:bold;">{{item.isShelf?'已':''}}加入书架</text>
            </div>
            <text @click="toReadPage(item)"
              style="width: 300px;height: 80px;background-color: #F11212;border-radius: 40px;font-size: 26px;color: #ffffff;text-align:center;font-weight:bold;margin-left:30px;"
            >开始阅读</text>
          </div>
        </div>
      </list-item>
    </list>
    <div style="position:absolute;top:0;left:0;width:100%;background:linear-gradient(rgba(0, 0, 0,0.3), rgba(0, 0, 0,0));">
      <common-back-header @back-click="backClickHandler" text-color="#FFFFFF"></common-back-header>
    </div>
  </div>
</template>
<import name="book-status" src="../../components/book-list/book-status/index.ux"></import>

<script>
export default {
  public: {
    pageDetail: {
      pageUrl: '推荐小说页',
      pageName: '推荐小说页',
      pageCode: 'READ_COMMON',
      pageOrigin: ''
    },
    pathUrl: '',
    bookList: [],
    bookIds: '',
    textArr: ['62%的人在午休时读它','三章后即名场面','你可能在找的梦中情文','看看爽文，解解生活的苦','56%的人都收藏了它','68%的萌新都在读它',],
    starArr: ['', '', '', '', ''], // 评分星星数组
  },

  onInit() {
    this.getData()
  },
  onShow() {
    // 更新页面来源
    this.pathUrl = curObj.pathUrl
    
    
  },
  onHide() {
    
  },
  async getData() {
    let res = await $apis.example.getBooksDetail({bookIds: this.bookIds})
    if (res.code == 200) {
      this.bookList = res.data
    }
  },
  async addShelf(item) {
    if (item.isShelf) {
      $utils.showToast('请去首页的书架移除！')
      return
    }
    if(!CLICK_UTILS.dom_click_vali_shake(`recommandAddShelf_${this.__id__}`,500)) return
    COMMON_REPORT_UTILS.page_click_report('加入书架', '', `${this.bookId}`, ``) //点击上报
    let res = await $apis.example.addBookshelfApi({ bookId: item.bookId})
    if (res.code == 200) {
      item.isShelf = !item.isShelf
      this.$app.$def.tabListType[0] = 0
    }
  },
  toReadPage(item) {
    COMMON_REPORT_UTILS.page_click_report('开始阅读', '', `${this.bookId}`, ``) //点击上报
    let params = {
      bookId: item.bookId,
      pathUrl: this.pathUrl
    }
    this.isJump = true
    $utils.routetheUrl('/pagesC/Read', params)
  },
  //阅读人数处理 万级 处理成小数点
  readNumHandle(readNum) {
    if (Number(readNum) > 10000) {
      return (Number(readNum) / 10000).toFixed(1) + 'w'
    }
    return readNum
  },
  backClickHandler() {
    $utils.goBack()
  },
}
</script>

<style lang="less">
.wrapper {
  width: 750px;
  height: 100%;
  flex-direction: column;
  align-items: center;
  background: linear-gradient(180deg,#1b0d0f 0%, #281418 100%);
}

.book-item {
  width: 690px;
  border-radius: 24px;
  flex-direction: column;
  margin: 30px 30px 0;
}

.detail-bottom-count-content {
    align-items: center;
    margin: 30px 23px 0 0;

    .count-item {
        flex-direction: column;
        justify-content: center;
        padding: 0 30px;
        width: 314px;

        .novel-score {
            font-size: 32px;
            font-weight: bold;
            color: #333333;
            height: 32px;
            .novel-score-small-font {
                font-size: 20px;
                color: #333333;
                font-weight: normal;
            }
        }
        .novel-score-desc {
            font-size: 20px;
            color: #999999;
            margin-top: 14px;
            height: 20px;
        }
    }
}

.detail-bottom-star {
  width: 24px;
  height: 24px;
}
</style>
