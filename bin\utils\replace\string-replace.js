const { insertComments } = require('./file-position')
const { findCodeMatch } = require('./base/match')
const colors = require('colors')

function stringReplace({ content, oldCode, newCode, failCodeInfo, file, desc }) {
  // 插入位置标识
  const fileContent = insertComments(content)

  const matchedOldCode = findCodeMatch(oldCode, fileContent)
  const matchedNewCode = findCodeMatch(newCode, fileContent)

  // 有新的内容就不再替换
  if (matchedNewCode) {
    return content
  }

  // 没有匹配到旧的值，说明有代码段有问题
  if (!matchedOldCode) {
    console.log(colors.red('有代码段未匹配上，请检查：', oldCode))
    failCodeInfo.push({ oldCode, desc })
    return content
  }

  return content.replace(matchedOldCode, newCode)
}

module.exports = stringReplace
