/**
 * 从 source 中找到 target 核心匹配的内容
 * @param target
 * @param source
 * @return {*|null}
 */
function findCodeMatch(target, source) {
  if (!source) {
    console.log('找不到匹配的内容', target)
  }
  // 移除目标字符串中的所有空白字符和分号
  const normalizedTarget = target.replace(/[\s;]+/g, '')

  let targetIndex = 0
  let start = -1

  for (let i = 0; i < source.length; i++) {
    const currentChar = source[i]

    // 如果当前字符与目标字符的当前字符匹配（忽略空白字符和分号）
    if (currentChar === normalizedTarget[targetIndex]) {
      if (start === -1) {
        start = i // 记录起始位置
      }
      targetIndex++
    } else if (/[\s;]/.test(currentChar)) {
      // 如果是空白字符或分号，跳过
      continue
    } else {
      // 不匹配且非空白字符或分号，重置匹配
      targetIndex = 0
      start = -1
    }

    // 如果完全匹配目标字符串
    if (targetIndex === normalizedTarget.length) {
      return source.slice(start, i + 1)
    }
  }

  return null // 如果没有匹配到，返回 null
}

module.exports = {
  findCodeMatch
}
