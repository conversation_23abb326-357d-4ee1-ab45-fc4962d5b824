<import name="common-header" src="../components/common-header/index"></import>
<template>
  <!-- template里只能有一个根节点 -->
  <div class="shelf-wrapper">
    <list style="height: 100%;" @scroll="listScroll">

      <list-item type="top">
        <stack>
          <div class="top-bg">
            <image src="https://img.qdreads.com/v173/sc_bj_wsh.png"></image>
          </div>
          <!-- 热门推荐 -->
          <div
            class="shelf-recommom-section"
            style="margin-top: {{statusBarHeight + 138}}px;"
          >
            <swiper class="recommom-swiper" indicator="{{false}}" @change="swiperChangeHandle">
              <block for="(index, item) in bannerList">
                <div style="padding: 0 30px;height:100%;" @appear="startLottie(index)">
                  <div style="background-color: #ffffff;border-radius: 24px;flex-direction:column;align-items:center;">
                    <div class="recommom-wrapper" @click="compClickHandler(item)">
                      <stack class="recommom-image-wrapper">
                        <image src="https://img.qdreads.com/v173/sjyy_dcc.png" style="width:142px;height:189px;margin-top:7px;"></image>
                        <div> 
                          <image
                            src="{{item.bookIconUrl}}"
                            style="object-fit: fill;width:128px;height:180px;border-radius: 6px;margin-left:7px;"
                          ></image>
                          <book-status xstype="{{item.xstype}}"></book-status>
                        </div>
                      </stack>
                      <div class="recommom-info-wrapper">
                        <text class="ri-name">{{ item.bookName }}</text>
                        <text class="ri-content">{{ item.bookContenUrl }}</text>
                        <div style="height: 22px;margin-top: 26px;align-items: center;">
                          <text style="height: 22px;font-size: 22px;color: #C8AB80;">{{item.fcate}}</text>
                          <div style="width:4px;height:4px;border-radius:50%;background-color:#adadad;margin:2px 4px 0;"></div>
                          <text style="height: 22px;font-size: 22px;color: #adadad;">{{ item.bookNum | readNumHandle }}人阅读</text>
                        </div>
                      </div>
                      <block>
                        <lottie class="swiper-lottie" source="https://img.qdreads.com/lottie/jrtj.json" id="banner{{index}}" autoplay={{false}} loop="{{false}}"></lottie>
                      </block>
                      <!-- <block else>
                        <image src="https://img.qdreads.com/v173/sj_jrtt.png" class="swiper-lottie"></image>
                      </block> -->
                    </div>
                    <div class="indi-wrapper">
                      <block for="{{(index, item) in bannerList}}">
                        <div
                          if="{{swiperIndex != index}}"
                          class="indicator"
                          style="margin-left: 8px"
                        ></div>
                        <block else>
                          <div style="margin-left: 8px">
                            <div
                              style="width: 4px;height: 6px;border-top-left-radius: 100%;background-color: #F11212;"
                            ></div>
                            <div
                              style="width: 8px; height: 6px; background-color: #F11212"
                            ></div>
                            <div
                              style="width: 4px;height: 6px;border-bottom-right-radius: 100%;background-color: #F11212;"
                            ></div>
                          </div>
                        </block>
                      </block>
                    </div>
                  </div>
                </div>
              </block>
            </swiper>
          </div>
        </stack>
      </list-item>

      <!-- 书架书籍 -->
      <list-item type="book" class="shelf-book-section">
        <div style="width:630px;align-items:center;margin-left:30px;">
          <text style="height: 36px;color: #333333;font-size: 36px;font-weight: bold;">书架</text>
          <image @click="toHistory" src="https://img.qdreads.com/v173/<EMAIL>" style="width:26px;height:26px;margin:2px 4px 0 auto;object-fit:fill;"></image>
          <text @click="toHistory" style="height: 26px;color: #666666;font-size: 26px;">浏览记录</text>
          <div style="width: 1px;height: 20px;background-color: #d4d4d4;margin:0 30px;"></div>
          <image @click="moreBtnClick" src="https://img.qdreads.com/v173/<EMAIL>" style="width:26px;height:26px;margin:2px 4px 0 0;"></image>
          <text @click="moreBtnClick" style="height: 26px;color: #666666;font-size: 26px;">更多</text>
        </div>
        <div style="position:absolute;left:0px;top:66px;width:100%;justify-content:center;">
          <block>
            <div if="{{transformClass == 'show-loading'}}" class="loading-view-image">
              <lottie class="loading-lottie" source="../../lottie/data.json" id="shelf-loading" autoplay={{true}} loop={{true}}></lottie>
            </div>
          </block>
          <!-- <block else>
            <div if="{{transformClass == 'show-loading'}}" class="loading-view-image">
              <image src="../../assets/images/load.gif"></image>
            </div>
          </block> -->
        </div>
        <div class="shelf-book-section-list {{transformClass}}">
          <block if="{{getDataFinished}}">
              <div
                show="{{shelfDirection}}"
                for="{{rootItem in boardList}}"
                style="width:100%;"
              >
                <div
                  class="shelf-boook-item"
                  for="(index,item) in rootItem"
                  style="margin-left:{{index==0?23:25}}px;"
                  @click="toBookDetail(item)"
                >
                  <stack>
                    <image src="https://img.qdreads.com/v173/sj_sjyy.png" style="width:197px;height: 267px;margin-top:7px;"></image>
                    <image
                      style="margin-left:7px;"
                      class="shelf-boook-item-image"
                      src="{{item.bookIconUrl}}"
                      @longpress="delShelfBook(item)"
                    ></image>
                    <div style="position:absolute;top:0;right:6px;width:62px;height:32px;">
                      <book-status xstype="{{item.isRecommend==1?2:item.xstype}}"></book-status>
                    </div>
                    <div show="{{isEdit}}" style="margin-left:7px;" class="image-cover"></div>
                    <image
                      style="left:17px;"
                      class="shelf-boook-item-del"
                      src="https://img.qdreads.com/v163_2/sj_sc.png"
                      show="{{isEdit}}"
                      @click="delShelfBook(item)"
                    ></image>
                  </stack>
                  <div style="flex-direction: column;margin-left:7px;">
                    <text class="shelf-boook-item-title">{{ item.bookName }}</text>
                    <text class="shelf-boook-item-author">{{ item.current?'已读'+item.current+'章':'未读' }}</text>
                  </div>
                </div>
              </div>
              <div show="{{!shelfDirection}}" style="width:100%;height: 181px;margin-top:40px;padding:0 30px;" for="(index, item) in shelfList" @click="toBookDetail(item)" @longpress="delShelfBook(item)">
                <stack>
                  <image
                    style="width: 128px;height: 181px;border-radius:6px;"
                    src="{{item.bookIconUrl}}"
                  ></image>
                  <book-status xstype="{{item.isRecommend==1?2:item.xstype}}"></book-status>
                  <div show="{{isEdit}}" style="width: 128px;height: 181px;border-radius:6px;" class="image-cover"></div>
                  <image
                    class="shelf-boook-item-del"
                    src="https://img.qdreads.com/v163_2/sj_sc.png"
                    show="{{isEdit}}"
                    @click="delShelfBook(item)"
                  ></image>
                </stack>
                <div style="height:100%;padding:6px 0;flex:1;margin-left:40px;flex-direction:column;">
                  <text style="height: 30px;font-size: 30px;color: #333333;lines:1;text-overflow:ellipsis;">{{ item.bookName }}</text>
                  <text style="height: 24px;color: #adadad;font-size: 24px;margin-top:18px;">已{{item.xstype==1?'完结':'更新'}}·共{{item.chapter_total}}章节</text>
                  <div style="margin-top:auto;">
                    <image src="https://img.qdreads.com/v173/tag.png" style="width: 24px;height: 24px;"></image>
                    <text style="height: 22px;color: #adadad;font-size: 22px;">{{item.current?'已读'+item.current+'章':'未读'}}</text>
                  </div>
                </div>
              </div>
          </block>
          <block else>
            <div
              type="shelf-boook-item"
              for="{{rootItem in boardLoadingList}}"
              style="padding: 0 56px"
            >
              <div
                class="shelf-boook-item"
                for="{{(index,item) in rootItem}}"
                style="{{index == 0 ? 'margin-left:0;' : ''}}"
              >
                <div
                  style="background-color: #EFEFEF"
                  class="shelf-boook-item-image"
                ></div>
                <div style="background-color: #EFEFEF;height: 50px;width: 100%;" class="shelf-boook-item-title"></div>
                <div style="background-color: #EFEFEF;height: 30px;width: 80px;" class="shelf-boook-item-author"></div>
              </div>
            </div>
          </block>
          <div if="{{getDataFinished && !shelfList.length}}" type="empty" class="empty">
            <div class="empty-section">
              <image
                class="empty-image"
                src="http://img.qdreads.com/image/2022-09-30/empty-new.png"
              ></image>
              <text class="empty-des">您还没有观看的书籍，赶快去看书吧~</text>
            </div>
          </div>
        </div>
        <!-- 更多弹窗 -->
        <div show="{{morePopShow}}" style="position:absolute;right:30px;top:74px;width: 249px;height: 230px;background-size:100%;background-repeat: no-repeat; background-image:url('https://img.qdreads.com/v173/sj_xf.png');flex-direction:column;">
          <div style="align-items:center;margin-top:9px;padding-left:23px;height:110px;" @click="editShelfBook">
            <image src="https://img.qdreads.com/v173/<EMAIL>" style="width: 28px;height: 28px;margin-right:8px;margin-top:3px;"></image>
            <text style="height: 28px;color: #ffffff;font-size: 28px;">{{isEdit?'退出':'书籍'}}管理</text>
          </div>
          <div style="width: 229px;height: 1px;background-color: rgba(255,255,255,0.17);margin-top:0px;margin-left:10px;"></div>
          <div style="align-items:center;margin-top:0px;padding-left:23px;height:110px;" @click="shiftDirection">
            <image src="https://img.qdreads.com/v173/{{shelfDirection?'sj_lb@2x':'sj_gg@2x'}}.png" style="width: 28px;height: 28px;margin-right:8px;margin-top:5px;"></image>
            <text style="height: 28px;color: #ffffff;font-size: 28px;">切换{{shelfDirection?'列表':'宫格'}}模式</text>
          </div>
        </div>
      </list-item>
      <block if="{{getDataFinished && shelfList.length}}">
        <list-item type="total-book">
          <text style="height: 20px;font-size: 20px;color: #acacac;margin:20px auto 0;">-您的书架中共 {{shelfList.length}} 本书-</text>
        </list-item>
        <list-item type="bottom-text">
          <text style="height: 20px;font-size: 20px;color: #acacac;margin:80px auto 56px;">已经到底了～</text>
        </list-item>
      </block>
      <list-item type="bottom-padding" style="width:100%;">
        <div if="{{isShowAddDesktop}}" style="width:100%;height:{{addDesktopHeight+20}}px"></div>
      </list-item>
      <list-item type="bottom-padding" style="width:100%;">
        <div if="{{isShowAddDesktop}}" style="width:100%;height:{{addDesktopHeight+20}}px"></div>
      </list-item>
    </list>

    <!-- 删除书籍 弹窗 -->
    <div class="dialog-wrap" if="{{isOpen}}">
      <div class="dialog-content">
        <text class="shelf-del-title">确定从书架删除这本书吗?</text>
        <div>
          <text class="shelf-del-cancel-btn" @click="shelfMaskChangeType"
            >取消</text
          >
          <text class="shelf-del-btn" @click="delShelfBookSubmit">确定</text>
        </div>
      </div>
    </div>
    <!-- 状态栏 -->
    <div 
      class="shelf-header" 
      show="{{selectIndex == 0}}" 
      style="
        padding-top: {{statusBarHeight}}px;
        height: {{statusBarHeight + 108}}px;
        background-color: rgba(255, 255, 255, {{opacity}});
        padding-right: {{menuBarLeft + 30}}px;
        top: 0;left: 0;
      "
    >
      <text class="shelf-header-title">趣点阅读</text>
      <div style="margin-left: auto;">
        <!-- <block if="{{!isEdit}}">
          <image style="width: 48px;height: 48px;" @click="toHistory" src="https://img.qdreads.com/v163/<EMAIL>"></image>
          <image style="margin-left: 30px;width: 48px;height: 48px;" @click="editShelfBook" src="https://img.qdreads.com/v163/<EMAIL>"></image>
        </block>
        <block else>
          <text style="font-size: 32px;color: #232730;height: 32px;font-weight:bold;" @click="editShelfBook">退出管理</text>
        </block> -->
      </div>
    </div>
  </div>
</template>

<import name="book-status" src="../../components/book-list/book-status/index.ux"></import> 

<script>
import device from '@system.device'

export default {
  props: {
    selectIndex: {
      type: undefined,
      default: 0
    },
    viewShowTimes: {
      type: Number,
      default: 0
    },
    backPopShow: {
      type: Number,
      default: 0
    },
    isShowAddDesktop: {
      default: false
    },
    addDesktopHeight: {
      default: 80
    },
  },
  data: {
    getDataFinished: false,
    isEdit: false, // 编辑书架状态   默认非编辑状态
    isOpen: false, // 书架产出弹窗是否打开状态值
    delBookInfo: {}, // 删除书籍信息
    shelfList: [], // 书架数据
    bannerList: [], // 热门推荐数据
    starArr: ['', '', '', '', ''], // 评分星星数组
    statusBarHeight: 40,
    boardList: [],
    listScrollY: 0,
    opacity: 0,
    boardLoadingList: [['', '', ''], ['', '', '']],
    menuBarLeft: 150,
    shelfGuideSecondOpacity: 1,
    appearGuideSecondTimes: 0,
    canEmit: true,
    swiperIndex: 0,
    shelfDirection: true,
    morePopShow: false,
    timer: '',
    transformClass: '',
  },
  createBoardList() {
    let strArr = []
    let len = this.shelfList.length, n = 3
    for (let i = 0; i < len / n; i++) {
      let a = this.shelfList.slice(n * i, n * (i + 1))
      strArr.push(a)
    }
    return this.boardList = strArr
  },
  /**
   * 页面初始化
   */
  async onInit() {
    this.statusBarHeight = sdk.env.device.statusBarHeight
    this.$watch('selectIndex', 'selectIndexChangeHandler')
    this.$watch('viewShowTimes', 'viewShowTimesChangeHandler')
    let res = await $utils.getStorage('shelf_layout')
    if (res) {
      this.shelfDirection = JSON.parse(res).shelf_direction
    }
    // this.getMenuData()
  },
  onReady() {

  },
  onDestroy() {

  },
  /**
   * 页面隐藏
   */
  onHide() { },
  selectIndexChangeHandler(newVal, oldVal) {
    this.isEdit = false
    if (newVal == 0 && this.$app.$def.tabListType[newVal] == 0) {
      this.getShelfList();
      this.getBannerList();
      this.$app.$def.tabListType[newVal] = 1
    }
    if (newVal !== 0 && this.isOpen && this.isEdit) {
      this.shelfMaskChangeType()
      this.isEdit = false;
    }
  },
  viewShowTimesChangeHandler(newVal, oldVal) {
    if (newVal == 1) return
    // if (this.selectIndex !== 0) return
    this.selectIndexChangeHandler(0, 100)
  },
  /**
   * 搜索框点击 跳转之搜索页
   */
  searchFocusHandler() {
    COMMON_REPORT_UTILS.page_click_report('搜索') //点击上报
    this.$emit('pageJumpHandle',{pageUrl:'/pagesB/Search',pageData:{pathUrl: "书架页"}})
  },
  /**
   * banner item点击函数
   * @param {Object} item 列表项
   */
  compClickHandler(item) {
    COMMON_REPORT_UTILS.page_click_report('热书推荐', '', `${item.bookId}`) //点击上报
     let params = {bookId: item.bookId,pathUrl: "书架页"}
    this.$emit('pageJumpHandle',{pageUrl:'/pagesC/Info',pageData:params})
  },
  // 热门推荐数据
  getBannerList() {
    $apis.example.shelfBannerApi({
      sex: this.$app.$def.sex
    }).then(res => {
      if (res.code == 200) {
        this.bannerList = res.data;
        this.$app.$def.uploadListShow(this.bannerList, '书架推荐')
      }
    })
  },
  // 书架数据
  getShelfList() {
    $apis.example.bookshelf({}).then(res => {
      this.getDataFinished = true
      if (res.code == 200) {
        this.shelfList = res.data
        if (this.canEmit) {
          this.canEmit = false
          this.$emit('bookListHandle', {eventName: 'guideFirst', bookInfo: this.shelfList[0]})
        }
        this.$app.$def.uploadListShow(this.shelfList, '书架历史')
        this.createBoardList()
      }
    }).catch(err => {
      this.getDataFinished = true
    })
  },
  // 编辑书籍状态   点击事件
  editShelfBook() {
    COMMON_REPORT_UTILS.page_click_report('编辑') //点击上报
    this.isEdit = !this.isEdit;
  },
  /**
   * 书架删除功能
   * item: 书籍信息
   */
  delShelfBook(bookInfo) {

    this.delBookInfo = bookInfo;
    this.shelfMaskChangeType()
  },
  /**
   * 书架删除-提交
   */
  delShelfBookSubmit() {
    $apis.example.removeBookshelf({
      bookId: this.delBookInfo.bookId
    }).then(res => {
      if (res.code == 200) {
        // 删除
        this.shelfList.splice(this.shelfList.findIndex(item => item.bookId === this.delBookInfo.bookId), 1)
        this.createBoardList()
        this.shelfMaskChangeType()
        this.getShelfList(); // 更新书架书籍
        $utils.showToast('已移出')
      }
    })

  },
  // 删除书架书籍-取消
  delShelfBookCancel() {
    this.isOpen = false;
  },
  /**
   * 更改删除小说弹窗状态
   */
  shelfMaskChangeType() {
    this.isOpen = !this.isOpen
  },
  /**
  * 书架-打开书本详情页
  * item: 书籍信息
  */
  toBookDetail(bookItem) {
    if (!this.isEdit) { //过滤掉编辑时的点击
      if (bookItem.tag == 2) {
        // 书架推荐
        COMMON_REPORT_UTILS.list_click_report('1', [`${bookItem.bookId}`], '书架推荐') //点击上报
      } else {
        // 历史小说
        COMMON_REPORT_UTILS.list_click_report('1', [`${bookItem.bookId}`], '书架历史') //点击上报
      }

      var params = {
        bookId: bookItem.bookId,
        chapterId: bookItem.chapter.chapterId,
        pathUrl: "书架页"
      }
      this.$emit('pageJumpHandle',{pageUrl:'/pagesC/Read',pageData:params})
    }
  },

  listScroll(evt) {
    this.listScrollY += evt.scrollY
    if (this.listScrollY >= 0) {
      this.opacity = Math.min(this.listScrollY / 300, 1)
    }
  },

  getMenuData() {
    if (this.$app.$def.brand == 'huawei') return
    let that = this
    try {
      let menuBarRect = that.$page.getMenuBarRect()
      if (menuBarRect && menuBarRect.menuBarWidth > 0) {
        device.getInfo({
          success: function (ret) {
            let radio = 750 / ret.screenWidth
            if (that.$app.$def.brand == 'vivo') radio = 1
            that.menuBarLeft = that.keepTwoDecimal((750 - menuBarRect.menuBarLeft * radio))
          }
        })
      }
    } catch (e) { }
  },
  keepTwoDecimal(num) {
    var result = parseFloat(num)
    if (isNaN(result)) {
      return false
    }
    return Math.ceil(Math.round(num * 100) / 100)
  },
  toHistory() {
    this.$emit('pageJumpHandle',{pageUrl:'/pagesA/Read-History',pageData:{pathUrl: "书架页"}})
    COMMON_REPORT_UTILS.page_click_report('浏览记录') //点击上报
  },
  swiperChangeHandle({ index }) {
    this.swiperIndex = index
    
  },
  //阅读人数处理 万级 处理成小数点
  readNumHandle(readNum) {
    if (Number(readNum) > 10000) {
      return (Number(readNum) / 10000).toFixed(1) + '万'
    }
    return readNum
  },
  moreBtnClick() {
    this.morePopShow = !this.morePopShow
  },
  shiftDirection() {
    this.moreBtnClick()
    this.transformClass = 'show-loading'
    this.shelfDirection = !this.shelfDirection
    let timer1 = setTimeout(() => {
      this.transformClass = 'hide-loading'
      clearTimeout(timer1)
    }, 1000);
    let timer2 = setTimeout(() => {
      this.transformClass = ''
      clearTimeout(timer2)
    }, 1300);
    $utils.setStorage('shelf_layout', {shelf_direction: this.shelfDirection})
  },
  startLottie(index) {
    if (this.$element(`banner${index}`)) {
      this.$element(`banner${index}`).play()
    }
  }
}
</script>

<style lang="less">
@import '../../assets/styles/style.less';
@import './index.less';
</style>
