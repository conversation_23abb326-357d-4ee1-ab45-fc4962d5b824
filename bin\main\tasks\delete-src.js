// ==================== 删除生成文件 ===========================
const { consoleSplit } = require('../../utils/console')
const fs = require('fs')
const path = require('path')
const { projectPath } = require('../constant')

function deleteGenerateFile() {
  consoleSplit('删除生成文件 src sign')
  const signPath = path.resolve(projectPath, 'sign')
  const srcPath = path.resolve(projectPath, 'src')
  if (fs.existsSync(signPath)) {
    fs.rmSync(signPath, { recursive: true })
  }

  if (fs.existsSync(srcPath)) {
    fs.rmSync(srcPath, { recursive: true })
  }

  console.log('删除生成文件 src sign 成功')
}

function deleteSrcNoSDK() {
  consoleSplit('删除生成文件 src')
  const srcDir = path.resolve(projectPath, 'src') // 根据实际路径修改

  // 读取 src 目录
  const files = fs.readdirSync(srcDir)

  files.forEach(file => {
    const filePath = path.join(srcDir, file)

    // 检查是否是 cy-sdk 文件夹，如果不是就删除
    if (file !== 'cy-sdk' && file !== '_config.json') {
      fs.rmSync(filePath, { recursive: true, force: true })
    }
  })
}

module.exports = { deleteGenerateFile, deleteSrcNoSDK }
