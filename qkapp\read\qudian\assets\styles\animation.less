/**
*   CY animation 动画库
*   @animation-name     roll-out-bottom          元素掉落动画
*   @animation-name     blink-1                  元素闪烁动画
*   @animation-name     rotate-center            中心旋转动画
*   @animation-name     scale-up-center          元素放大动画
*   @animation-name     scale-down-center        元素缩小动画
*   @animation-name     scale-down-up-center     元素线性放大缩小动画 
*   @animation-name     scale-up-down-center     元素线性缩小放大动画 
*   @animation-name     scale-down-up-large-center    元素线性放大缩小动画 1 - 1.2 - 1
*   @animation-name     shake-lr                 元素旋转抖动动画 中心点
*   @animation-name     shake-horizontal         元素左右摆动动画 水平
*   @animation-name     wobble-hor-bottom        元素左右摇晃动画 中心点在中下方
*/

// 元素掉落动画
.roll-out-bottom {
    animation-name: rollOutBottom;
    animation-duration: 600ms;
    animation-timing-function: ease-in;
    animation-fill-mode: forwards;
}

@keyframes rollOutBottom {
    0% {
        transform: translateY(0) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: translateY(1624px) rotate(540deg);
        opacity: 0;
    }
}

// 元素闪烁动画
.blink-1 {
    animation-name: blinkA;
    animation-duration: 1500ms;
    animation-fill-mode: forwards;
    animation-iteration-count: infinite;
}

@keyframes blinkA {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 1;
    }
    100% {
        opacity: 1;
    }
    25% {
        opacity: 0;
    }
    75% {
        opacity: 0;
    }
}

// 中心旋转动画
.rotate-center {
    animation-name: rotateCenter;
    animation-duration: 4000ms;
    animation-timing-function: linear;
    animation-fill-mode: forwards;
    animation-iteration-count: infinite;
}

@keyframes rotateCenter {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

// 元素放大动画
.scale-up-center {
    animation-name: scaleUpCenter;
    animation-duration: 300ms;
    animation-timing-function: linear;
    animation-fill-mode: forwards;
}

@keyframes scaleUpCenter {
    0% {
        transform: scale(0);
    }
    100% {
        transform: scale(1);
    }
} 
//元素缩小动画
.scale-down-center {
    animation-name: scaleDownCenter;
    animation-duration: 300ms;
    animation-timing-function: linear;
    animation-fill-mode: forwards;
}

@keyframes scaleDownCenter {
    0% {
        transform: scale(1);
    }
    100% {
        transform: scale(0);
    }
} 
//元素线性放大缩小动画
.scale-down-up-center {
    animation-name: scaleDownUpCenter;
    animation-duration: 1500ms;
    animation-timing-function: linear;
    animation-fill-mode: forwards;
    animation-iteration-count: infinite;
}

@keyframes scaleDownUpCenter {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(0.8);
    }
    100% {
        transform: scale(1);
    }
}
//元素线性缩小放大动画
.scale-up-down-center {
    animation-name: scaleUpDownCenter;
    animation-duration: 1500ms;
    animation-timing-function: linear;
    animation-fill-mode: forwards;
    animation-iteration-count: infinite;
}

@keyframes scaleUpDownCenter {
    0% {
        transform: scale(0.8);
    }
    50% {
        transform: scale(1);
    }
    100% {
        transform: scale(0.8);
    }
}
// 元素旋转抖动 中心点
.shake-lr {
    animation-name: shakeLr;
    animation-duration: 1000ms;
    animation-timing-function: cubic-bezier(0.455, 0.030, 0.515, 0.955);
    animation-fill-mode: forwards;
    animation-iteration-count: infinite;
}

@keyframes shakeLr {
    0% {
        transform: rotate(0deg);
    }
    10% {
        transform: rotate(8deg);
    }
    20% {
        transform: rotate(-10deg);
    }
    40% {
        transform: rotate(-10deg);
    }
    60% {
        transform: rotate(-10deg);
    }
    30% {
        transform: rotate(10deg);
    }
    50% {
        transform: rotate(10deg);
    }
    70% {
        transform: rotate(10deg);
    }
    80% {
        transform: rotate(-8deg);
    }
    90% {
        transform: rotate(8deg);
    }
    100% {
        transform: rotate(0deg);
    }
}

// 元素左右摆动 水平方向
.shake-horizontal {
    animation-name: shakeHorizontal;
    animation-duration: 800ms;
    animation-timing-function: cubic-bezier(0.455,0.030,0.515,0.955);
    animation-fill-mode: forwards;
}

@keyframes shakeHorizontal {

    0% {
        transform: translateX(0);
    }

    100% {
        transform: translateX(0);
    }

    10% {
        transform: translateX(-10px);
    }

    30% {
        transform: translateX(-10px);
    }

    50% {
        transform: translateX(-10px);
    }

    70% {
        transform: translateX(-10px);
    }

    20% {
        transform: translateX(10px);
    }

    40% {
        transform: translateX(10px);
    }

    60% {
        transform: translateX(10px);
    }

    80% {
        transform: translateX(8px);
    }

    90% {
        transform: translateX(-8px);
    }
}

// 元素左右摇晃  中心点在中下方
.wobble-hor-bottom {
    animation-name: wobbleHorBottom;
    animation-duration: 800ms;
    animation-fill-mode: forwards;
}

@keyframes wobbleHorBottom {

    0% {
        transform: translateX(0%);
    }

    15% {
        transform: translateX(-30px) rotate(-6deg);
    }

    30% {
        transform: translateX(15px) rotate(6deg);
    }

    45% {
        transform: translateX(-15px) rotate(-3.6deg);
    }

    60% {
        transform: translateX(9px) rotate(2.4deg);
    }

    75% {
        transform: translateX(-6px) rotate(-1.2deg);
    }
    100% {
        transform: translateX(0%);
    }
}

// 元素上下摆动 垂直方向
.shake-vertical {
    animation-name: shakeVertical;
    animation-duration: 800ms;
    animation-timing-function: cubic-bezier(0.455,0.030,0.515,0.955);
    animation-fill-mode: forwards;
}

@keyframes shakeVertical {

    0% {
        transform: translateY(0);
    }

    100% {
        transform: translateY(0);
    }

    10% {
        transform: translateY(-8px);
    }

    30% {
        transform: translateY(-8px);
    }

    50% {
        transform: translateY(-8px);
    }

    70% {
        transform: translateY(-8px);
    }

    20% {
        transform: translateY(8px);
    }

    40% {
        transform: translateY(8px);
    }

    60% {
        transform: translateY(8px);
    }

    80% {
        transform: translateY(6.4px);
    }

    90% {
        transform: translateY(-6.4px);
    }
}
.ad-bg-tag{
    width: 46px;
    height: 22px;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 4px;
    font-size: 18px;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    color: #ffffff;
    line-height: 22px;
    text-align: center;
    position: absolute;
    bottom: 6px;
    right: 6px;
}
//元素线性放大缩小动画 1 - 1.2 - 1
.scale-down-up-large-center {
    animation-name: scaleUpDownLargeCenter;
    animation-duration: 1500ms;
    animation-timing-function: linear;
    animation-fill-mode: forwards;
    animation-iteration-count: infinite;
}

@keyframes scaleUpDownLargeCenter {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

.bounce-bottom {
    animation-name: bounce_bottom;
    animation-duration: 6s;
    animation-fill-mode: forwards;
    animation-iteration-count: infinite;
}

@keyframes bounce_bottom {
    0% {
        transform: translateY(0px);
        animation-timing-function: ease-in;
        opacity: 1;
    }

    2% {
        transform: translateY(-45px);
        animation-timing-function: ease-in;
        opacity: 1;
    }

    4% {
        transform: translateY(0px);
        animation-timing-function: ease-out;
    }

    6% {
        transform: translateY(-24px);
        animation-timing-function: ease-in;
    }

    8% {
        transform: translateY(0px);
        animation-timing-function: ease-out;
    }

    10% {
        transform: translateY(-12px);
        animation-timing-function: ease-in;
    }

    12% {
        transform: translateY(0px);
        animation-timing-function: ease-out;
    }

    14% {
        transform: translateY(-6px);
        animation-timing-function: ease-in;
    }

    16% {
        transform: translateY(0px);
        animation-timing-function: ease-out;
    }

    18% {
        transform: translateY(-4px);
        animation-timing-function: ease-in;
    }

    20% {
        transform: translateY(0px);
        animation-timing-function: ease-out;
        opacity: 1;
    }

    100% {
        transform: translateY(0px);
        animation-timing-function: ease-out;
        opacity: 1;
    }
}