<template>
  <!-- 4个 只有一行 横板排列 -->
    <div class="outer-wrapper {{styleArr[styleType].bgColorClass}}">
      <div style="align-items:center;justify-content:space-between;margin-left:30px;margin-right:30px;">
        <image style="width: 153px;height: 36px;" src="{{styleArr[styleType].imgUrl}}"></image>
        <text style="height: 36px;font-size: 24px;color: #999999;" @click="changeList">换一换</text>
      </div>
      <text style="height: 28px;font-size: 28px;color: #333333;margin-top:30px;margin-left:30px;margin-right:30px;">这些小说太有意思了!笑的停不下来！</text>
      <div style="margin-left:23px;margin-right:23px;">
        <div for="{{(index, item) in subList}}" style="margin-left: {{index % 4 == 0 ? '0' : '24'}}px;
          margin-top: 30px;flex-direction:column;width:142px;" @click="compClickHandler(item)">
          <stack>
            <image src="https://img.qdreads.com/v173/sjyy_dcc.png" style="width:142px;height:189px;margin-top:7px;"></image>
            <div style="margin-left:7px;"> 
              <image
                src="{{item.bookIconUrl}}"
                style="object-fit: fill;width:128px;height:181px;border-radius: 6px;"
              ></image>
              <!-- <book-status xstype="{{item.xstype}}"></book-status> -->
            </div>
          </stack>
          <div style="height:76px;margin-top:10px;align-items:flex-start;">
            <text style="color: #333333;font-size: 26px;line-height: 36px;lines: 2;text-overflow: ellipsis;">{{item.bookName}}</text>
          </div>
          <text style="color: #ff3d4f;font-size: 22px;height: 22px;margin-top: 10px;flex-shrink: 0;">{{ item.score | formatScore }} 分</text>
        </div>
      </div>
    </div>
</template>

<!-- <import name="book-status" src="../book-status/index.ux"></import> -->

<script>
export default {
  props: {
    novelList: {
      type: Array,
      default: []
    },
    title: {
      type: String,
      default: ''
    },
    styleType: {
      type: Number,
      default: 0
    }
  },
  data: {
    styleArr: [
      {
        imgUrl: "https://img.qdreads.com/v173/sc_np_hot.png",
        bgColorClass: 'bgStyle1'
      },
      {
        imgUrl: 'https://img.qdreads.com/v173/sc_vp_hot.png',
        bgColorClass: 'bgStyle2'
      },
      {
        imgUrl: 'https://img.qdreads.com/v173/sc_hy_hot.png',
        bgColorClass: 'bgStyle3'
      },
    ]
  },
  computed: {
    subList() {
      return this.novelList.slice(0, 4)
    }
  },
  compClickHandler(item) {
    if (item === '') return;
    this.$emit('compClick', { bookId: item.bookId ,bookName: item.bookName })
  },
  formatScore(value) {
    if (!value) return ''
    return value / 10
  },
  changeList() {
    LOG('changeList1')
    this.$emit('changeClick', )
  }
}
</script>

<style lang="less">
.outer-wrapper {
  width: 690px;
  background-color: #ffffff;
  border-radius: 24px;
  flex-direction: column;
  padding: 30px 0;
}

.bgStyle1 {
  background: linear-gradient(180deg,#d8edff, #ffffff 134px, #ffffff 100%);
}

.bgStyle2 {
  background: linear-gradient(180deg,#ffe9ef, #ffffff 134px, #ffffff 100%);
}

.bgStyle3 {
  background: linear-gradient(180deg,#ffeede, #ffffff 134px, #ffffff 100%);
}
</style>

