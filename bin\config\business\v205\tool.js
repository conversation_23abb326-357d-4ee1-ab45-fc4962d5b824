// 要修改的文件及其对应的修改内容
const baseConfig = require('./base')

const publicFile = []

/**
 * 配置文件
 * @type {BusinessConfig[]}
 */
const appFile = [
  {
    filePath: './page/pages/Main/index.ux',
    modifications: [
      {
        oldCode: `<exit-icon if="{{isShowQuitButton}}"></exit-icon>`,
        newCode: `<exit-icon if="{{tacticsStatus}}"></exit-icon>`,
        desc: '1. 退出按钮判断条件修改',
        type: 'replace'
      },
      {
        oldCode: `onBackPress`,
        newCode: `onBackPress() {
    return sdk.backPress.handleBackPress()
  }`,
        desc: 'onBackPress 使用新的方法',
        type: 'replaceFun'
      },
      {
        oldCode: `bqtVideoAdControllerEventDispatchHandler`,
        newCode: ``,
        desc: '删除bqtVideoAdControllerEventDispatchHandler',
        type: 'deleteFun'
      },
      {
        oldCode: `await appUtils.page_ad_comp_init.call(this, true, 'main_ad_circle', 'is_pageHome_allow_back_time')`,
        newCode: `await appUtils.page_ad_comp_init.call(this, true,'home')`,
        type: 'replace',
        desc: 'onInit里修改page_ad_comp_init方法传参'
      },
      {
        oldCode: `changeTab`,
        newCode: `changeTab(evt) { 
    if (this.activeTab === evt.index) return;
    let index = evt.index === undefined ? 1 : evt.index;
    this.activeTab = index;
    POP_TOOLS.commonFunc2PatchPopShow({ actionCode: 'PAGE_ENTRY', code: '', customAction: {} })
    sdk.tactics.setQuery(\`activeTab=\${this.activeTab}\`)
    COMMON_REPORT_UTILS.page_click_report(this.tabList[this.activeTab].title);
    const pageName = sdk.tactics.getPageInfo()?.pageName
    if (!pageName) return
    sdk.report({
      event_id: 'page_show',
      event_name: 'n_页面展示',
      page_url: pageName
    })
  }`,
        desc: 'tab bar 的change事件回调修改',
        type: 'replaceFun'
      },
      {
        oldCode: `public.isShowBanner`,
        newCode: ``,
        desc: '删除isShowBanner',
        type: 'deleteAttr'
      },
      {
        oldCode: `public.isShowPreloadImg`,
        newCode: ``,
        desc: '删除isShowPreloadImg',
        type: 'deleteAttr'
      },
      {
        oldCode: `public.isShowBoostComp`,
        newCode: ``,
        desc: '删除isShowBoostComp',
        type: 'deleteAttr'
      },
      {
        oldCode: `public.isShowNativeBoostGroupComp`,
        newCode: ``,
        desc: '删除isShowNativeBoostGroupComp',
        type: 'deleteAttr'
      },
      {
        oldCode: `public.firstAdLoadComplete`,
        newCode: ``,
        desc: '删除firstAdLoadComplete',
        type: 'deleteAttr'
      },
      {
        oldCode: `public.videoBqtConfig`,
        newCode: ``,
        desc: '删除videoBqtConfig',
        type: 'deleteAttr'
      },
      {
        oldCode: `public.isShowNativeBoostGroupComp`,
        newCode: ``,
        desc: '删除isShowNativeBoostGroupComp',
        type: 'deleteAttr'
      },
      {
        oldCode: `public.nativeBoostGroupController`,
        newCode: ``,
        desc: '删除nativeBoostGroupController',
        type: 'deleteAttr'
      },
      {
        oldCode: `COMMON_REPORT_UTILS.page_show_report(that.$app.$def.pageCodeInfo.pathUrl)`,
        newCode: ``,
        desc: '上报SDK处理',
        type: 'delete'
      },
      {
        oldCode: `REPORT_SDK && REPORT_SDK.page_show()`,
        newCode: ``,
        desc: '上报SDK处理',
        type: 'delete'
      },
      {
        oldCode: `that.nativeBoostGroupController.startTimeout++`,
        newCode: ``,
        desc: 'nativeBoostGroupController相关删除',
        type: 'delete'
      },
      {
        oldCode: `this.nativeBoostGroupController.stopTimeout++`,
        newCode: ``,
        desc: 'nativeBoostGroupController相关删除',
        type: 'delete'
      },
      {
        oldCode: `<import name="preload-img" src="../../sdk-components/preload-img/index.ux"></import>`,
        newCode: ``,
        desc: 'preload-img相关删除',
        type: 'delete'
      },
      {
        oldCode: `<import name="custom-fill-comp" src="../../sdk-components/custom-pop-group/custom-fill-comp/index.ux"></import>`,
        newCode: ``,
        desc: 'custom-fill-comp删除',
        type: 'delete'
      },
      {
        oldCode: `<import name="custom-pop-group" src="../../cy-sdk/action/components/custom-pop-group/index.ux"></import>`,
        newCode: ``,
        desc: 'custom-vpop-group删除',
        type: 'delete'
      },
      {
        oldCode: `<import name="custom-vpop-group" src="../../sdk-components/custom-vpop-group/index.ux"></import>`,
        newCode: `<import name="custom-pop-group" src="../../cy-sdk/action/components/custom-pop-group/index.ux"></import>`,
        desc: 'custom-vpop-group删除',
        type: 'replace'
      },
      // 组件
      {
        oldCode: `div.custom-pop-group`,
        newCode: `<div class="native-boost-group" style="width: 750px; height: 500px; opacity: 0;position:absolute;">
      <custom-pop-group
        onevent-watch="nativeBoostGroupEventDispatchHandler"
        onappear="popComponmentAppearHandler"
      ></custom-pop-group>
    </div>`,
        desc: 'native-boost-group替换',
        type: 'replaceTag'
      },
      {
        oldCode: `div.custom-fill-comp`,
        newCode: '',
        desc: 'custom-fill-comp 使用组件删除',
        type: 'replaceTag'
      },
      {
        oldCode: `block.preload-img`,
        newCode: '',
        desc: 'preload-img 使用组件删除',
        type: 'replaceTag'
      },
      {
        oldCode: `<custom-vpop-group
      if="{{videoBqtConfig.isShow}}"
      pop-code="{{videoBqtConfig.popCode}}"
      action-code="{{videoBqtConfig.actionCode}}"
      custom-action="{{videoBqtConfig.customAction}}"
      onevent-watch="bqtVideoAdControllerEventDispatchHandler"
      back-press-times="{{videoBqtConfig.backPressTimes}}"
    ></custom-vpop-group>`,
        newCode: '',
        desc: 'custom-vpop-group 删除',
        type: 'delete'
      },
      {
        oldCode: ``,
        newCode: ``,
        desc: '添加pageMixin',
        type: 'addPageMixin'
      }
    ]
  },
  {
    filePath: 'app.ux',
    modifications: [
      {
        desc: 'app.ux 替换',
        type: 'appUxReplace',
        oldCode: `script`,
        newCode: `import commonApp from "./utils/app_ux_public.js"

const hook2global = global.__proto__ || global
hook2global.$shelfList = []
hook2global.$collectList = []

export default appMixin({
  $shelfList: hook2global.$shelfList,
  $collectList: hook2global.$collectList,
  selectChapter: '',
  ...commonApp,
  onCreate: commonApp.onCreatePublic,
  onShow: function () {
    commonApp.onShowPublic(this)
  },
  onHide: function () {
    commonApp.onHidePublic(this)
  },
  onError: function (err) {
    commonApp.onErrorPublic(this, err)
  },
  onDestroy: function () {
    commonApp.onDestroyPublic(this)
  }
})`
      }
    ]
  }
]

module.exports = {
  publicFile: [...baseConfig.publicFile, ...publicFile],
  appFile: [...baseConfig.appFile, ...appFile]
}
