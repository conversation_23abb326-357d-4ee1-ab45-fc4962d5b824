<template>
  <!-- template里只能有一个根节点 -->
  <div class="store-wrapper">
    <div if="{{headerTabData.index == 1}}" class="abs-bg-male" style="top:-{{scrollDataArr[Number(headerTabData.index)].searchHeight}}px;"></div>
    <div elif="{{headerTabData.index == 3}}" class="abs-bg-vip" style="top:0;"></div>
    <div else class="abs-bg-normal" style="top:-{{scrollDataArr[Number(headerTabData.index)].searchHeight}}px;"></div>
    <tabs style="width: 100%;height: 100%;"  index="{{headerTabData.index}}" @change="handleTabChange">
      <tab-content style="width: 100%;height: 100%;" >
        <!-- 推荐 -->
        <list class="tabs-list" @scroll="contentSwipeHandle">
          <list-item type="header">
            <common-header></common-header>
          </list-item>
          <list-item type="ad" style="width:100%;height: 284px;">
          </list-item>

          <list-item type="row-item" if="{{recommendList[0] && recommendList[0].data.length}}" onappear="itemShow('推荐-'+recommendList[0].title,recommendList[0].data)" style="margin-top: 4px;">
            <common-novel-row-item novel-list="{{recommendList[0].data}}" title="{{recommendList[0].title}}" @comp-click="compClickHandler(recommendList[0].title)"
            ></common-novel-row-item>
          </list-item>

          <list-item type="coverflow-swiper" if="{{recommendList[1] && recommendList[1].data.length}}" onappear="itemShow('推荐-'+recommendList[1].title,recommendList[1].data)" style="margin-top: 30px;">
            <coverflow-swiper novel-list="{{recommendList[1].data}}" title="{{recommendList[1].title}}" @comp-click="compClickHandler(recommendList[1].title)"
            ></coverflow-swiper>
          </list-item>

          <list-item type="novel-list-recommand" if="{{recommendList[2] && recommendList[2].data.length}}" onappear="itemShow('推荐-'+recommendList[2].title,recommendList[2].data)" style="margin-top: 30px;">
            <common-swipe-novel-list novel-list="{{recommendList[2].data}}" title="{{recommendList[2].title}}" @comp-click="compClickHandler(recommendList[2].title)" @to-recommand-page="toRecommandPage"
            ></common-swipe-novel-list>
          </list-item>

          <list-item type="row-four" if="{{recommendList[3] && recommendList[3].data.length}}" onappear="itemShow('推荐-'+recommendList[3].title,recommendList[3].data)" style="margin-top: 30px;">
            <novel-list-row-four novel-list="{{recommendList[3].data}}" title="{{recommendList[3].title}}" @comp-click="compClickHandler(recommendList[3].title)"
            ></novel-list-row-four>
          </list-item>

          <list-item type="row-one" if="{{recommendList[4] && recommendList[4].data.length}}" onappear="itemShow('推荐-'+recommendList[4].title,recommendList[4].data)" style="margin-top: 30px;">
            <novel-list-row-one novel-list="{{recommendList[4].data}}" title="{{recommendList[4].title}}" @comp-click="compClickHandler(recommendList[4].title)" @to-recommand-page="toRecommandPage"
            ></novel-list-row-one>
          </list-item> 

          <list-item type="bottom-padding" style="width:100%;">
              <div if="{{isShowAddDesktop || isKeepRead}}" style="width:100%;height:{{bottomHeight}}px"></div>
              <div else style="height:30px;"></div>
          </list-item>
        </list>

          <!-- 男频 -->
        <list class="tabs-list" @scroll="contentSwipeHandle">
          <list-item type="header">
          <common-header></common-header>
          </list-item>
          <list-item type="ad" style="width:100%;height: 284px;">
          </list-item>

          <list-item type="row-item" if="{{maleList[0] && maleList[0].data.length}}" onappear="itemShow('男频-'+maleList[0].title,maleList[0].data)" style="margin-top: 4px;">
            <common-novel-row-item item-type="male" novel-list="{{maleList[0].data}}" title="{{maleList[0].title}}" @comp-click="compClickHandler(maleList[0].title)"
            ></common-novel-row-item>
          </list-item>

          <list-item type="novel-list-male" if="{{maleList[1] && maleList[1].data.length}}" style="margin-top: 30px;">
            <common-swipe-novel-list item-type="male" novel-list="{{maleList[1].data}}" title="{{maleList[1].title}}" @comp-click="compClickHandler(maleList[1].title)" @to-recommand-page="toRecommandPage"
              @list-appear="itemShow('男频-'+maleList[1].title, maleList[1].data)"
            ></common-swipe-novel-list>
          </list-item>

          <list-item type="row-four" onappear="itemShow('男频-'+maleList[2].title,maleList[2].data)" if="{{maleList[2] && maleList[2].data.length}}" style="margin-top: 30px;width:750px;">
            <novel-list-row-four item-type="male" novel-list="{{maleList[2].data}}" title="{{maleList[2].title}}" @comp-click="compClickHandler(maleList[2].title)"
            ></novel-list-row-four>
          </list-item>

          <list-item type="male-one-four" onappear="itemShow('男频-'+maleList[3].title, maleList[3].data)" if="{{maleList[3] && maleList[3].data.length}}" style="margin-top: 30px;">
            <novel-list-one-four novel-list="{{maleList[3].data}}" title="{{maleList[3].title}}" @comp-click="compClickHandler(maleList[3].title)"
              style-type="{{0}}" @change-click="changeListData"
            ></novel-list-one-four>
          </list-item>

          <list-item type="row-one" onappear="itemShow('男频-'+maleList[4].title, maleList[4].data)" if="{{maleList[4] && maleList[4].data.length}}" style="margin-top: 30px;">
            <novel-list-row-one item-type="male" novel-list="{{maleList[4].data}}" title="{{maleList[4].title}}" @comp-click="compClickHandler(maleList[4].title)" @to-recommand-page="toRecommandPage"
            ></novel-list-row-one>
          </list-item>

          <list-item type="bottom-padding" style="width:100%;">
              <div if="{{isShowAddDesktop || isKeepRead}}" style="width:100%;height:{{bottomHeight}}px"></div>
              <div else style="height:30px;"></div>
          </list-item>
        </list>

        <!-- 女频 -->
        <list class="tabs-list" @scroll="contentSwipeHandle">
          <list-item type="header">
          <common-header></common-header>
          </list-item>
          <list-item type="ad" style="width:100%;height: 284px;">
          </list-item>

          <list-item type="row-item" if="{{femaleList[0] && femaleList[0].data.length}}" onappear="itemShow('女频-'+femaleList[0].title,femaleList[0].data)" style="margin-top: 4px;">
            <common-novel-row-item novel-list="{{femaleList[0].data}}" title="{{femaleList[0].title}}" @comp-click="compClickHandler(femaleList[0].title)"
            ></common-novel-row-item>
          </list-item>

          <list-item type="novel-list-female" onappear="itemShow('女频-'+femaleList[1].title,femaleList[1].data)" if="{{femaleList[1] && femaleList[1].data.length}}" style="margin-top:30px;">
            <common-swipe-novel-list novel-list="{{femaleList[1].data}}" title="{{femaleList[1].title}}" @comp-click="compClickHandler(femaleList[1].title)" @to-recommand-page="toRecommandPage"
            ></common-swipe-novel-list>
          </list-item>

          <list-item type="row-four" onappear="itemShow('女频-'+femaleList[2].title,femaleList[2].data)" if="{{femaleList[2] && femaleList[2].data.length}}" style="margin-top: 30px;width:750px;">
            <novel-list-row-four novel-list="{{femaleList[2].data}}" title="{{femaleList[2].title}}" @comp-click="compClickHandler(femaleList[2].title)"
            ></novel-list-row-four>
          </list-item>

          <list-item type="female-one-four" onappear="itemShow('女频-'+femaleList[3].title,femaleList[3].data)" if="{{femaleList[3] && femaleList[3].data.length}}" style="margin-top: 30px;">
            <novel-list-one-four novel-list="{{femaleList[3].data}}" title="{{femaleList[3].title}}" @comp-click="compClickHandler(femaleList[3].title)"
              style-type="{{1}}" @change-click="changeListData"
            ></novel-list-one-four>
          </list-item>

          <list-item type="row-one" onappear="itemShow('女频-'+femaleList[4].title,femaleList[4].data)" if="{{femaleList[4] && femaleList[4].data.length}}" style="margin-top: 30px;">
            <novel-list-row-one novel-list="{{femaleList[4].data}}" title="{{femaleList[4].title}}" @comp-click="compClickHandler(femaleList[4].title)" @to-recommand-page="toRecommandPage"
            ></novel-list-row-one>
          </list-item>
          <list-item type="bottom-padding" style="width:100%;">
              <div if="{{isShowAddDesktop || isKeepRead}}" style="width:100%;height:{{bottomHeight}}px"></div>
              <div else style="height:30px;"></div>
          </list-item>
        </list>

      </tab-content>
    </tabs>
    <div style="top: -{{0}}px;background-color:{{headerTabData.index == 3?vipBgColor:'transparent'}};" 
      class="store-header-wrapper header-wrapper-index-{{headerTabData.index}}">
        <div class="store-top-header">
          <common-header></common-header>
          <!-- <div style="margin-bottom:16px;"></div> -->
          <div style="width:750px;height:{{184 - scrollDataArr[Number(headerTabData.index)].searchHeight}}px;">
            <div style="height: 68px;margin-left:30px;margin-top:20px;">
              <stack @click="showInteresting">
                <image src="https://img.qdreads.com/v173/<EMAIL>" style="width:68px;height: 68px;opacity:{{1-scrollDataArr[Number(headerTabData.index)].searchOpacity}}"></image>
                <block>
                  <lottie class="intre-lottie" style="opacity:{{scrollDataArr[Number(headerTabData.index)].searchOpacity}};" source="https://img.qdreads.com/lottie/rk.json" autoplay={{true}} loop={{true}}></lottie>
                </block>
                <!-- <block else>
                  <image class="intre-lottie" style="opacity:{{scrollDataArr[Number(headerTabData.index)].searchOpacity}};" src="https://img.qdreads.com/v173/sc_ydxh.png"></image>
                </block> -->
                <!-- show="{{scrollDataArr[Number(headerTabData.index)].searchOpacity!=0}}" -->
              </stack>
            </div>
            <div style="width:{{690 - scrollDataArr[Number(headerTabData.index)].searchWidth}}px;left:{{30+scrollDataArr[Number(headerTabData.index)].searchLeft}}px;top:{{108-scrollDataArr[Number(headerTabData.index)].searchTop}}px;"
              class="store-header-search {{headerTabData.index == 3 ? 'selected-vip' : ''}}" @click="searchTabClick">
              <text class="search-title {{headerTabData.index == 3 ? 'selected-vip-title' : ''}}">无上神帝</text>
            </div>
          </div>
          <!-- <div style="margin-top:20px;"></div> -->
        </div> 
        <div style="padding: 0 30px;height: 100px;align-items: center;">
          <div
              for="{{headerTabData.list}}"
              @click="sexTabClick($idx)"
              class="tab-item"
              style="padding-left: {{$idx == 0 ? '0' : '50'}}px"
            >
              <block if="{{$item.imgPath}}">
                <image if="{{headerTabData.index != $idx}}" src="{{$item.imgPath}}" class="tab-item-image"></image>
                <image else src="{{$item.imgPathSelected}}" style="width: 99px;height: 44px;"></image>
              </block>
              <text else class="tab-item-title {{headerTabData.index == $idx ? 'item-title-selected' : ''}}">
                {{$item.title}}
              </text>
              <div if="{{headerTabData.index == $idx}}" style="margin: 8px auto 0">
                <div style="width: 8px;height: 8px;border-top-left-radius: 100%;background-color: {{$item.imgPath ? '#ffdcb4' : '#f11212'}};"></div>
                <div style="width: 30px;height: 8px;background-color: {{$item.imgPath ? '#ffdcb4' : '#f11212'}};"></div>
                <div style="width: 8px;height: 8px;border-bottom-right-radius: 100%;background-color: {{$item.imgPath ? '#ffdcb4' : '#f11212'}};"></div>
              </div>
              <div else style="width: 30px;height: 14px;"></div>
            </div>
        </div>
       </div>
    <!-- 支付出错弹窗 -->
    <common-loading loading="{{loading}}"></common-loading>
    <!-- 自定义toast -->
    <block if="{{customToastShow}}">
      <my-toast toast-text="{{customToastText}}"></my-toast>
    </block>
  </div>
</template>

<import name="common-loading" src="../../components/loading/index.ux"></import>
<import name="common-header" src="../components/common-header/index"></import>
<import name="common-swipe-novel-list" src="../../components/book-list/common-swipe-novel-list/index.ux"></import>
<import name="common-novel-row-item" src="../../components/book-list/common-novel-row-item/index.ux"></import>
<import name="coverflow-swiper" src="../components/coverflow-swiper/index.ux"></import>
<import name="novel-list-row-four" src="../../components/book-list/novel-list-row-four/index.ux"></import>
<import name="novel-list-row-one" src="../../components/book-list/novel-list-row-one/index.ux"></import>
<import name="novel-list-one-four" src="../../components/book-list/novel-list-one-four/index.ux"></import>
<import name="my-toast" src="../../components/my-toast"></import>

<script>
export default {
  data: {
    statusBarHeight:40,
    loading: false,
    subLength: 3,
    headerTabData: {
      index: 0,
      list: [
        {
          title: '推荐',
          type: 'recommend',
          imgPath: ''
        },
        {
          title: '男频',
          type: 'boy',
          imgPath: ''
        },
        {
          title: '女频',
          type: 'girl',
          imgPath: ''
        }
      ]
    },
    bannerData: {
      config: {
        autoplay: true,
        loop: true,
        enableswipe: true,
        indicator: false
      },
      list: []
    },
    novelList: [],
    bottomData: {
      page: 1,
      totalPage: 1,
      list: [],
      count: 0
    },
    advConfig: null, //广告数据
    isSwiperReload: true, //swiper是否重新渲染
    maleData: {
      bannerData: {
        config: {
          autoplay: true,
          loop: true,
          enableswipe: true,
          indicator: false
        },
        list: []
      },
      novelList: [],
      bottomData: {
        page: 1,
        totalPage: 1,
        list: [],
        count: 0
      },
      isRefresh: true
    },
    femaleData: {
      bannerData: {
        config: {
          autoplay: true,
          loop: true,
          enableswipe: true,
          indicator: false
        },
        list: []
      },
      novelList: [],
      bottomData: {
        page: 1,
        totalPage: 1,
        list: [],
        count: 0
      },
      isRefresh: true
    },
    appearCount: 0,
    recommendList: [
    ],
    maleList: [
    ],
    femaleList: [
    ],
    vipList: [
    ],
    scrollDirection: '',
    searchOpacity: 1,
    searchHeight: 0,
    listScrollY: 0,
    vipBgColor: 'rgba(28, 23, 43,0)',
    listRealScrollY: 0,
    hasSetInteresting: false,
    indexArr: [1, 2, 3, 4],
    searchWidth: 0,
    searchLeft: 0,
    searchTop: 0,
    tab1HeaderHeight: 0,
    tab2HeaderHeight: 0,
    scrollDataArr: [
      {searchWidth: 0, searchLeft: 0, searchTop: 0, searchOpacity: 1, searchHeight: 0,listRealScrollY: 0},
      {searchWidth: 0, searchLeft: 0, searchTop: 0, searchOpacity: 1, searchHeight: 0,listRealScrollY: 0},
      {searchWidth: 0, searchLeft: 0, searchTop: 0, searchOpacity: 1, searchHeight: 0,listRealScrollY: 0},
      {searchWidth: 0, searchLeft: 0, searchTop: 0, searchOpacity: 1, searchHeight: 0,listRealScrollY: 0},
    ],
    payStatus: 0, // vip充值状态改变
    customToastText: '', // 自定义toast文案
    customToastShow: false,
    payErrorEventName: '',
    vipMoudleShowTimes: 0,
  },
   computed: {
    bottomHeight() {
      let tempHeight = 0
      if(this.isShowAddDesktop){
        tempHeight += (this.addDesktopHeight + 40)
      }
      if(this.isKeepRead){
        tempHeight += 196
      }
      return tempHeight
    }
  },
   computed: {
    bottomHeight() {
      let tempHeight = 0
      if(this.isShowAddDesktop){
        tempHeight += (this.addDesktopHeight + 40)
      }
      if(this.isKeepRead){
        tempHeight += 196
      }
      return tempHeight
    }
  },
  formatScore(value) {
    if (!value) return ''
    return value / 10
  },
  /**
   * 换一换点击
   */
  async changeListData() {
    LOG('changeList2', this.headerTabData.index)

    let res = await $apis.example.bookinfo({
      type: Number(this.headerTabData.index) + 1,
      version: 2,
      rand: 1
    })
    if (res.code == 200) {
      try {
        switch (Number(this.headerTabData.index) + 1) {
          case 2:
            this.maleList[3].data = res.data.data[3].data
            this.$app.$def.uploadListShow(this.maleList[3].data, '男频' + this.maleList[3].title)
            break;
          case 3:
            this.femaleList[3].data = res.data.data[3].data
            this.$app.$def.uploadListShow(this.femaleList[3].data, '女频' + this.femaleList[3].title)
            break;
          case 4:
            this.vipList[1].data = res.data.data[1].data
            this.$app.$def.uploadListShow(this.vipList[1].data, '会员' + this.vipList[1].title)
            break;
          default:
            break;
        }
      } catch (error) {}
    }
    COMMON_REPORT_UTILS.page_click_report('换一换')
  },
  /**
   * 性别tab点击
   * @param {Number} idx 下表
   */
  sexTabClick(idx) {
    if (this.headerTabData.index == idx) return
    this.headerTabData.index = idx
    this.$app.$def.tabListType[3] = 0
    this.checkIsRefreshData()

    let type = ''
    switch (Number(this.headerTabData.index) + 1) {
      case 1:
        type = '推荐'
        this.$page.setStatusBar({textStyle: 'dark'})
        break;
      case 2:
        type = '男频'
        this.$page.setStatusBar({textStyle: 'dark'})
        break;
      case 3:
        type = '女频'
        this.$page.setStatusBar({textStyle: 'dark'})
        break;
      case 4:
        type = '会员'
        this.$page.setStatusBar({textStyle: 'light'})
        break;
      default:
        break;
    }
    COMMON_REPORT_UTILS.page_click_report(type)
  },
  checkIsRefreshData() {
    switch (Number(this.headerTabData.index) + 1) {
      case 1:
        if (this.recommendList.length == 0) this.getViewData(1)
        break;
      case 2:
        if (this.maleList.length == 0) this.getViewData(2)
        break;
      case 3:
        if (this.femaleList.length == 0) this.getViewData(3)
        break;
      case 4:
        if (this.vipList.length == 0) this.getViewData(4)
        break;
      default:
        break;
    }
  },
  handleTabChange({index}) {
    this.sexTabClick(index)
  },
  /**
   * 搜索按钮点击
   */
  searchTabClick() {
    console.log('跳转到搜索页面')
    COMMON_REPORT_UTILS.page_click_report('搜索') 
    this.$emit('pageJumpHandle',{pageUrl:'/pagesB/Search',pageData:{pathUrl: "书城页",headerTabIndex:this.headerTabData.index}})
  },
  /**
   * 组件内书籍item点击事件监听
   */
  compClickHandler(sectionName, evt) {
    console.log('evt.detail', evt.detail,sectionName)
    this.bookItemClick(evt.detail,sectionName)
  },
  toRecommandPage(evt) {
    this.$emit('pageJumpHandle',{pageUrl:'/pagesA/recommand-book',pageData:{pathUrl: "书城页",bookIds: evt.detail.bookIds}})
  },
  /**
   * 书籍item点击通用事件
   */
  bookItemClick(bookInfo,sectionName) {
    COMMON_REPORT_UTILS.list_click_report('1',[`${bookInfo.bookId}`],`${sectionName}`) //点击上报
    let params = {bookId: bookInfo.bookId,pathUrl: "书城页"}
    this.$emit('pageJumpHandle',{pageUrl:'/pagesC/Info',pageData:params})
  },
  /**
   * 获取页面数据
   */
  getViewData(num, loadingShow = true) {
    if (loadingShow) this.loading = true
    $apis.example.bookinfo({
      type: num,
      version: 2
    }).then(res => {
      this.loading = false
      if (res.code == 200) {
        switch (num) {
          case 1:
            this.recommendList = res.data.data
            break;
          case 2:
            this.maleList = res.data.data
            break;
          case 3:
            this.femaleList = res.data.data
            break;
          case 4:
            this.vipList = res.data.data
            break;
          default:
            break;
        }
      }
    }).catch(err => {
      this.loading = false
    })
  },
  getAllViewData(num) {
    $apis.example.bookinfo({
      type: num,
      version: 2
    }).then(res => {
      if (res.code == 200) {
        let type = num.split(',')
        type.forEach(element => {
          switch (element) {
            case '1':
              this.recommendList = res.data.type1.data
              break;
            case '2':
              this.maleList = res.data.type2.data
              break;
            case '3':
              this.femaleList = res.data.type3.data
              break;
            case '4':
              this.vipList = res.data.type4.data
              break;
            default:
              break;
          }
        });
      }
    }).catch(err => {
    })
  },
  onInit() {
    this.statusBarHeight = sdk.env.device.statusBarHeight

    this.headerTabData.index = 0
    if(this.$app.$def.isSetSex){
      this.headerTabData.index = this.$app.$def.sex == 1 ? 1 : 2
    }
    this.$watch('selectIndex', 'selectIndexChangeHandler')
    this.$watch('viewShowTimes', 'viewShowTimesChangeHandler')
    if (this.$parent().mySelectIndex == 1) {
      this.selectIndexChangeHandler(1, 100)
    }
    this.checkShowInterestDialog()
  },
  async selectIndexChangeHandler(newVal, oldVal) {
    if (newVal == 1 && this.$app.$def.tabListType[newVal] == 0) {
      await this.getViewData(Number(this.headerTabData.index) + 1)
      this.getAllViewData(this.indexArr.filter(item => item != Number(this.headerTabData.index)+1).join(','))
      this.$emit('plusViewShowTimes')
      this.$app.$def.tabListType[newVal] = 1
    }
  },
  viewShowTimesChangeHandler(newVal, oldVal) {
    if (newVal == 1) return
    this.payStatus ++; 
    if (this.selectIndex !== 1) return
    this.vipMoudleShowTimes ++;
    this.selectIndexChangeHandler(1, 100)
  },
  async checkShowInterestDialog() {
    let res = await $apis.example.getHasInterest()
    if (res.code == 200) {
      this.hasSetInteresting = res.data.have_interest == 1 ? false : true
    }
  },
  contentSwipeHandle(evt) {
    // if (evt.scrollY == 0) return
    // if (this.listRealScrollY >= 0) {
    this.scrollDataArr[Number(this.headerTabData.index)].listRealScrollY += evt.scrollY
    // }
    // this.listRealScrollY = Math.max(this.listRealScrollY, 0)
    /* if (evt.scrollY > 0) {
      if (this.listScrollY <= 68) {
        this.listScrollY += evt.scrollY
        this.listScrollY = Math.min(this.listScrollY, 68)
      }
    }
    if (evt.scrollY < 0) {
      if (this.listScrollY > 0) {
        this.listScrollY += evt.scrollY
        this.listScrollY = Math.max(this.listScrollY, 0)
      }
    } */
    // LOG('listRealScrollY', this.listRealScrollY, evt.scrollState)
    // let scrollY = Math.min(this.scrollDataArr[Number(this.headerTabData.index)].listRealScrollY, 68)
    this.scrollDataArr[Number(this.headerTabData.index)].searchHeight = Math.min(this.scrollDataArr[Number(this.headerTabData.index)].listRealScrollY, 68)
    this.scrollDataArr[Number(this.headerTabData.index)].searchOpacity = 1 - (Math.max(Math.min(this.scrollDataArr[Number(this.headerTabData.index)].listRealScrollY, 68), 0) / 68)
    this.scrollDataArr[Number(this.headerTabData.index)].searchWidth = (Math.min(this.scrollDataArr[Number(this.headerTabData.index)].listRealScrollY, 68) / 68) * 380
    this.scrollDataArr[Number(this.headerTabData.index)].searchTop = (Math.min(this.scrollDataArr[Number(this.headerTabData.index)].listRealScrollY, 68) / 68) * 88
    this.scrollDataArr[Number(this.headerTabData.index)].searchLeft = (Math.min(this.scrollDataArr[Number(this.headerTabData.index)].listRealScrollY, 68) / 68) * 88
    this.vipBgColor = `rgba(28, 23, 43, ${Math.min(this.scrollDataArr[Number(this.headerTabData.index)].listRealScrollY / 200, 1)})`
  },
  showInteresting() {
    this.$emit('showInteresting')
  },
  itemShow(title,data){
    LOG('itemShow===========>',title,data)
    if (title.includes('男频') && this.headerTabData.index != 1) return
    if (title.includes('女频') && this.headerTabData.index != 2) return
    this.$app.$def.uploadListShow(data, title)
  },
  // 展示自定义toast
  showMyToast(content, duration = 2500) {
    if (this.customToastShow) return
    this.customToastText = content
    this.customToastShow = true
    let timer = setTimeout(() => {
      this.customToastShow = false
      this.customToastText = ''
      clearTimeout(timer)
    }, duration);
  },
  //支付失败组件事件
  payErrorDialofClickHandler(evt) {
    this.payErrorEventName = evt.detail.eventName;
  },
  props: {
    selectIndex: {
      type: Number,
      default: 1
    },
    viewShowTimes: {
      type: Number,
      default: 0
    },
    backPopShow: {
      type: Number,
      default: 0
    },
    isShowAddDesktop: {
      default: false
    },
    addDesktopHeight: {
      default: 80
    },
    isKeepRead:{
      default: false
    }
  }
}
</script>

<style lang="less" scoped>
@import './index.less';
</style>
