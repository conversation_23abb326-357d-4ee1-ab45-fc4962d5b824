<template>
  <div class="is-wrapper">
    <div class="is-main">
      <text class="is-title">请选择你的阅读偏好</text>
      <div style="margin-top: 66px;">
        <div @click="shiftSex(1)"
          style="width: 134px;height: 134px;background-color: #ffffff;border-width: 2px;border-style: solid;border-color: {{sexIndex == 1?'#F11212':'#666666'}};
            border-radius: 50%;justify-content:center;align-items:center;
          "
        >
          <image src="https://img.qdreads.com/v166/phsz_n.png" style="width: 84px;height: 103px;"></image>
        </div>
        <div @click="shiftSex(2)"
          style="width: 134px;height: 134px;background-color: #ffffff;border-width: 2px;border-style: solid;border-color: {{sexIndex == 2?'#F11212':'#666666'}};
            border-radius: 50%;justify-content:center;align-items:center;margin-left:82px;
          "
        >
          <image src="https://img.qdreads.com/v166/phsz_v.png" style="width: 84px;height: 103px;"></image>
        </div>
      </div>
      <div class="is-item-wrapper">
        <block for="(index, value) in settingData">
          <text 
            style="margin-left: {{index % 3 == 0 ? 0 : 30}}px" 
            class="is-item {{value.is_show == 1 ? 'is-item-selected' : ''}}"
            @click="itemSelect(value)"
          >{{value.title}}</text>
        </block>
      </div>
      <text 
        style="" 
        class="is-btn {{btnEnable ? 'is-btn-enable' : ''}}"
        @click="submitSetting"
      >完成设置，开启阅读</text>
      <image @click="closeDialog" src="https://img.qdreads.com/v166/xq-gb-icon.png" class="is-close-btn"></image>
    </div>
  </div>  
</template>

<script>
export default {
  data: {
    sexIndex: 1,
    settingData: []
  },
  
  props: [],

  computed: {
    btnEnable() {
      return this.settingData.find((element) => element.is_show === 1)
    }
  },
  
  onInit() {
    this.sexIndex = this.$app.$def.sex
    this.getFirstCategoryList()
  },

  shiftSex(index) {
    this.sexIndex = index
    this.getFirstCategoryList()
  },

  itemSelect(item) {
    item.is_show = item.is_show * -1
  },

  /**
   * 获取一级分类列表
   */
  getFirstCategoryList() {
    let that = this
    return new Promise((resolve, reject) => {
      $apis.example.getInterestSettings({
        sex: that.sexIndex
      }).then(res => {
        if (res.code == 200) {
          that.settingData = res.data
          resolve()
        } else {
          resolve()
        }
      }).catch(err => {
        resolve()
      })
    })

  },
  async submitSetting() {
    if (!this.btnEnable) return
    let interest = []
    this.settingData.forEach((element) => {
      if (element.is_show === 1) interest.push(element.id)
    })
    let res = await $apis.example.setInterestSettings({interest: interest.join(',')})
    if (res.code == 200) {
      this.$app.$def.sex = this.sexIndex
      this.$emit('closeDialog', { hasSet: true })
      $utils.showToast("设置成功")
    } else {
      $utils.showToast("设置失败，请稍后再试")
    }
  },
  closeDialog() {
    this.$emit('closeDialog')
  },
}
</script>

<style>
.is-wrapper {
  position: fixed;
  top: 0px;
  right: 0px;
  left: 0px;
  bottom: 0px;
  flex: 1;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(0,0,0,0.60);
}

.is-main {
  width: 590px;
  background-color: #ffffff;
  border-radius: 24px;
  padding: 0 30px;
  flex-direction: column;
  align-items: center;
}

.is-bg {
  width: 100%;
  height: 142px;
}

.is-title {
  height: 44px;
  font-size: 44px;
  font-weight: bold;
  color: #333333;
  margin-top: 50px;
}

.is-male-wrapper, .is-female-wrapper {
  width: 274px;
  height: 100px;
  background-color: #f8f8f8;
  align-items: center;
}

.is-male-wrapper > image, .is-female-wrapper > image {
  height: 100px;
  width: 100px;
}

.is-male-wrapper > text, .is-female-wrapper > text {
  height: 32px;
  font-size: 32px;
  font-weight: 500;
  color: #999999;
}

.is-male-wrapper {
  border-top-left-radius: 200px;
  border-bottom-left-radius: 200px;
}

.is-male-wrapper > image {
  margin-left: 54px;
  margin-right: 26px;
}

.is-female-wrapper {
  border-top-right-radius: 200px;
  border-bottom-right-radius: 200px;
}

.is-female-wrapper > text {
  margin-left: 40px;
  margin-right: 26px;
}

.is-item-wrapper {
  flex-wrap: wrap;
  width: 100%;
  margin-top: 20px;
}

.is-item {
  width: 156px;
  height: 56px;
  background-color: #f6f6f6;
  border-radius: 30px;
  margin-left: 30px;
  margin-top: 30px;
  text-align: center;
  color: #999;
  font-size: 26px;
}

.is-btn {
  width: 100%;
  height: 96px;
  background-color: #f6f6f6;
  border-radius: 50px;
  margin-top: 60px;
  text-align: center;
  color: #999999;
  margin-bottom: 30px;
}

.is-item-selected {
  background-color: #feeaea;
  color: #F11212;
}

.is-btn-enable {
  background-color: #f11212;
  color: #FFFFFF;
}

.is-close-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 26px;
  height: 26px;
}
</style>
