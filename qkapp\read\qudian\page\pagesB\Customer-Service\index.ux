<import name="tel" src="../../components/tel/index.ux"></import>
<template>
  <div class="complaint">
    <div class="back_img"></div>
    <div class="nav-wrapper">
      <div class="nav-back" style="margin-top:{{marginTop}}px;" onclick="toBack">
      <image src="https://qkimg.tianyaoguan.cn/img/b.png" alt="" ></image>
      <text>投诉与反馈</text>
    </div>
     </div>


    <div class="wrapper">
      <div class="text-content">
          <text class="greeting">您好～</text>
          <text class="message">欢迎进入快应用问题反馈中心</text>
      </div>
      <image class="image" src="https://img.sytangshiwl.top/public/kefu.png" alt="客服形象"></image>
    </div>

    <!-- 电话组件 -->
    <tel></tel>


    <div class="content">
      <div class="title">
        <text >请选择举报原因</text>
      </div>
      <div class="reasons">
              <div class="reason-item" for="(index,item) in reportReasons" :key="index" onclick="reportSelect(index)"><text>{{item}}</text></div>
      </div>
      <div class="textarea">
        <textarea
          id="com-textarea"
          maxlength="200"
          placeholder="请填写问题描述以便我们提供更好的帮助"
          @change="textChange"
          >{{ content }}</textarea
        >
        <text class="pa font_14">{{ content.length }}/200</text>
      </div>
    </div>
    <div class="info">
      <input
        id="com-input"
        type="text"
        maxlength="30"
        placeholder="可填写手机号，邮箱便于我们与您联系"
        value="{{ contact }}"
        @change="onChange"
      />
      <div class="xuantian"><text>选填</text></div>
    </div>
    <div class="submitBtn">
      <text
        class="bold tc white  {{ content.length>0 ? 'red' : '' }}"
        @click="onSubmit"
        >提交举报</text
      >
    </div>
    <!-- 上传中 -->
    <div class="loading" if="{{ loadingShow }}">
      <div>
        <image
          class="loadingImg"
          src="https://qkimg.tianyaoguan.cn/img/loading.png"
        ></image>
        <text>上传中...</text>
      </div>
    </div>

    <!-- 温馨提示 -->
    <div class="popup" if="{{ popupShow }}">
      <stack style="justify-content: center;">
        <div class="popupContent">
          <text class="bold tc title">提交成功</text>
          <text class="tc tips">可以选择返回或者退出哦~</text>
          <div class="btn">
            <text class="font_14 tc btn1" @click="toBack">返回页面</text>
            <text class="font_14 tc btn2" @click="toQuit">退出应用</text>
          </div>
        </div>
        <div class="subPic">
          <image
            src="https://bargaqkappcluster.oss-cn-beijing.aliyuncs.com/image%2F2022-12-15%2F1671095577592_sumit.png"
          ></image>
        </div>
        <div class="closeBtn" @click="closePop">
          <image
            src="https://bargaqkappcluster.oss-cn-beijing.aliyuncs.com/image%2F2023-01-10%2F1673351474818_e-cancel_clear.png"
          ></image>
        </div>
      </stack>
    </div>
  </div>
</template>

<script>
import media from '@system.media';
import fetch from '@system.fetch'
import app from '@system.app';
import secretKeyConfig from '../../json/tacticsSecret.json'
import device from '@system.device'

export default {
  data: {
    content: '',
    contact: '',
    images: [],
    marginTop:80,
    loadingShow: false, // 上传图片loading
    popupShow: false,
    reportReasons:['广告太多','功能无法正常使用','其它问题'],
  },
 async onInit() {
    let deviceInfo = await device.getInfo({})
    this.marginTop = deviceInfo.data.statusBarHeight
    console.log('deviceInfo',deviceInfo.data)
  },
  // onShow() {},
  // 富文本内容变化
  textChange(e) {
    if (e.text.length > 200) {
      this.content = e.text.substring(0, 200);
    } else {
      this.content = e.text;
    }
  },
  reportSelect(index){
    if(this.content.includes(this.reportReasons[index]))return
    this.content =this.content+ (this.content?'，':'') + this.reportReasons[index]
  },

  // 选择图片
  uploadImage() {
    let that = this;
    media.pickImage({
      success: function (data) {
        console.log('pickImage =>>>>>>>>>>', data)
        let files = [{
          uri: data.uri,
          name: "file"
        }];
        that.uploadImageToOSS(files);
      },
      fail: function (data, code) {
        console.log(`handling fail, code = ${code}`)
      },
      cancel: function () { }
    })
  },
  // 上传图片
  async uploadImageToOSS(files) {
    console.log("files =>>>>>>>>", files);
    let that = this;
    that.loadingShow = true;
    await putOSS(files, 'images/',
      async (result) => {
        console.log("putOSS=>>>>>>>>>>>", result);
        that.images.push(result);
        that.loadingShow = false;
      }, (errMsg, errCode) => {
        if (errCode == '201') {
          $utils.showToast('图片上传失败', 1);
        } else {
          $utils.showToast('图片太大了', 1);
        }
      }
    )
  },
  // 删除图片
  delImg(index) {
    this.images.splice(index, 1);
  },
  onChange(e) {
    this.contact = e.text;
  },
  //返回
  toBack() {
    this.popupShow = false
    $utils.goBack()
  },
  //退出应用
  toQuit() {
    this.popupShow = false
    this.$app.exit()
  },
  onSubmit() {
    if (!this.content) {
      $utils.showToast('请您填写投诉建议', 1);
      return;
    }
    let that = this;
    this.$element('com-input').focus({ focus: false })
    this.$element('com-textarea').focus({ focus: false })
    if(!/^1\d{10}$/.test(this.contact)&&this.contact) {
        $utils.showToast('请输入正确的手机号', 1);
        return;
    }
    COMMON_REPORT_UTILS.page_click_report(`提交按钮点击`)
    let base_url = process.env.NODE_ENV == 'development' ? 'https://test-tactics-api.ghfkj.cn/user/complaint' : 'https://tactics-api.ghfkj.cn/user/complaint'
    let source_pkg = this.$app.$def.sourcePkg
    if (source_pkg == 'unknown' || source_pkg == undefined || source_pkg == null || source_pkg == '') {
      source_pkg = 'com.dairyshopping.home'
    }
    let param = {
      content: this.content,
      phone: this.contact,
      deviceId: sdk.env.identity.androidId,
      sourcePkg: source_pkg,
      brand: sdk.env.device.brand,
      deviceModel: encodeURIComponent(sdk.env.device.model),
      secretKey: process.env.NODE_ENV == 'development' ? secretKeyConfig.dev : secretKeyConfig.pro,
    }
    console.log('params_______________-------', param)
    fetch.fetch({
      url: base_url,
      responseType: 'text',
      data: param,
      method: 'post',
      header: {
					"Content-Type": 'application/json'
				},
      success: function (res) {
        
        console.log('resss', res)
        $utils.showToast('感谢您的反馈~', 1);
        setTimeout(()=>{
          that.$app.exit()
        },3000)
        // $utils.goBack()
        // that.content=''
        // that.contact=''
        // if (res.code>=400) {
        //      $utils.showToast("投诉异常")
        // }else{
        //    let resData = JSON.parse(res.data)
        //    if(resData.code==200){//添加成功
        //       this.popupShow = true;
        //    }
        // }
      },
      fail: function (err, code) {
        console.log('err', err)
        $utils.showToast('提交成功', 1);
        $utils.goBack()
      }
    })
  },
  // 关闭弹窗
  closePop() {
    this.popupShow = false;
    // $utils.routeReplacetheUrl('/pages/Main', { activeTab: 1 }, false)
    $utils.goBack()
  }
}
</script>


<style lang="less">
@import './index.less';
</style>


