{"package": "com.qudianread.home", "name": "趣点阅读", "versionName": "2.3.7", "versionCode": 203007, "minPlatformVersion": 1100, "icon": "/assets/images/logo.png", "huaweiSplashAdSlotId": "x6gihwc2vu", "features": [{"name": "system.request"}, {"name": "system.requesttask"}, {"name": "system.prompt"}, {"name": "system.router"}, {"name": "system.shortcut"}, {"name": "system.fetch"}, {"name": "system.network"}, {"name": "service.account"}, {"name": "system.storage"}, {"name": "system.device"}, {"name": "system.brightness"}, {"name": "system.app"}, {"name": "service.push"}, {"name": "system.notification"}, {"name": "system.wifi"}, {"name": "system.file"}, {"name": "service.ad"}, {"name": "system.calendar"}, {"name": "system.package"}, {"name": "system.webview"}, {"name": "system.audio"}, {"name": "system.sensor"}, {"name": "system.vibrator"}, {"name": "system.battery"}, {"name": "system.clipboard"}, {"name": "system.keyguard"}, {"name": "service.share", "params": {"appSign": "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", "wxKey": "wxfc688bf7333c0112"}}], "config": {"logLevel": "debug", "network": {"readTimeout": 8000, "connectTimeout": 8000}}, "router": {"entry": "pagesC/Start", "pages": {"pagesA/Main": {"component": "index"}, "pagesA/WelfareNew": {"component": "index"}, "pagesB/Activity": {"component": "index"}, "pagesB/Login": {"component": "index"}, "pagesB/Sex": {"component": "index"}, "pagesB/Setting": {"component": "index"}, "pagesB/Privacy": {"component": "index"}, "pagesB/Subscribe": {"component": "index"}, "pagesB/Customer-Service": {"component": "index"}, "pagesB/Search": {"component": "index"}, "pagesB/Top": {"component": "index"}, "pagesC/Start": {"component": "index"}, "pagesC/Info": {"component": "index"}, "pagesC/Read": {"component": "index", "launchMode": "singleTask"}, "UnionAd/AdPage": {"component": "index"}, "UnionAd/AdLanding": {"component": "index"}, "UnionAd/AdReward": {"component": "index"}, "pagesC/Empty": {"component": "index"}, "pagesB/Privacy-Setting": {"component": "index"}, "pagesB/Recommend": {"component": "index"}, "pagesB/Recommend-Read": {"component": "index"}, "pagesB/Page-Error": {"component": "index"}, "pagesC/Action": {"component": "index", "launchMode": "singleTask"}, "pagesA/Read-History": {"component": "index"}, "pagesA/recommand-book": {"component": "index"}, "pagesD/WakeEmpty": {"component": "index"}}, "widgets": {}}, "subpackages": [{"name": "pkgB", "resource": "pagesB"}, {"name": "pkgA", "resource": "pagesA"}], "display": {"titleBar": false, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0, "statusBarTextStyle": "auto", "homePage": "pagesA/Main", "themeMode": 0, "pages": {"pagesB/Customer-Service": {"statusBarTextStyle": "auto"}, "pagesB/Activity": {"statusBarTextStyle": "auto"}, "pagesC/Read": {"menuBarData": {"menuBar": false}, "fullScreen": true, "fitCutout": "portrait|landscape"}, "pagesC/Action": {"fullScreen": true, "fitCutout": "portrait|landscape", "menuBarData": {"menuBar": false}, "statusBarTextStyle": "auto"}, "pagesA/WelfareNew": {"fullScreen": true, "fitCutout": "portrait|landscape", "menuBarData": {"menuBar": false}, "statusBarTextStyle": "auto"}, "UnionAd/AdPage": {"fullScreen": true, "titleBar": false, "statusBarImmersive": true, "menuBarData": {"menuBar": false}}, "UnionAd/AdReward": {"fullScreen": true, "titleBar": false, "statusBarImmersive": true, "menuBarData": {"menuBar": false}}, "UnionAd/AdLanding": {"fullScreen": true, "titleBar": false, "statusBarImmersive": true, "menuBarData": {"menuBar": false}}, "pagesC/Empty": {"fullScreen": true, "fitCutout": "portrait|landscape"}, "pagesA/recommand-book": {"statusBarTextStyle": "auto"}, "pagesD/WakeEmpty": {"titleBarText": ""}}}}