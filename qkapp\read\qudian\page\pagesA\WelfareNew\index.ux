<template>
  <div class="welfare-wrapper">
    <div
      class="header-bg-image"
    ></div>
    <stack style="width: 100%; height: 100%">
      <div class="native-boost-group boots-hide" if="{{isShowBoostComp}}">
        <custom-pop-group
        if="{{isShowBoostComp}}"
        onevent-watch="nativeBoostGroupEventDispatchHandler"
      ></custom-pop-group>
      </div>
      <div class="header-content">
        <image
          class="header-button"
          src="https://img.qdreads.com/v155/welfare/welfare-header-button-neww.png"
        ></image>
        <div class="activity-1" if="{{isShowTurntable}}">
          <welfare-activity activity-type="turntable"></welfare-activity>
        </div>
        <div class="activity-2" if="{{isShowShake}}">
          <welfare-activity activity-type="shake"></welfare-activity>
        </div>
      </div>
      <list
        class="page-stack-2"
        id="task-content-list"
        onscrollend="listScrollDown"
        onscroll="listScroll"
      >
        <list-item type="empty">
          <div
            class="top-stack-new"
            onclick="topHeaderClickHandler(1)"
            onappear="listTopHeadeApper"
            ondisappear="listTopHeaderDisApper"
          >
            <div if="{{isShowTurntable}}" class="activity-1" onclick="activityClickHandler('turntable')"></div>
            <div if="{{isShowShake}}" class="activity-2" onclick="activityClickHandler('shake')"></div>
          </div>
        </list-item>
        <list-item type="tab" style="width: 100%; flex-direction: column">
          <div style="width: 100%; flex-direction: column; align-items: center">
            <div class="task-section-content"></div>
            <div class="task-section-header">
              <image
                class="img"
                src="https://img.qdreads.com/v155/welfare/welfare-task-section-new.png"
              ></image>
              <text class="title">做任务 赚金币</text>
              <image
                class="arrow"
                src="https://img.qdreads.com/v155/welfare/welfare-top-arrow.png"
                onclick="topHeaderClickHandler(2)"
              ></image>
            </div>
            <welfare-sign
              if="{{welfareTasKInfo}}"
              sign-task-list="{{signTaskList}}"
              sign-today="{{welfareTasKInfo.signInTask.isSignToday}}"
              onsign-click="signCheckClickHandler"
            ></welfare-sign>
            <!-- <div style="margin-top: 36px" if="{{welfareRedBadInfo}}">
              <unpack-red-packets
                packets="{{welfareRedBadInfo}}"
                packets-today="{{userBalanceInfo.today_envelope}}"
                onevent-watch="packetsClickHandler"
              ></unpack-red-packets>
            </div> -->
            <!-- 新手任务 -->
            <div
              if="{{newUserTaskList.length > 0}}"
              class="task-wrapper"
            >
              <div class="task-title-1">
                <image src="https://img.qdreads.com/v155/welfare1.5.0/rookie-title.png"></image>
                <text>持续阅读得海量金币</text>
              </div>
              <block for="(index,item) in newUserTaskList">
                <task-item
                  data="{{item}}"
                  @btn-click="dailyTaskClickHandle"
                ></task-item>
              </block>
            </div>

            <!-- 日常任务 -->
            <div
              if="{{welfareTasKInfo}}"
              class="task-wrapper"
              style="margin-bottom: 36px"
            >
              <div class="task-title-1">
                <image src="https://img.qdreads.com/v155/welfare1.5.0/daily-title.png"></image>
                <text>每日0点重置</text>
              </div>
              <block for="(index,item) in dailyTaskList">
                <task-item
                  data="{{item}}"
                  @btn-click="dailyTaskClickHandle"
                ></task-item>
              </block>
            </div>
          </div>
        </list-item>
        <list-item type="empty-2" style="width:100%;height:{{allTaskLength}}px;">
          <div></div>
        </list-item>
      </list>
      <div class="page-stack-3" show="{{scrollTop}}">
        <image
          class="back-image"
          src="https://img.qdreads.com/image%2F2022-11-08%2Fwelfare-header-new-top.png"
        ></image>
        <div class="task-section-header">
          <div class="task-section-content"></div>
          <image
            class="img"
            src="https://img.qdreads.com/v155/welfare/welfare-task-section-new.png"
          ></image>
          <text class="title">做任务 赚金币</text>
          <image
            class="arrow"
            src="https://img.qdreads.com/v155/welfare/welfare-down-arrow.png"
            onclick="taskSectionDownHandler"
          ></image>
        </div>
      </div>
      <div class="page-stack-4">
        <div
          style="align-items: center;margin-top:{{statusBarHeight + 48}}px;"
          @click="backBtnClick"
        >
          <image
            src="https://img.qdreads.com/v155/welfare1.5.0/title-icon.png"
            class="back-icon-img"
          ></image>
          <text class="page-title">福利中心</text>
        </div>
        <!-- <div class="coin-detail" @click="jumpToCoinDetailView">
          <image
            class="coin-img"
            src="https://img.qdreads.com/v155/welfare/welfare-top-coin.png"
          ></image>
          <text class="coin-text"
            >{{ userBalanceInfo.today_gold }}(约{{
              userBalanceInfo.today_gold
                ? (userBalanceInfo.today_gold / 10000).toFixed(2)
                : 0
            }}元)</text
          >
          <div class="coin-button">
            <text>兑换金币</text
            ><image src="https://img.qdreads.com/v155/welfare/welfare-change-coin.png"></image>
          </div>
        </div> -->
      </div>
    </stack>

    <!-- 福利页宝箱按钮悬浮 -->
    <div
      if="boxFixedShow && !videoBqtConfig.isShow && !nativeBoostGroupController.popShow && boxTimeCount >=0 && $app.$def.auditOnline !== 1 "
      @click="boxFixedClick"
      class="box-fixed-container {{boxTimeCount == 0 ? 'shake-lr' : ''}}"
    >
      <stack>
        <image
          class="box-fixed-image"
          src="https://img.qdreads.com/readPoint/rp-dialog-icon-11.png"
        ></image>
        <image
          class="box-fixed-button-icon"
          src="https://img.qdreads.com/readPoint/rp-dialog-icon-31.png"
        ></image>
        <text class="box-fixed-text" if="{{boxTimeCount == 0}}">立即领取</text>
        <text class="box-fixed-text" elif="{{boxTimeCount > 0}}">{{
          boxFiexdText
        }}</text>
      </stack>
    </div>
    <!-- 兴趣爱好弹窗 -->
    <interest-settings
      if="{{isShowDialog}}"
      @close-dialog="closeDialog"
    ></interest-settings>
        <!-- 退出应用按钮 -->
    <exit-icon if="{{isShowExIcon}}"></exit-icon>
    <block if="{{customToastShow}}">
      <my-toast toast-text="{{customToastText}}"></my-toast>
    </block>
  </div>
</template>

<import name="common-header" src="../../components/common-back-header/index"></import>
<import name="common-back-header" src="../../components/common-back-header/index.ux"></import>
<import name="welfare-sign" src="../components/walfare-sign/index.ux"></import>
<import name="unpack-red-packets" src="../components/unpack-red-packets"></import>
<import name="task-item" src="../components/task-item"></import>
<import name="interest-settings" src="../../components/interest-settings"></import>
<import name="welfare-activity" src="../../components/welfare-activity/index.ux"></import>
<import name="my-toast" src="../../components/my-toast"></import>
<import name="exit-icon" src="../../components/exit-icon/index.ux"></import><!-- 投诉按钮 -->

<import name="custom-pop-group" src="../../cy-sdk/action/components/custom-pop-group/index.ux"></import>

<script>
import shortcut from '@system.shortcut';
import calendar from '@system.calendar';

export default pageMixin({
  private: {
    scrollTop: false,
    isShowBoostComp: true, //是否存在自定义补量池位置
    isCheckBackHandler:false,
    isShowPreloadImg:false, //是否开启图片预加载组件
    showCustomFill:false,
    boostTimer:null,
    nativeBoostGroupController: {
      backPressTimes: 0,
      popCode: '',
      loopTimes: 5000,
      middleCode: '',//中插广告弹窗code
      getBoost: 0,
      isAuditUser: false, //是否为审核用户
      isBoostType: false, //是否开启补量
      actionCode: 'PAGE_ENTRY', //触发行为 包含自定义行为 页面进入 物理返回 弹窗关闭
      isFullScreen: true,
      maxAdCount: 3,//补量池最大获取成功广告数
      stopTimeout: 0,//停止补量池轮询 onhide时
      startTimeout: 0,//启动补量池轮询 onshow时
      customAction: {},
      popShow: false,
      middleBackPressTimes: 0,
      maxShowTimes: 10000,
      boostRequestType: 2,//广告池请求方式  1 间隔请求 2 销毁请求
      isClearBoost:0,//是否清空补量池 累加变值监听
      viewAdvsMaxCount:2,
      layerRequestEnterPoolIntervalTimes: '5000',
      isEnableLayer:0,//是否开启分层
      popConfigs:null,
      popNewConfigs:null,
      middlePopNewConfigs:null,
      sceneCode:'',
      lowerShowTimes:500,//最小曝光时间 ms 
      maxCountByScene:3, //每个场景每个广告主广告上限
      bqtDefaultStyle:{},//百度广告默认按钮样式
    },
    screenInserConfig: {
      popCode: '',
      actionCode: '',
      isShow: false,
      backPressTimes: 0,
      customAction: {}
    },
    videoBqtConfig: {
      popCode: '',
      actionCode: '',
      isShow: false,
      backPressTimes: 0,
      customAction: {}
    },
    isAllowBackPress: false,
    backPressRefuseReport: false,//物理返回 初始化拦截上报状态


    pageName: '福利中心',
    swiperList: [],//swiper数据源
    welfareTasKInfo: null, //福利任务

    boxFixedShow: false,
    boxTimeCount: -1,
    boxFixedAllowClick: true,//固定悬浮宝箱按钮是否可点击
    boxFiexdText: '立即领取',
    boxTimer: null,

    isJump: false,
    pageDetail: {
      pageRoute: '/pagesA/WelfareNew',
      pageType: '二级页面',
      pageName: '福利页',
      pageUrl: '福利页',
      pageCode: 'READ_WELFARE',  //READ_WELFARE READ_MAIN
      pageOrigin: ''
    },
    statusBarHeight: 40,
    listScrollY: 0,
    listScrollDirection: 'top',
    welfareRedBadInfo: null,
    userBalanceInfo:{
      user_gold: 0, //用户金币
      today_gold: 0, //今日金币
      user_envelope: 0,  //用户红包
      today_envelope: "0",  //今日获取的红包
      user_balance: 0   //用户余额
    },
    isShowShake:false,
    isShowTurntable:false,
    tacticsStatus:0, //策略是否获取完毕
    isFirstAdLoad:false,  //首个广告是否请求完毕
    isCanDirectBack:false,//是否拦截
    isShowExIcon: false,
  },
  public: {
    pathUrl: '',
    intent: 0,
    channelId: null,
    linkId: null,
    adid: '',
    unionid: '',
    creativeid: '',
    campaignid: '',
    materialType: '',
    uniqueId: '',
    hapKey: '',
    routerPushNewView: 1,// 是否需要重新打开当前页面
    isShowDialog: false,
    customToastText: '',
    customToastShow: false,
    setPageBackComplete:false
  },
  computed: {
    signDays() {
      // 已签时间
      let days = this.signTaskList.filter(element => element.isGet == 1)
      return days.length
    },
    allTaskLength(){
      let taskLength = this.dailyTaskList.length + this.newUserTaskList.length
      let itemHeight = 700 - (taskLength - 1) * 140
      return itemHeight > 0 ? itemHeight : 0
    },
    signTaskList() {
      // 签到任务
      return this.welfareTasKInfo ? this.welfareTasKInfo.signInTask.signInfo : []
    },
    newUserTaskList() {
      // 新人任务
      if(this.welfareTasKInfo){
        return this.welfareTasKInfo.newUserTask.task.filter((item)=>item.status != 2)
      }
      return []
    },
    dailyTaskList() {
      // 日常任务
      if (this.welfareTasKInfo) {
        let task = this.welfareTasKInfo.dailyTask.task.map((item) => {
          if (item.behavior_times >= 1 || item.consecutive_days >= 1) {
            switch (item.task_code) {
              case "readNovel":
                item.task_name = `${item.task_name}(${item.times}/${item.behavior_times}分钟)`
                break
              case "invite":
                item.task_name = `${item.task_name}(${item.times}/${item.behavior_times}位)`
                break
              case "readAdvert":
                item.task_name = `${item.task_name}(${item.times}/${item.behavior_times}次)`
                break
              case "consecutiveLogin":
                item.task_name = `${item.task_name}(${item.times}/${item.behavior_times}天)`
                break
            }
          }
        })
      }
      return this.welfareTasKInfo ? this.welfareTasKInfo.dailyTask.task : []
    }
  },
  async onInit() {
    let that = this

    sdk.backPress.backPressPlugin.clear()
    sdk.backPress.backPressPlugin.register()
    POP_TOOLS.changeCurrentView(this)

    if (that.$page.query.clearRouter == 'true') { $utils.clear() }
    that.$app.$def.setSecureByPage(that)
    that.tacticsStatus = 1

    that.getWelfarePageData(true)
    if (Number(sdk.tactics.getCustomParams('is_show_exit_icon'))) {
      that.isShowExIcon = true
    }
    that.isShowShake = Number(sdk.tactics.getCustomParams('is_open_welfare_activity_shake'))
    that.isShowTurntable = Number(sdk.tactics.getCustomParams('is_open_welfare_activity_turntable'))

    POP_TOOLS.commonFunc2PatchPopShow({ actionCode: 'PAGE_ENTRY', code: '', customAction: {} })
    let is_open_ad_circle = Number(sdk.tactics.getCustomParams('welfare_ad_circle'))
    if(is_open_ad_circle){
        let boost_delay_time = Number(sdk.tactics.getCustomParams('boost_delay_time')) || 0
        that.boostTimer = setTimeout(() => {
            if(that.boostTimer){
                clearTimeout(that.boostTimer)
                that.boostTimer = null
            }
            console.log('开启延时补量')
            that.showCustomFill = true
        },boost_delay_time)
    }
    //页面拦截时长
      let timeout = sdk.tactics.getCustomParams('is_activity_allow_back_time')
      LOG('拦截事件===>开始', timeout)
      let timer = setTimeout(() => {
        LOG('拦截事件===>结束')
        that.isCanDirectBack = true
        that.isAllowBackPress = true
      }, Number(timeout));
  },
  onReady() {
    this.statusBarHeight = sdk.env.device.statusBarHeight

  },
  onShow() {
    const that = this
    if (that.isJump) {
      that.isJump = false
      
      that.isAllowBackPress = true
      that.nativeBoostGroupController.startTimeout++
      that.getWelfarePageData()
    }
    AD_UTILS.viewShowHandler()
    
  },
  onHide() {
    this.nativeBoostGroupController.stopTimeout++
    AD_UTILS.viewHideHandler()
    
  },
  // 获取福利页数据
  getWelfarePageData(isEntry = false) {
    this.boxFixedInit()
    // this.getActivityListData()
    this.getWelfareTaskData(isEntry)
    this.getWelfareRedBagData()
    this.getUserBalanceInfoData()
  },
  /**
   * 获取任务列表
   */
  getWelfareTaskData(isEntry=false) {
    $apis.example.getTask().then(res => {
      if (res.code == 200) {
        this.welfareTasKInfo = res.data
        isEntry && this.showAutoSign()
      }
    }).catch(errData => {
      if (typeof errData == 'object' && (errData.code == "timeout" || errData.code == 999)) {
        this.isJump = true
        $utils.routetheUrl('/pagesA/errorPage', { pathUrl: "福利页" }, false)
      }
    })
  },
  // 获取拆红包
  getWelfareRedBagData(){
    $apis.example.welfareRedBagApi().then(res => {
      if (res.code == 200) {
        this.welfareRedBadInfo = res.data
      }
    }).catch(errData => {
    })
  },
  getUserBalanceInfoData() {
    $apis.example.getUserBalanceInfoApi().then(res => {
      if (res.code == 200) {
        this.userBalanceInfo = res.data
      }
    }).catch(errData => {
    })
  },

  // 获取swiper数据
  // getActivityListData() {
  //   $apis.example.getActivityList({}).then(res => {
  //     if (res.code == 200) {
  //       this.swiperList = res.data;
  //     }
  //   }).catch(err => {
  //   })
  // },
  /**
   * 福利任务点击
   */
  dailyTaskClickHandle(evt) {
    if(!CLICK_UTILS.dom_click_vali_shake(`dailyTaskClickHandle_${this.__id__}`,2000)) return
    let item = evt.detail.data
    item.text = evt.detail.text
    switch (item.status) {
      case 0:
        // 去完成
        this.toCompleteTasks(item)
        break
      case 1:
        // 去领取
        this.getBookTicket(item);
        break
      case 3:
        // 已完成
        $utils.showToast('已完成!');
        COMMON_REPORT_UTILS.page_click_report(`${item.task_name}$已完成`)
        break
    }
  },
  /**
   * 签到 立即查看
   */
  signCheckClickHandler(evt) {
    if(!CLICK_UTILS.dom_click_vali_shake(`signCheckClickHandler_${this.__id__}`,2000)) return
    let itemName = this.welfareTasKInfo.signInTask.isSignToday == 1 ? '立即查看' : '立即签到'
    COMMON_REPORT_UTILS.page_click_report(`${itemName}$${this.welfareTasKInfo.signInTask.isSignToday}`)
    // if (this.welfareTasKInfo.signInTask.isSignToday == 1) {
    //   $utils.showToast('您今天已签到!')
    //   return
    // }
    this.welfareButtonClickHandler({ name: 'SIGN_IN', coin: 0, othercoin: 0, isAuto: true })
  },
  /**
   * 福利任务去完成
   */
  toCompleteTasks(item) {
    const that = this
    COMMON_REPORT_UTILS.page_click_report(`${item.task_name|| ''}$${item.text}`)
    switch (item.task_code) {
      case 'recharge':

        break
      case 'readNovel':
        that.$app.$def.tabListType[1] = 0
        this.isJump = true
        $utils.routetheUrl('/pagesA/Main', { selectIndex: 1, pathUrl: "福利页" })
        break
      case 'mornShare':
      case 'noonShare':
      case 'nightShare':
      case "invite":
      case "dayShare":
        this.isJump = true
        $utils.routetheUrl('/pagesB/Activity', { taskCode: item.task_code, pathUrl: "福利页" });
        break
      case 'addDesktop':
        shortcut.hasInstalled({
          success: function (res) {
            if (res) {
              that.checkAddDesktopIsSuccess(item.task_code)
            } else {
              shortcut.install({
                success: function () {
                  that.checkAddDesktopIsSuccess(item.task_code)
                },
                fail: function (data, code) {
                  $utils.showToast('添加失败!');
                }
              })
            }
          }
        })
        break
      case 'addCalendar':
        this.$app.$def.isNeedShowDialogForCalendarClick().then((res) => {
          // 添加到日历
          calendar.insert({
            title: '一点阅读每日读书计划',
            duration: '一点阅读每日读书计划',
            startDate: this.getTime(),
            endDate: '3206606400000',
            remindMinutes: [5],
            rrule: 'FREQ=DAILY;INTERVAL=1;COUNT=1;',
            success: function (data) {
              $utils.showToast('添加成功!');
              that.$app.$def.taskInfoUpdateHandle({
                task_code: item.task_code
              }).then(() => {
                that.getWelfareTaskData()
              })
            },
            fail: function () {
              $utils.showToast('添加失败, 稍后重试!');
            }
          })
        })
        break
      case 'consecutiveLogin':
        if(that.$app.$def.loginStatus == 1) {
          $utils.showToast('今天已完成，明天再来！');
          return
        }
        this.isJump = true
        $utils.routetheUrl('/pagesB/Login', { pathUrl: "福利页", });
        break
      case 'setInterest':
        that.isShowDialog = true
        break
      case 'readAdvert':
        POP_TOOLS.commonFunc2PatchPopShow({ actionCode: "WELFARE_TAKE_AD", code: '', customAction: {task_code:'readAdvert',is_click: 2,coin:item.book_tickets} })
        break
      case 'firstLogin':
        if(that.$app.$def.loginStatus == 1) return
        this.isJump = true
        $utils.routetheUrl('/pagesB/Login', { pathUrl: "福利页", });
        break
      case 'trunPages':
        this.isJump = true
        $utils.routetheUrl('/pagesA/Main', { selectIndex: 1, pathUrl: "福利页" })
        break
    }

  },

  /**
   * 加桌后 判断是否加桌
   */
  checkAddDesktopIsSuccess(taskCode) {
    let that = this;
    let timer = setTimeout(() => {
      shortcut.hasInstalled({
        success: function (res) {
          $utils.showToast('添加成功!');
          that.$app.$def.taskInfoUpdateHandle({
            task_code: taskCode
          }).then(() => {
            that.getWelfareTaskData()
          })
        }
      })
      clearTimeout(timer);
      timer = null
    }, 500)
  },

  /**
   * 福利任务完成 领取金币
   */
  getBookTicket(item) {
    COMMON_REPORT_UTILS.page_click_report(`${item.task_name||''}$去领取`)
    this.welfareButtonClickHandler({ name: item.task_code, coin: item.book_tickets})
  },
  /**
   * 福利页按钮点击事件
   * @param {Object} evt 传递过来参数
   * @description evt.detail.name  
   * SIGN_IN ADD_DESKTOP ADD_CALENDAR INVITE RECHARGE READ_NOVEL UNLOCK_CHAPTRS READ_NEW_NOVEL
   */
  async welfareButtonClickHandler(taskInfo) {
    let that = this
    if(taskInfo.name == 'readAdvert'){
        POP_TOOLS.setDynamicText('AWARD_CLICK', taskInfo.coin)
        POP_TOOLS.commonFunc2PatchPopShow({ actionCode: "AWARD_CLICK", code: '', customAction: { task_code: taskInfo.name, is_click: 1, coin: taskInfo.coin } })
        return
    }
    if (taskInfo.name == 'SIGN_IN' && that.welfareTasKInfo.signInTask.isSignToday != 1) {
      //TODO 打开签到弹窗
      let signItems = that.signTaskList[that.welfareTasKInfo.signInTask.signInDays]
      POP_TOOLS.setDynamicText('SIGN_IN', signItems.gold)
      POP_TOOLS.commonFunc2PatchPopShow({ actionCode: "SIGN_IN", code: '', customAction: { task_code: taskInfo.name, is_click: 1, coin: signItems.gold, isAuto: taskInfo.isAuto ? true : false } })

    } else {
      let coinRes = await that.commonReceiveCoinFunc(taskInfo.name)
      if (coinRes && coinRes.code == 200) {
        //TODO 领取相应的奖励  并打开领取奖励弹窗
        coinRes.data.coupon && $utils.showToast(`恭喜您获得${coinRes.data.coupon}金币`)
        POP_TOOLS.setDynamicText('AWARD_CLICK', taskInfo.coin)
        POP_TOOLS.commonFunc2PatchPopShow({ actionCode: "AWARD_CLICK", code: '', customAction: { task_code: taskInfo.name, is_click: 1, coin: taskInfo.coin } })
      }
    }
  },
  async commonReceiveCoinFunc(popType) {
    let that = this
    return new Promise(async (resolve) => {
      if (popType == 'SIGN_IN' && that.welfareTasKInfo.signInTask.isSignToday != 1) {
        let signRes = await $apis.example.centerTypeApi({ type: 2 })
        if (signRes.data.isSignToday == 2) {
          let signWelfare = await $apis.example.welfareSignInApi({ brand: that.$app.$def.brand })
          if (signWelfare.code == 200) {
            that.$app.$def.isVip = signWelfare.data.isvip
          }
          resolve(signWelfare)
        }
        resolve(0)
      } else {
        let taskWelfare = await $apis.example.welfareReceiveApi({ task_code: popType, brand: that.$app.$def.brand })
        resolve(taskWelfare)
      }
      that.getWelfarePageData()
    })
  },
  /**
   * 悬浮按钮初始化
   */
  boxFixedInit() {
    LOG('悬浮按钮初始化')
    $apis.example.centerTypeApi({ type: 1 }).then(res => {
      this.boxTimeCount = res.data.countDown
      if (this.boxTimeCount > 0) {
        this.boxFixedTimer()
        this.boxFixedShow = true
      } else if (this.boxTimeCount == 0) {
        this.boxFixedShow = true
      } else {
        this.boxFixedShow = false
      }
    })
  },
  boxFixedTimer() {
    this.clearBoxTimer()
    this.boxTimer = setInterval(() => {
      this.boxTimeCount--
      this.boxFiexdText = $utils.formatTime(this.boxTimeCount)
      if (this.boxTimeCount <= 0) {
        clearInterval(this.boxTimer)
        this.boxTimer = null
      }
    }, 1000)
  },
  clearBoxTimer() {
    if (this.boxTimer) {
      clearInterval(this.boxTimer)
      this.boxTimer = null
    }
  },

  /**
 * 宝箱悬浮按钮点击
 */
  async boxFixedClick() {
    const that = this
    if (that.boxTimeCount > 0) {
      $utils.showToast('等待倒计时结束后再领取金币吧')
      return
    } else if (that.boxTimeCount == 0 && that.boxFixedAllowClick) {
      that.boxFixedAllowClick = false
      that.boxFixedShow = false
      setTimeout(() => {
        that.boxFixedAllowClick = true
        that.boxFixedShow = true
        that.boxFixedInit()
      }, 2000)
      let coinRes = await that.commonReceiveCoinFunc('BOX')
      if(coinRes.code == 200){
        POP_TOOLS.setDynamicText('BOX_CLICK', coinRes.data.coupon)
        POP_TOOLS.commonFunc2PatchPopShow({ actionCode: "BOX_CLICK", code: '', customAction: { task_code: 'BOX', is_click: 1 } })
      }
    }
  },
  backBtnClick() {
    COMMON_REPORT_UTILS.page_click_report('返回')
    $utils.goBack()
  },
  onBackPress() {
    return sdk.backPress.handleBackPress()
  },
  /**
   * 接受自定义通用组件控制器派发的数据
   */
  nativeBoostGroupEventDispatchHandler(evt) {
    let that = this
    LOG('VIEW', `nativeBoostGroupEventDispatchHandler======>`, evt)
    let _cudPopCode = that.nativeBoostGroupController.popCode
    switch (evt.detail.eventName) {
      case 'noBoostComp4Use': //没有可使用的补量组件
        // let _cudPopCode = that.nativeBoostGroupController.popCode
        let _cudActionCode = that.nativeBoostGroupController.actionCode
        that.nativeBoostGroupController.popCode = ''
        LOG('CUSTOM', '当前广告池无可用组件')
        if (_cudActionCode !== 'PAGE_BACK' && _cudActionCode !== 'PAGE_ENTRY' && _cudActionCode !== 'POPUP_CLOSE') {
          // $utils.showToast('当前暂无广告，请稍后重试')
        }
        POP_TOOLS.noAd4UseEventHandler({ actionCode: _cudActionCode, code: _cudPopCode, customAction: evt.detail.customAction }, { ...that.pageDetail }, that.$app.$def)
        break
      case 'noPopCode':
        POP_TOOLS.commonFunc2PatchPopShow({ actionCode: "PAGE_BACK", code: '', customAction: {} })
        break
      case 'refresh':
        that.nativeBoostGroupController.popCode = ''
        that.nativeBoostGroupController.popShow = false
        POP_TOOLS.commonFunc2PatchPopShow({ actionCode: evt.detail.actionCode, code: evt.detail.actionCode == 'POPUP_CLOSE' ?  _cudPopCode : '', customAction: {popType:'refresh'} })        
        break
      case 'firstAdLoadComplete':
        LOG('CUSTOM', '第一个广告获取结束')
        LOG('拦截事件===>第一个广告获取结束')
        that.isFirstAdLoad = true
        break
      case 'setPageBackComplete':
        try {
          LOG('CUSTOM', 'pageBackCompleteHandler',that.setPageBackComplete)
          LOG('v207','返回场景预加载结束')
          if(that.setPageBackComplete){

          }else{
            that.setPageBackComplete = true
          }
        } catch (error) {
          LOG('CUSTOM', '返回场景广告获取结束',error)
        }
        break
      case 'closePopData':
        POP_TOOLS.commonPopControllerEventDispatch(evt.detail, { ...that.pageDetail }, that.$app.$def)
        that.nativeBoostGroupController.popShow = false
        that.nativeBoostGroupController.popCode = ''
        break
      case 'popShow':
        that.nativeBoostGroupController.popShow = true
        if (that.nativeBoostGroupController.actionCode == 'SIGN_IN') {
          that.commonReceiveCoinFunc('SIGN_IN')
        }
        if (Number(sdk.tactics.getCustomParams('is_show_exit_icon'))) {
          that.isShowExIcon = false
          setTimeout(() => {
            that.isShowExIcon = true
          }, 500)
        }
        break
    }
  },
  // 打开白青藤或视频自定义弹窗
  addBqtVideoPop(event, popCode = "Baidu_KHEKCERA", actionCode = "PAGE_ENTRY", customAction = {}) {
    let that = this
    let _popCode = popCode
    let _actionCode = actionCode
    let _customAction = customAction
    if (that.videoBqtConfig.isShow) {
      that.videoBqtConfig.isShow = false
    }
    setTimeout(() => {
      that.videoBqtConfig.popCode = `${_popCode}`
      that.videoBqtConfig.actionCode = `${_actionCode}`
      that.videoBqtConfig.customAction = _customAction
      that.videoBqtConfig.isShow = true
    }, 300)
  },
  /**
   * 接受自定义通用视频和百度组件控制器派发的数据
   */
  bqtVideoAdControllerEventDispatchHandler(evt) {
    let that = this
    LOG('BQT_AD', `bqtVideoAdControllerEventDispatchHandler======>`, evt)
    switch (evt.detail.eventName) {
      case 'closePopData':
        POP_TOOLS.bqtVideoAdEventDispatch(evt.detail, { ...that.pageDetail }, that.$app.$def)
        if (evt.detail.action !== 'videoAdClick') {
          that.videoBqtConfig.isShow = false
        }
        break
      case 'adLoadSuccess':
        //签到
        if (evt.detail.actionCode == 'SIGN_IN') that.commonReceiveCoinFunc('SIGN_IN')
        break
        
    }
  },

  // 百度插屏广告
  bqtScreenInsPop(event, popCode = "BaiduScreenInsertion_SOM6EFUP", actionCode = 'PAGE_ENTRY', customAction = {}) {
    LOG("===bqtScreenInsPop")
    let that = this
    let _popCode = popCode
    let _actionCode = actionCode
    let _customAction = customAction
    if (that.screenInserConfig.isShow) {
      that.screenInserConfig.isShow = false
    }
    that.screenInserConfig.popCode = _popCode
    that.screenInserConfig.actionCode = _actionCode
    that.screenInserConfig.customAction = _customAction
    that.screenInserConfig.isShow = true
  },
  
  /**
* 接受插屏广告的回调
*/
  screenInserControllerEventDispatchHandler(evt) {
    let that = this
    LOG('BQT_AD', `screenInserControllerEventDispatchHandler======>`, evt)
    switch (evt.detail.eventName) {
      case 'closePopData':
        if (evt.detail.action == 'adLoadFailed') {
          POP_TOOLS.noAd4UseEventHandler({ actionCode: evt.detail.actionCode, code: evt.detail.popData.popConfig.code, customAction: evt.detail.customAction }, { ...that.pageDetail }, that.$app.$def)
        } else {
          POP_TOOLS.commonPopControllerEventDispatch(evt.detail, { ...that.pageDetail }, that.$app.$def)
        }
        that.screenInserConfig.isShow = false
        break
    }
  },
  getTime() {
    let date = new Date(),
      Y = date.getFullYear(),
      M = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1,
      D = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
    return new Date(`${Y}-${M}-${D} 20:00`).getTime();
  },
  
  // * 页面数据处理方法群 -end-
    /**
   * swiper点击事件
   */
  topHeaderClickHandler(type) {
    if(type == 1){
      COMMON_REPORT_UTILS.page_click_report('点击赚钱')
    }
    this.$element('task-content-list').scrollTo({ index: 1, behavior: 'smooth' })
  },
  // list首个元素隐藏
  listTopHeaderDisApper() {
    if(this.isJump) return
    this.scrollTop = true
  },
  // list首个元素展示
  listTopHeadeApper() {
    this.scrollTop = false
  },
  // 下拉任务置顶
  taskSectionDownHandler() {
    this.$element('task-content-list').scrollTo({ index: 0, behavior: 'smooth' })
  },
  //list停止滚动
  listScrollDown() {
    LOG(this.listScrollDirection, this.listScrollY)
    if (this.listScrollY < 518 && this.listScrollY > 0) {
      this.$element('task-content-list').scrollTo({ index: this.listScrollDirection == 'top' ? 1 : 0, behavior: 'smooth' })
    }
  },
  //list滚动
  listScroll(evt) {
    this.listScrollY += evt.scrollY
    this.listScrollDirection = evt.scrollY > 0 ? 'top' : 'down'
  },

    //定时签到提醒弹窗展示
  async showAutoSign() {
    let isSignAutoShow = Number(sdk.tactics.getCustomParams('sign_hint_second'))
    let isEntrySign = Number(sdk.tactics.getCustomParams('is_sign_page_entry'))
    if(!isSignAutoShow || this.welfareTasKInfo.signInTask.isSignToday == 1 || isEntrySign) return
    try {
      let curDate = $utils.dateToString()
      let res = await $utils.getStorage('_SD_BD_SIGN_TIMES_')
      let iconInfo = res ? JSON.parse(res) : { curDate, showTimes: 0 }
      // 隔天则更新
      if(curDate != iconInfo.curDate){iconInfo = { curDate, showTimes: 0 }}
      // 未加桌 自动提示加桌时长大于0 当前展示次数小于三次
      if (isSignAutoShow > 0 && Number(iconInfo.showTimes) < 3) {
        let addTimer = setTimeout(() => {
          clearTimeout(addTimer)
          addTimer = null
          if (!this.videoBqtConfig.isShow && !this.nativeBoostGroupController.popShow) {
            //非中插 非弹窗
            POP_TOOLS.commonFunc2PatchPopShow({ actionCode: "SIGN_IN_1", code: '', customAction: {} })
            iconInfo.showTimes++
            $utils.setStorage('_SD_BD_SIGN_TIMES_', iconInfo)
          }
        }, isSignAutoShow)
      }
    } catch (error) {
    }

  },

  closeDialog() {
    this.isShowDialog = false
    this.getWelfareTaskData()
  },
  // 检测页面进入是否自动签到 再pop_utils中调用
  checkAutoSign(){
    let isEntrySign = Number(sdk.tactics.getCustomParams('is_sign_page_entry'))
    if (isEntrySign) {
      this.welfareButtonClickHandler({ name: 'SIGN_IN', coin: 0, othercoin: 0, isAuto:true})
    }
  },
  // 活动点击
  activityClickHandler(type,evt){
    this.isJump = true
    evt.stopPropagation()
    if(!CLICK_UTILS.dom_click_vali_shake(`activityClickHandler_${this.__id__}`,2000)) return
    if(type == 'turntable'){
      // 转盘
      $utils.routetheUrl('/pagesC/Action',{actId:'1051'})
    }else{
      //摇一摇
      $utils.routetheUrl('/pagesC/Action',{actId:'1050'})
    }
  },
  showMyToast(content, duration = 2500) {
    if (this.customToastShow) return
    this.customToastText = content
    this.customToastShow = true
    let timer = setTimeout(() => {
      this.customToastShow = false
      this.customToastText = ''
      clearTimeout(timer)
    }, duration);
  },
})
</script>

<style lang="less">
@import '../../assets/styles/style.less';
@import './index.less';
@import '../../assets/styles/animation.less';
</style>
