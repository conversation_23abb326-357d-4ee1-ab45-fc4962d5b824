const path = require('path')
const { copyFile } = require('../../utils')
const appCopyDes = require('../../config/app-copy-des')
const { projectPath } = require('../constant')

function copyAppFile(appInfo, brand) {
  const { category, code } = appInfo

  appCopyDes.forEach(([source, target]) => {
    const from = path.resolve(projectPath, './qkapp', `${category}/${code}`, source)
    const to = path.resolve(projectPath, target)
    copyFile(from, to)

    console.log(`复制成功：${from} -> ${to}`)
  })
}

module.exports = copyAppFile
