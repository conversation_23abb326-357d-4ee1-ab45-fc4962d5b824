const parser = require('@babel/parser')
const { parseVueFile } = require('./base/code-split')
const traverse = require('@babel/traverse').default

function insertPositionComments(jsCode) {
  if (jsCode.indexOf('/* @position: import end */') !== -1) {
    return jsCode
  }

  const ast = parser.parse(jsCode, {
    sourceType: 'module',
    plugins: ['asyncFunctions']
  })

  const comments = []
  let attrStartInserted = false
  let functionStartInserted = false
  let exportStartInserted = false

  let lastImportEnd = 0
  let lastAttrEnd = 0
  let lastFunctionEnd = 0

  traverse(ast, {
    ImportDeclaration(path) {
      if (attrStartInserted) {
        return
      }

      const { end } = path.node
      if (end > lastImportEnd) {
        lastImportEnd = end
      }
    },
    VariableDeclaration(path) {
      // 如果尚未插入 attr start/end 注释，插入在第一个变量声明前后
      if (functionStartInserted) {
        return
      }

      if (!attrStartInserted) {
        comments.push({ index: path.node.start, comment: '/* @position: attr start */\n' })

        attrStartInserted = true
      }

      const { end } = path.node
      if (end > lastAttrEnd) {
        lastAttrEnd = end
      }
    },
    ExpressionStatement(path) {
      // 如果尚未插入 attr start/end 注释，插入在第一个变量声明前后
      if (functionStartInserted) {
        return
      }

      if (!attrStartInserted) {
        comments.push({ index: path.node.start, comment: '/* @position: attr start */\n' })

        attrStartInserted = true
      }

      const { end } = path.node
      if (end > lastAttrEnd) {
        lastAttrEnd = end
      }
    },
    FunctionDeclaration(path) {
      // 在第一个函数声明之前插入 function start
      if (!functionStartInserted) {
        comments.push({ index: path.node.start, comment: '/* @position: function start */\n' })
        functionStartInserted = true
      }
      const { end } = path.node
      if (end > lastFunctionEnd) {
        lastFunctionEnd = end
      }
    },
    ExportDefaultDeclaration(path) {
      const declaration = path.node.declaration
      const properties = declaration.properties
      const start = properties[0].start
      let end = properties[properties.length - 1].end
      if (jsCode.charAt(end) === ',') {
        end += 1
      }
      // 在 export 语句前插入 export start 注释
      if (!exportStartInserted) {
        comments.push({ index: start, comment: '/* @position: export start */\n' })
        comments.push({ index: handleEnd(jsCode, end), comment: '\n/* @position: export end */' })
        exportStartInserted = true
      }
    }
  })

  comments.push({ index: lastImportEnd, comment: '\n/* @position: import end */' })
  comments.push({ index: lastAttrEnd, comment: '\n/* @position: attr end */' })
  comments.push({ index: lastFunctionEnd, comment: '\n/* @position: function end */' })

  // 根据记录的索引插入注释
  comments.sort((a, b) => b.index - a.index) // 从后往前插入，避免索引偏移
  let modifiedJsCode = jsCode
  for (const { index, comment } of comments) {
    modifiedJsCode = modifiedJsCode.slice(0, index) + comment + modifiedJsCode.slice(index)
  }

  return modifiedJsCode
}

// 解析 .vue 文件

function insertUXComments(uxCode) {
  if (uxCode.indexOf('/* @position: export start */') !== -1) {
    return uxCode
  }
  const sfc = parseVueFile(uxCode)

  const scriptCode = sfc.script.content
  const ast = parser.parse(scriptCode, {
    sourceType: 'module',
    plugins: ['asyncFunctions']
  })

  const comments = []

  let exportStartInserted = false
  let objectInserted = false

  let lastImportEnd = 0

  // 找default export 之后的第一个对象
  let objectAfterExport = false

  traverse(ast, {
    ImportDeclaration(path) {
      if (exportStartInserted) {
        return
      }

      const { end } = path.node
      if (end > lastImportEnd) {
        lastImportEnd = end
      }
    },
    ExportDefaultDeclaration(path) {
      exportStartInserted = true
    },
    ObjectExpression(path) {
      if (exportStartInserted) {
        objectAfterExport = true
      }

      if (!objectAfterExport || objectInserted) {
        return
      }
      objectInserted = true
      const properties = path.node.properties
      const exportStart = properties[0].start
      const exportEnd = properties[properties.length - 1].end

      comments.push({ index: exportStart, comment: '/* @position: export start */\n  ' })
      comments.push({ index: handleEnd(scriptCode, exportEnd), comment: '\n  /* @position: export end */' })

      properties.forEach((property, index) => {
        if (!property.key) {
          return
        }
        if (property.key.name === 'private') {
          const { start, end } = getObjectPosition(property.value)
          comments.push({ index: start, comment: '/* @position: private start */\n    ' })
          comments.push({ index: handleEnd(scriptCode, end), comment: '\n    /* @position: private end */' })
        }
        if (property.key.name === 'public') {
          const { start, end } = getObjectPosition(property.value)
          comments.push({ index: start, comment: '/* @position: public start */\n    ' })
          comments.push({ index: handleEnd(scriptCode, end), comment: '\n    /* @position: public end */' })
        }
        if (property.key.name === 'data') {
          const returnObject = property.body.body.find(v => v.type === 'ReturnStatement').argument
          const { start, end } = getObjectPosition(returnObject)
          comments.push({ index: start, comment: '/* @position: data start */\n      ' })
          comments.push({ index: handleEnd(scriptCode, end), comment: '\n      /* @position: data end */' })
        }
        if (property.key.name === 'onHide') {
          specialFunc(property, 'onHide')
        }
        if (property.key.name === 'onShow') {
          specialFunc(property, 'onShow')
        }
        if (property.key.name === 'onInit') {
          specialFunc(property, 'onInit')
        }
      })
    }
  })

  function specialFunc(node, name) {
    if (!node.body) {
      return
    }
    const blockList = node.body.body
    comments.push({ index: blockList[0].start, comment: `/* @position: ${name} start */\n    ` })
    comments.push({ index: blockList[blockList.length - 1].end, comment: `\n    /* @position: ${name} end */` })
  }

  function getObjectPosition(node) {
    const properties = node.properties
    const start = properties[0].start
    const end = properties[properties.length - 1].end

    return {
      start,
      end
    }
  }

  comments.push({ index: lastImportEnd, comment: '\n/* @position: import end */' })

  comments.sort((a, b) => b.index - a.index) // 从后往前插入，避免索引偏移
  let modifiedJsCode = sfc.script.content
  for (const { index, comment } of comments) {
    modifiedJsCode = modifiedJsCode.slice(0, index) + comment + modifiedJsCode.slice(index)
  }

  // 组件import的处理
  const startCode = uxCode.slice(0, sfc.script.contentStartIndex)
  const importEnd = sfc.imports.length ? sfc.imports[sfc.imports.length - 1].end : 0
  const comment = sfc.imports.length ? '\n<!-- @position: comp import end -->' : '<!-- @position: comp import end -->\n'
  const importCode = startCode.slice(0, importEnd) + comment + startCode.slice(importEnd)

  return importCode + modifiedJsCode + uxCode.slice(sfc.script.contentEndIndex)
}

function handleEnd(code, endIndex) {
  if (code.charAt(endIndex) === ',') {
    return endIndex + 1
  }

  return endIndex
}

function isJSCode(code) {
  return code.indexOf('const') > -1 && code.indexOf('<script>') === -1
}

function isUXCode(code) {
  return code.indexOf('<script>') > -1
}

function insertComments(code) {
  if (isJSCode(code)) {
    return insertPositionComments(code)
  }
  if (isUXCode(code)) {
    return insertUXComments(code)
  }
  return code
}

module.exports = {
  insertPositionComments,
  insertUXComments,
  insertComments
}
