const { consoleSplit } = require('../../utils/console')
const path = require('path')
const { createVersionReadme } = require('../../utils/readme')
const { version } = require('../../constants/version')
const { projectPath } = require('../constant')
const createServer = require('../../server')
const fs = require('fs')

function createReadme(srcPath, brand, appType, sdkVersion) {
  consoleSplit('生成 readme')
  const manifest = require(path.resolve(srcPath, 'manifest.json'))
  const packageLockJson = require(path.resolve(projectPath, 'package-lock.json'))
  const sdkCommitHash = packageLockJson.packages?.['node_modules/ad-sdk'].resolved.split('#')[1] || ''
  const readmeJson = {
    ...manifest,
    sdkVersion,
    interfaceVersion: version,
    brand,
    appType,
    sdkCommitHash
  }
  const readmeText = createVersionReadme(readmeJson)
  fs.writeFileSync(path.resolve(projectPath, 'src', 'README.md'), readmeText)

  console.log('生成 readme 成功')
  createServer(readmeJson)
}

module.exports = createReadme
