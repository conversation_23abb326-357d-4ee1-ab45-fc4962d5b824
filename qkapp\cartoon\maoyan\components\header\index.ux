<template>
  <div class="custom-title-bar">
    <div style="align-items:center;">
      <div if="{{showIcon}}" class="img-wrapper" @click="goBack">
        <image
          src="../../assets/v100/{{isBlack?'back-white-icon.png':'back-icon.png'}}"
        ></image>
      </div>
      <text class="{{isBlack?'black-theme':'white-theme'}}">{{ title }}</text>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    isBlack: {
      default: true
    },
    showIcon: {
      default: true
    },
    title: {
      default: ''
    }
  },

  goBack() {
    if (this.$app.$def.brand == 'huawei' || this.$app.$def.brand == 'honor') {
      $utils.sleep(100);
    }
    this.$emit('backClick')
  }
}
</script>

<style lang="less">
@import '../../assets/styles/index.less';

.custom-title-bar {
  align-items: center;
  width: 100%;
  height: 175px;
  padding: 30px @app-padding 0;
  background-color: #ffffff;

  .img-wrapper {
    padding: @app-padding;
    padding-left: 0;

    > image {
      width: 30px;
      height: 30px;
    }
  }

  text {
    .text-title;
  }

  .black-theme,
  .white-theme {
    lines: 1;
    text-overflow: ellipsis;
  }

  .black-theme {
    color: @text-black;
  }

  .white-theme {
    color: @white;
  }
}
</style>
