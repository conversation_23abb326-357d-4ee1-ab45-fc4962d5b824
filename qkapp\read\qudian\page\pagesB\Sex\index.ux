/**
* page : 男女选择页面
* author: yangyang
* date: 2022-03-03
*/
<template>
  <div class="sex-wrapper">
        <!-- 状态栏 -->
    <common-header
      title="性别选择"
      text-center="{{ backTitleIsCenter }}"
      onback-click="pageBack"
      back-size="normal"
    ></common-header>
    <div class="sex-title-box">
      <text class="sex-title">请选择你的性别</text>
    </div>
    <text class="sex-des">我们将为你推荐感兴趣的书籍</text>
    <div class="sex-select">
      <div class="sex-select-item" @click="sexSelected(1)">
        <image
          class="sex-select-item-image"
          src="https://img.qdreads.com/v163/user/man-head.png"
        ></image>
      </div>
      <div class="sex-select-item" @click="sexSelected(2)">
        <image
          class="sex-select-item-image"
          src="https://img.qdreads.com/v163/user/woman-head.png"
        ></image>
      </div>
    </div>

    <!-- 隐私政策弹窗start -->
    <div class="prompt-pop-modal" if="isShowPrompt">
      <div class="prompt-pop">
        <div class="content">
          <div>
            <text class="title"> 用户隐私政策概要 </text>
          </div>
          <div>
            <text style="text-indent: 60px">感谢您信用使用趣点阅读。</text>
          </div>
          <div>
            <text style="text-indent: 60px">
              <span
                >我们非常重视您的个人信息和隐私保护。为了更好的保障您的个人权益，在您使用我们的产品前，请认真的阅读：</span
              >
              <a href="/pagesB/Privacy?type=1">《用户协议》</a>
              <span>以及</span>
              <a href="/pagesB/Privacy?type=2">《隐私政策》</a>
              <span
                >，如果您已同意以上协议内容，请点击“同意”，开始使用我们的产品和服务！</span
              >
            </text>
          </div>
        </div>
        <div class="bottom">
          <text class="default-btn btn" @click="disagree">不同意</text>
          <text class="active-btn btn" @click="agree">同意</text>
        </div>
      </div>
    </div>
    <!-- 隐私政策弹窗end -->
    <!-- 隐私保护提示 弹窗start -->
    <div class="prompt-pop-modal" if="isShowPromptTip">
      <div class="prompt-pop">
        <div class="content" style="padding-left: 50px; padding-right: 50px">
          <text class="title"> 隐私保护提示 </text>
          <text style="text-indent: 60px">
            <span
              >请您放心，趣点阅读坚决保障您的隐私安全，请您认真阅读并同意：</span
            >
            <a href="/pagesB/Privacy?type=1">《用户协议》</a>
            <span>以及</span>
            <a href="/pagesB/Privacy?type=2">《隐私政策》</a>
            <span>，全部条款才可以继续使用。</span>
          </text>
        </div>
        <div class="bottom">
          <text class="default-btn btn" @click="disagree(1)">不同意并退出</text>
          <text class="active-btn btn" @click="backPrompt">再想想</text>
        </div>
      </div>
    </div>
    <!-- 隐私保护提示 end -->
    <!-- 返回腾讯系App的button -->
    <back-app-button
      if="{{$app.$def.isTencentBackButtonVisible}}"
      btn-text="{{$app.$def.tencentBackName}}"
      back-url="{{$app.$def.tencentBackUrl}}"
      package-name="{{$app.$def.tencentBackPkg}}"
    ></back-app-button>
  </div>
</template>
<import name="back-app-button" src="../../components/back-app-button"></import>
<import name="common-header" src="../../components/common-back-header/index"></import>

<script>
export default pageMixin({
  data: () => ({
    pathUrl: "",
    type: 0,
    currentIndex: 2, //性别标识  1： 男 2： 女
    isShowPrompt: false, // 隐私政策弹窗
    isShowPromptTip: false, // 隐私政策提示弹窗 （二次提示）
    isWriteOff: false,
    pageDetail:{
      pageUrl:'选择性别页',
      pageName: '选择性别页',
      pageCode: 'READ_COMMON',
      pageOrigin: ''
    },
    backTitleIsCenter: false, // 状态栏文字居左
  }),
  /**
   * 性别选中-点击事件
   * @param i  1： 男 2： 女
   */
  sexSelected(i) {
    // v3.4.0 点击上报
    COMMON_REPORT_UTILS.page_click_report(`${i == 1 ? '男' : '女'}`) //点击上报
    this.currentIndex = i;
    this.sexConfirm();
  },
  onInit() {
    
    // 如果同意了，就不需要再次确认
    $utils.getStorage('privacyAgree').then((res) => {
      if (res && res == 1) {
        this.isShowPrompt = false;
      } else {
        this.isShowPrompt = true;
      }
    })

    this.currentIndex = this.$app.$def.sex

    if (this.isWriteOff) {
      $utils.clear()
      this.isWriteOff = false
    }
  },
  onShow() {
    // 更新页面来源
    this.pathUrl = curObj.pathUrl
  },
  /**
   * 偏好设置-性别选择确认
   */
  sexConfirm() {
    COMMON_REPORT_UTILS.page_click_report('确认') //点击上报
    if (Number(this.type) == 1) {
      this.uploadInfo(this.currentIndex);
    } else {
      this.$app.$def.login({isSetSex:1,sex: this.currentIndex}).then(res => {
        if (res.code == 200) {
          this.$app.$def.tabListType = [0, 0, 0, 0, 0]
          $utils.routetheUrl('/pagesA/Main', { selectIndex: 1, pathUrl: "性别选择页" }, true)
        } else if (res.code == 409) {
          $utils.routetheUrl('/pagesB/Login', { pathUrl: "性别选择页" })
        }
      })
    }
  },
  uploadInfo(index) {
    $apis.example.uploadInfo({ sex: index }).then(res => {
      if (res.code == 200) {
        this.$app.$def.sex = index
        $utils.setStorage('userInfo', res.data)
        $utils.goBack()
      } else {
        $utils.showToast(res.msg, 0)
      }
    })
  },
  /**
   * 物理返回
   */
  onBackPress() {
    COMMON_REPORT_UTILS.back_click_report('','','跳转页面')
  },
  // 用户协议&隐私政策
  privacylink(i) {
    $utils.routetheUrl('/pagesB/Privacy', { type: i }, false)
  },
  // 同意隐私政策
  agree() {
    this.isShowPrompt = false;
    $utils.setStorage('privacyAgree', 1)
    // 初始化百度SDK 百度SDK会请求设备id
  },
    pageBack() {
    $utils.goBack()
    COMMON_REPORT_UTILS.page_click_report( `返回`)
  },
  // 不同意隐私政策
  disagree(type) {
    if (type == 1) {
      this.$app.exit()  // 不同意退出应用
      return
    }
    this.isShowPrompt = false;
    this.isShowPromptTip = true;
  },
  // 返回隐私政策
  backPrompt() {
    this.isShowPrompt = true;
    this.isShowPromptTip = false;
  }
})
</script>

<style lang="less">
@import './index.less';
</style>
