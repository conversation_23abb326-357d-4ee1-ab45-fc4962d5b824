// 修改业务文件
const { consoleSplit } = require('../../utils/console')
const path = require('path')
const fs = require('fs')
const { projectPath } = require('../constant')
const { modifyFile } = require('../../utils/replace')
const { createModifyReadme } = require('../../utils/readme')

// 加载配置的通用函数
function loadConfig(configName, appType) {
  const configPath = path.resolve(__dirname, `../../config/business/${configName}/index.js`)
  if (!fs.existsSync(configPath)) {
    console.log(`配置文件 ${configName} 不存在`)
    return null
  }
  return require(configPath).getConfig(appType)
}

function applyModifications(configContent, getFilePath, configName) {
  const failInfos = []
  configContent.forEach(({ filePath, modifications }) => {
    if (!filePath) return

    const fullPath = getFilePath(filePath)
    if (!fs.existsSync(fullPath) || !fs.statSync(fullPath).isFile()) {
      console.log(`文件不存在：${fullPath}`)
      return
    }

    const failInfo = modifyFile(fullPath, modifications)
    failInfos.push(...failInfo)
  })
  createModifyReadme(configName, failInfos)
}

function modifyBusinessFile(appInfo, configName, appType) {
  const configContent = loadConfig(configName, appType)
  if (!configContent) return

  consoleSplit('修改公共业务文件')
  applyModifications(configContent.public, filePath => path.resolve(projectPath, filePath), configName)

  consoleSplit('修改应用业务文件')
  const { category, code } = appInfo
  applyModifications(
    configContent.app,
    filePath => path.resolve(projectPath, './qkapp', `${category}/${code}`, filePath),
    configName
  )
}

module.exports = modifyBusinessFile
