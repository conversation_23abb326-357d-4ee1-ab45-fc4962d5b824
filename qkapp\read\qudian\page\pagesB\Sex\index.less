/**
* page : 男女选择页面样式
* author: ya<PERSON><PERSON>
* date: 2022-03-03
*/
.sex-wrapper {
    flex-direction: column;
    background-color: #fff;
    .sex-title-box {
        margin-top: 338px;
        justify-content: center;
        align-items: center;
        .sex-title-mark {
            width: 12px;
            height: 12px;
            border: 6px solid #38415f;
            border-radius: 50%;
        }
        .sex-title {
            font-size: 36px;
            font-weight: bold;
            color: #333333;
            margin: 0 8px;
            height: 36px;
        }
    }
    .sex-des {
        font-size: 24px;
        color: #666666;
        height: 24px;
        margin-top: 30px;
        text-align: center;
        line-height: 24px;
    }
    .sex-select {
        width: 750px;
        flex-direction: row;
        padding: 100px 120px;
        justify-content: space-between;
        .sex-select-item {
            width: 208px;
            flex-direction: column;
            background-color: rgba(255, 255, 255, 0.99);
            .sex-select-item-image {
                width: 208px;
                height: 263px;
                object-fit: cover;
            }
        }
    }
    .sex-confirm {
        width: 600px;
        height: 88px;
        background-color: #38415f;
        border-radius: 200px;
        font-size: 40px;
        font-weight: 700;
        color: #ffffff;
        text-align: center;
        margin: 0 auto;
        margin-top: 240px;
    }
    .prompt-pop-modal {
        position: fixed;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.85);
        display: flex;
        align-items: center;
        justify-content: center;
        .prompt-pop {
          width: 600px;
          flex-direction: column;
          text {
            font-size: 30px;
            color: #666;
            font-size: 30px;
          }
          .content {
            border-top-left-radius: 20px;
            border-top-right-radius: 20px;
            padding: 20px 40px 46px 40px;
            flex-direction: column;
            background-color: #fff;
            .title {
              width: 100%;
              text-align: center;
              font-size: 32px;
              color: #333;
              font-weight: bold;
              height: 80px;
            }
            a {
                color:#F11212
            }
          }
      
        .bottom {
            width: 100%;
            height: 88px;
            .btn {
                width: 50%;
                color: #999;
                text-align: center;
                background-color: #fff;
            }
            .default-btn {
                border-bottom-left-radius: 20px;
                // border-top: 2px solid #f6f6f6;
            }
            .active-btn {
                background-color: #F11212;
                color: #fff;
                text-align: center;
                border-bottom-right-radius: 20px;
            }
          }
        }
      }
}