#!/usr/bin/env node

const { program } = require('commander')
const mainTask = require('./main')
const updateBusiness = require('./actions/business')
const sdkAction = require('./actions/sdk/index')

program
  .command('init')
  .description('初始化配置文件')
  .action(() => {
    require('./config/init')
  })

program
  .command('run')
  .option('--sdk <sdk>', '指定SDK版本')
  .option('--app <app>', '指定APP版本')
  .option('--brand <brand>', '指定产商')
  .option('--exit', '是否退出')
  .option('--ignoreSDK', '忽略SDK选择')
  .option('--upgrade', '更新版本号')
  .description('执行接入任务')
  .action(options => {
    console.log(options)
    mainTask(options)
  })

program
  .command('up')
  .description('升级业务代码')
  .action(() => {
    updateBusiness()
  })

program
  .command('sdk')
  .option('--brand <brand>', '产商名称')
  .description('sdk 代码运行')
  .action(options => {
    sdkAction(options)
  })

program.parse()
