const fs = require('fs')
const path = require('path')
const process = require('process')

function createVersionReadme({
  package,
  name,
  versionName,
  versionCode,
  minPlatformVersion,
  sdkVersion,
  interfaceVersion,
  brand,
  appType,
  sdkCommitHash
}) {
  const readmeText = `
# APP 信息
> app 信息如下，请仔细核对

## 包名
${package}

## 应用名
${name}

## 版本名
${versionName}

## 版本号
${versionCode}

## 最低支持平台
${minPlatformVersion}

## SDK 版本
[${sdkVersion}](https://gitlab.ghfkj.cn/cy/fre/quickapp/ad-sdk/-/commit/${sdkCommitHash})

## 对接版本
${interfaceVersion}

## 厂商
${brand}

## app 类型
${appType}
  `

  return readmeText
}

function createModifyReadme(version, failInfo) {
  const readmeText = failInfo.reduce((content, cur) => {
    return content + `### 修改点 ${cur.desc}\n\`\`\`\n${cur.oldCode}\n\`\`\`\n`
  }, `# SDK ${version} 修改失败代码段\n`)

  console.log('readmeText', readmeText)

  fs.writeFileSync(path.resolve(process.cwd(), version + '.md'), readmeText)
}

module.exports = {
  createVersionReadme,
  createModifyReadme
}
