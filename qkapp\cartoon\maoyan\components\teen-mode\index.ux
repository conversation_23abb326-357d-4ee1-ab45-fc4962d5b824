<template>
  <div class="prompt-pop-modal" @click="closeModal">
    <div class="mode-wrapper" @click="stopPro">
      <text class="mode-title">猫沇漫画青少年模式</text>
      <text class="mode-content">为呵护未成年人健康成长，猫沇漫画特别推出青少年模式，该模式下部分功能无法正常使用。请监护人主动选择，并设置监护密码。</text>
      <text @click="entryTeenMode" class="mode-btn mode-confirm">进入青少年模式</text>
      <text @click="closeModal" class="mode-btn">我知道了</text>
    </div>
  </div>
</template>

<script>
export default {
  data: {

  },
  onInit() {},
  closeModal() {
    this.$emit('eventWatch', { action: 'close' });
  },
  entryTeenMode() {
    this.$emit('eventWatch', { action: 'agree' });
  },
  stopPro(evt) {
    evt.stopPropagation();
  },
}
</script>

<style lang="less">
@import '../../assets/styles/index.less';

.prompt-pop-modal {
  position: fixed;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.85);
  align-items: center;
  justify-content: center;
}

.mode-wrapper {
  width: 530px;
  background-color: #ffffff;
  border-radius: 40px;
  flex-direction: column;
  align-items: center;
}

.mode-title {
  height: 40px;
  color: #333333;
  font-size: 40px;
  font-weight: bold;
  margin-top: 60px;
}

.mode-content {
  width: 450px;
  height: 176px;
  color: #666666;
  font-size: 28px;
  line-height: 40px;
  margin-top: 40px;
}

.mode-btn {
  width: 100%;
  height: 96px;
  border-top: 1px solid #eeeeee;
  color: #999999;
  text-align: center;
}

.mode-confirm {
  margin-top: 60px;
  color: @brand;
  font-weight: bold;
}
</style>
