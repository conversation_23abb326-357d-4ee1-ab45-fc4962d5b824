@font-face {
  font-family: 'iconfont';
  src: url('/assets/iconfont/iconfont.ttf');
}

.iconfont {
  font-family: 'iconfont';
}

/* 

// 快应用不支持伪类，需要用unicode的方式实现iconfont，对应的Unicode为'&#x' + content中‘\’后的内容
// 例如.icon-search，content中‘\’后的内容为‘e661’，对应的Unicode就是'&#xe661'，应用实例为：<text>&#xe661;</text>
// 该项目的iconfont对应的unicode如下：

.icon-search:before {
  content: "\e661";
}

.icon-arrow-left:before {
  content: "\e606";
}

.icon-user:before {
  content: "\e643";
}

.icon-menu:before {
  content: "\e60c";
}

.icon-edit:before {
  content: "\e641";
}

.icon-message:before {
  content: "\e60e";
}

.icon-shelf:before {
  content: "\e601";
}

.icon-shelf-line:before {
  content: "\e602";
}

.icon-search-book-line:before {
  content: "\e603";
}

.icon-search-book:before {
  content: "\e604";
}

.icon-refresh:before {
  content: "\e607";
}

.icon-order:before {
  content: "\e624";
}

.icon-order-reverse:before {
  content: "\e6d6";
}

.icon-library:before {
  content: "\e67d";
}

.icon-library-line:before {
  content: "\e681";
}

.icon-arrow-right:before {
  content: "\e6d7";
}
*/
