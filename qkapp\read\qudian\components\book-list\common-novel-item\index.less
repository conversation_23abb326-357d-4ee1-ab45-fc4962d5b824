.novel-item {
    width: 100%;
    height: 180px;
    flex-direction: row;
    margin-top: 32px;
    .novel-pic {
        width: 134px;
        height: 180px;
        margin-right: 24px;
        object-fit: fill;
        border-radius: 8px;
    }
    .novel-detail {
        flex: 1;
        flex-direction: column;
        .novel-title {
            lines: 1;
            text-overflow: ellipsis;
            height: 32px;
            font-size: 32px;
            font-weight: 700;
            color: #333;
        }
        .novel-desc {
            height: 72px;
            line-height: 36px;
            font-size: 24px;
            color: #666;
            lines: 2;
            text-overflow:ellipsis;
        }
        .novel-bottom-detail {
            flex-direction: row;
            height: 51px;
            padding-top: 15px;
            .novel-author {
                flex: 1;
                height: 36px;
                font-size: 20px;
                color: #999999;
            }
            .novel-type {
                height: 28px;
                margin-top: 4px;
                padding: 0 7px;
                border-radius: 6px;
                font-size: 20px;
                background-color: #f6f6f6;
                color: #999999;
            }
            .novel-type-1{
                background-color: #edf9ff;
                color: #0989FF;
            }
            .novel-type-2{
                background-color: #F5FBE3;
                color: #8BB80C;
            }
            .novel-type-3{
                background-color: #F6F1FF;
                color: #9157FF;
            }
            .novel-type-4{
                background-color: #F5F5F5;
                color: #7C7C7C;
            }
            .novel-type-5{
                background-color: #FFF3F5;
                color: #FF5E5E;
            }
        }
    }

    .nd-name-wrapper {
        justify-content: space-between;
        align-items: center;
        margin-top: 8px;
        margin-bottom: 24px;
        
        .ly-ily {
            height: 24px;
            font-size: 24px;
            font-weight: 500;
            color: #ff3d00;
            flex-shrink: 0;
        }
    }
}

.search-novel-item {
    width: 100%;
    height: 192px;
    flex-direction: row;
    margin-top: 32px;
    .novel-pic {
        width: 134px;
        height: 192px;
        margin-right: 30px;
        object-fit: fill;
        border-radius: 8px;
    }
    .novel-detail {
        flex: 1;
        flex-direction: column;
        .novel-title {
            lines: 1;
            text-overflow: ellipsis;
            height: 32px;
            line-height: 32px;
            font-size: 32px;
            font-weight: 700;
            color: #333;
        }
        .novel-desc {
            height: 80px;
            line-height: 40px;
            font-size: 24px;
            color: #666;
            lines: 2;
            text-overflow:ellipsis;
        }
        .novel-bottom-detail {
            flex-direction: row;
            height: 51px;
            padding-top: 15px;
            .novel-author {
                flex: 1;
                height: 36px;
                font-size: 20px;
                color: #999999;
            }
            .novel-type {
                height: 28px;
                margin-top: 4px;
                padding: 0 7px;
                border-radius: 6px;
                font-size: 20px;
                background-color: #f6f6f6;
                color: #999999;
            }
            .novel-type-1{
                background-color: #edf9ff;
                color: #0989FF;
            }
            .novel-type-2{
                background-color: #F5FBE3;
                color: #8BB80C;
            }
            .novel-type-3{
                background-color: #F6F1FF;
                color: #9157FF;
            }
            .novel-type-4{
                background-color: #F5F5F5;
                color: #7C7C7C;
            }
            .novel-type-5{
                background-color: #FFF3F5;
                color: #FF5E5E;
            }
        }
    }

    .nd-name-wrapper {
        justify-content: space-between;
        align-items: center;
        margin-top: 8px;
        margin-bottom: 16px;
        
        .ly-ily {
            height: 24px;
            font-size: 24px;
            font-weight: 500;
            color: #ff3d00;
            flex-shrink: 0;
        }
    }
}