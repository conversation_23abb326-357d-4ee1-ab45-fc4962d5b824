const fs = require('fs')
const path = require('path')
const { projectPath } = require('../constant')
const CONFIG_FILE_NAME = 'app-define-plugin.json'
const { snakeCase } = require('lodash')

function redefineAppConfig(appConfig, brand) {
  const plugin = {
    pro: {},
    dev: {}
  }

  for (const [key, value] of Object.entries(appConfig)) {
    const upperKey = `__${snakeCase(key).toUpperCase()}__`
    if (key === 'videoAdUnitId' && value && typeof value === 'object') {
      const brandValue = value[brand] || ''
      plugin.pro[upperKey] = JSON.stringify(brandValue)
    } else if (value && typeof value === 'object' && 'dev' in value && 'prod' in value) {
      value.dev && (plugin.dev[upperKey] = JSON.stringify(value.dev))
      value.prod && (plugin.pro[upperKey] = JSON.stringify(value.prod))
    } else {
      plugin.pro[upperKey] = JSON.stringify(value)
    }
  }
  return plugin
}

function createAppDefinePlugin(appConfig, brand) {
  const plugin = redefineAppConfig(appConfig, brand)
  console.log('app-define-plugin---start', plugin)
  console.log('app-define-plugin---end')
  const appConfigPath = path.resolve(projectPath, `src/${CONFIG_FILE_NAME}`)
  fs.writeFileSync(appConfigPath, JSON.stringify(plugin, null, 2))
  console.log('生成 APP define plugin 成功')
}

module.exports = {
  createAppDefinePlugin
}
