module.exports = {
  root: true,
  parser: '@babel/eslint-parser',
  env: {
    browser: true,
    es6: true,
    node: true,
    commonjs: true
  },
  extends: ['alloy'],
  plugins: ['prettier'],
  globals: {
    LOG: true,
    DB_RESERVE: true,
    COMMON_REPORT_UTILS: 'readonly',
    global: 'readonly',
    DATA_COLLECTION_SDK: 'readonly',
    sdk: 'readonly',
    POP_TOOLS: 'readonly',
    $pagePool: 'readonly',
    $getAppConfig: 'readonly'
  },
  parserOptions: {
    ecmaVersion: 2018,
    sourceType: 'module'
  },
  rules: {
    'prettier/prettier': ['error', { endOfLine: 'auto' }],
    'no-debugger': 2,
    'no-unused-vars': 1,
    'no-param-reassign': 0,
    'no-useless-catch': 1,
    'max-params': ['error', 5],
    'no-unused-expressions': 0,
    'no-useless-constructor': 0,
    indent: ['error', 2, { SwitchCase: 1 }],
    // 符号 , : 后有空格
    'comma-spacing': ['error', { before: false, after: true }],
    // 关键词 if else 前后有空格
    'keyword-spacing': ['error', { before: true, after: true }],
    // 大括号前有空格
    'space-before-blocks': 2,
    // case 语句不能声明语句，禁止
    'no-case-declarations': 0,
    // 结尾分号
    semi: ['error', 'never'],
    'object-curly-spacing': ['error', 'always']
  }
}
