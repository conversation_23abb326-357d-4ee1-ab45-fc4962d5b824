const path = require('path')
const { projectPath } = require('../main/constant')

function getSdkVersion() {
  const packageJson = require(path.resolve(projectPath, 'scripts/config/cy/var-config.js'))
  const versionFn = packageJson.pro.SDK_VERSION_VALUE
  return versionFn()
}

/**
 * 比较版本大小
 * @param {string} version - 待比较的版本号
 * @param {string} baseVersion - 基准版本号
 * @return {boolean} 如果 version 大于 baseVersion 则返回 true，否则返回 false
 */
function biggerThanCurVersion(version, baseVersion) {
  // 将版本号字符串按 . 分割成数字数组
  const versionParts = version.split('.').map(Number)
  const baseVersionParts = baseVersion.split('.').map(Number)

  // 同大版本比较
  if (versionParts[0] !== baseVersionParts[0]) {
    return false
  }

  // 获取两个版本号数组的最大长度
  const maxLength = Math.max(versionParts.length, baseVersionParts.length)

  for (let i = 0; i < maxLength; i++) {
    // 如果当前位置版本号不存在，默认为 0
    const v1 = versionParts[i] || 0
    const v2 = baseVersionParts[i] || 0

    if (v1 > v2) {
      return true
    } else if (v1 < v2) {
      return false
    }
    // 如果当前位相等，继续比较下一位
  }

  // 所有位都相等，返回 true
  return true
}

module.exports = {
  getSdkVersion,
  biggerThanCurVersion
}
