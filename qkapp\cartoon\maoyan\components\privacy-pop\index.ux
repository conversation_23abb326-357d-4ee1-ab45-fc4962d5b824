<template>
  <div>
    <!-- 隐私政策弹窗start -->
    <div class="prompt-pop-modal" if="isShowPrompt">
      <div class="prompt-pop">
        <div class="content">
          <div>
            <text class="title"> 用户隐私政策概要 </text>
          </div>
          <div>
            <text style="text-indent: 60px">感谢您信任使用猫沇漫画。</text>
          </div>
          <div>
            <text style="text-indent: 60px">
              <span
                >我们非常重视您的个人信息和隐私保护。为了更好的保障您的个人权益，在您使用我们的产品前，请认真的阅读：</span
              >
              <a href="/subPages/privacy?type=1">《用户协议》</a>
              <span>以及</span>
              <a href="/subPages/privacy?type=2">《隐私政策》</a>
              <span
                >，如果您已同意以上协议内容，请点击“同意”，开始使用我们的产品和服务！</span
              >
            </text>
          </div>
        </div>
        <div class="bottom">
          <text class="default-btn btn" @click="disagree">不同意</text>
          <text class="active-btn btn" @click="agree">同意</text>
        </div>
      </div>
    </div>
    <!-- 隐私政策弹窗end -->
    <!-- 隐私保护提示 弹窗start -->
    <div class="prompt-pop-modal" if="isShowPromptTip">
      <div class="prompt-pop">
        <div class="content" style="padding-left: 50px; padding-right: 50px">
          <text class="title"> 隐私保护提示 </text>
          <text style="text-indent: 60px">
            <span
              >请您放心，猫沇漫画坚决保障您的隐私安全，请您认真阅读并同意：</span
            >
            <a href="/subPages/privacy?type=1">《用户协议》</a>
            <span>以及</span>
            <a href="/subPages/privacy?type=2">《隐私政策》</a>
            <span>，全部条款才可以继续使用。</span>
          </text>
        </div>
        <div class="bottom">
          <text class="default-btn btn" @click="disagree(1)">不同意并退出</text>
          <text class="active-btn btn" @click="backPrompt">再想想</text>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data: {
    isShowPromptTip: false,
    isShowPrompt: true
  },
  onInit() {},
  // 同意隐私政策
  agree() {
    this.isShowPrompt = false
    this.$emit('eventWatch', { type: 'agree' })
  },
  // 不同意隐私政策
  disagree(type) {
    if (type == 1) {
      this.$app.exit() // 不同意退出应用
      return
    }
    this.isShowPrompt = false
    this.isShowPromptTip = true
  },
  // 返回隐私政策
  backPrompt() {
    this.isShowPrompt = true
    this.isShowPromptTip = false
  }
}
</script>

<style lang="less">
@import '../../assets/styles/index.less';

.prompt-pop-modal {
  position: fixed;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.85);
  display: flex;
  align-items: center;
  justify-content: center;
  .prompt-pop {
    width: 600px;
    flex-direction: column;
    text {
      font-size: 30px;
      color: #666;
      font-size: 30px;
    }
    .content {
      border-top-left-radius: 20px;
      border-top-right-radius: 20px;
      padding: 20px 40px 46px 40px;
      flex-direction: column;
      background-color: #fff;
      .title {
        width: 100%;
        text-align: center;
        font-size: 32px;
        color: #333;
        font-weight: bold;
        height: 80px;
      }
      a {
        color: @brand;
      }
    }

    .bottom {
      width: 100%;
      height: 88px;
      .btn {
        width: 50%;
        color: #999;
        text-align: center;
        background-color: #fff;
      }
      .default-btn {
        border-bottom-left-radius: 20px;
      }
      .active-btn {
        background-color: @brand;
        color: #fff;
        text-align: center;
        border-bottom-right-radius: 20px;
      }
    }
  }
}
</style>
