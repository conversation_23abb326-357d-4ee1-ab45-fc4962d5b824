<template>
  <div class="wrapper">
    <common-header title="浏览记录" text-center="{{false}}" onback-click="pageBack"></common-header>
    <list style="flex: 1;" onscrollbottom="scrollBottomHandler">
      <block if="{{true}}">
        <block>
          <list-item type="title" style="margin: 30px;">
            <text style="height: 36px;font-size: 36px;font-weight: 600;color: #333333;">今天</text>
          </list-item>
          <list-item type="item" class="rh-item" for="{{item in todayList}}" @click="bookItemClick(item)">
            <image src="{{item.public}}" class="rhi-image"></image>
            <div class="rhi-info">
              <text class="rhi-info-name">{{item.title}}</text>
              <text class="rhi-info-title">{{item.chapter}}</text>
            </div>
            <text class="rhi-button" style="color: {{item.is_shelf ? '#999999' : '#f11212'}}" @click="addShelf(item)">{{item.is_shelf ? '已加书架' : '加入书架'}}</text>
          </list-item>
        </block>
        
        <list-item type="title" style="margin: 0 0 30px 30px;">
          <text style="height: 36px;font-size: 36px;font-weight: 600;color: #333333;">更早</text>
        </list-item>
        <list-item type="item" class="rh-item" for="{{item in prevList}}" @click="bookItemClick(item)">
          <image src="{{item.public}}" class="rhi-image"></image>
          <div class="rhi-info">
            <text class="rhi-info-name">{{item.title}}</text>
            <text class="rhi-info-title">{{item.chapter}}</text>
          </div>
          <text class="rhi-button" style="color: {{item.is_shelf ? '#999999' : '#f11212'}}" @click="addShelf(item)">{{item.is_shelf ? '已加书架' : '加入书架'}}</text>
        </list-item>
        <list-item class="load-more" if="{{loadMore}}" type="showmode">
          <progress type="circular"></progress>
          <text>加载更多！</text>
        </list-item>
        <list-item class="load-more" else type="showmode">
          <text class="no-more">没有更多了</text>
        </list-item>
      </block>
      <block else>

      </block>
    </list>
  </div>
</template>

<import name="common-header" src="../../components/common-back-header"></import>
 
<script>
export default {
  public: {
    pageDetail: {
      pageUrl: '浏览记录页',
      pageName: '浏览记录页',
      pageCode: 'READ_COMMON',
      pageOrigin: ''
    },
    pathUrl: '',
    todayList: [],
    prevList: [],
    page: 1, // 当前页码
    pageSize: 20, // 页面大小
    totalPage: 0, // 消费记录  总页码
    loadMore: true,
  },

  onInit() {
    this.getData(true)
  },
  onShow() {
    // 更新页面来源
    this.pathUrl = curObj.pathUrl
    
    
  },
  onHide() {
    
  },
  async getData(init = false) {
    let res = await $apis.example.readHistoryApi({page: this.page, page_size: 10})
    if (res.code == 200) {
      if (init) this.todayList = res.data.today
      this.prevList = this.prevList.concat(res.data.ago.list)
      this.loadMore = res.data.ago.list.length < 10 ? false : true
    }
  },
  async addShelf(item, evt) {
    evt.stopPropagation()
    if (item.is_shelf) return
    let res = await $apis.example.addBookshelfApi({ bookId: item.bid})
    if (res.code == 200) {
      item.is_shelf = 1
      this.$app.$def.tabListType[0] = 0
    }
  },
  /**
   * list加载更多（上拉加载）
   */
  scrollBottomHandler() {
    this.page++
    if (!this.loadMore) {
      $utils.showToast('没有更多数据了！', 0)
      return
    } else {
      this.getData()
    }
  },
  /**
   * 书籍item点击通用事件
   */
  bookItemClick(bookInfo,sectionName) {
    if(!CLICK_UTILS.dom_click_vali_shake(`readHistory_${this.__id__}`,500)) return
    let params = {
      bookId: bookInfo.bid,
      pathUrl: "浏览记录页"
    }
    $utils.routetheUrl('/pagesC/Info', params, false)
  },
  pageBack() {
    COMMON_REPORT_UTILS.page_click_report('返回') //点击上报

    $utils.goBack()
  },
  onBackPress() {
    COMMON_REPORT_UTILS.back_click_report('','','跳转页面')
  },
}
</script>

<style lang="less">
.wrapper {
  flex-direction: column;
}

.rh-item {
  padding: 0 30px;
  margin-bottom: 40px;
  align-items: center;
}

.rhi-image {
  width: 115px;
  height: 162px;
  border-radius: 6px;
}

.rhi-info {
  flex-direction: column;
  width: 355px;
  flex-grow: 0;
  margin-left: 20px;
}

.rhi-info-name {
  height: 34px;
  font-size: 34px;
  color: #333333;
  lines: 1;
  text-overflow: ellipsis;
}

.rhi-info-title {
  height: 24px;
  font-size: 24px;
  color: #adadad;
  margin-top: 40px;
  lines: 1;
  text-overflow: ellipsis;
}

.rhi-button {
  width: 180px;
  height: 60px;
  background-color: #f4f5f7;
  border-radius: 30px;
  margin-left: 20px;
  color: #f11212;
  text-align: center;
  font-size: 28px;
}

.load-more {
    width: 100%;
    align-items: center;
    justify-content: center;
    .no-more {
        height: 50px;
        font-size: 24px;
        font-weight: 500;
        text-align: center;
        color: #cccccc;
        line-height: 24px;
    }
}
</style>
