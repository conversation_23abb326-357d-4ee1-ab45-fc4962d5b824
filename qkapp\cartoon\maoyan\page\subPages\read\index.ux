<import name="header" src="../../components/header"></import>
<import name="back-app-button" src="../../components/back-app-button/index.ux"></import>
<import name="complaint-icon" src="../../components/complaint-icon/index.ux"></import><!-- 投诉按钮 -->
<import name="exit-icon" src="../../components/exit-icon/index.ux"></import>

<import name="custom-pop-group" src="../../cy-sdk/action/components/custom-pop-group/index.ux"></import>

<template>
  <stack id="stack">
  <div class="stack-wrapper">
    <div class="native-boost-group">
      <custom-pop-group
        onevent-watch="nativeBoostGroupEventDispatchHandler"
        onappear="popComponmentAppearHandler"
      ></custom-pop-group>
    </div>

    <header title="{{pageTitle}}" @back-click="backIconHandle"></header>
    <list id="list" class="book-content" @scroll="onContentScroll">
      <list-item show="{{!!content}}" type="content" for="{{content}}">
        <image class="content-img" src="{{$item}}"></image>
      </list-item>
      <list-item type="bottom" style="width:100%;height:120px;"></list-item>
    </list>
    <div class="action-wrapper">
      <div class="action-main">
        <div class="btn-wrapper" @click="processBtnClick('prev')">
          <image src="../../assets/v100/prev.png" style="margin-left: 26px;"></image>
        </div>
        <text @click="processBtnClick('catalog')">目录</text>
        <div class="btn-wrapper" @click="processBtnClick('next')">
          <image src="../../assets/v100/next.png" style="margin-right: 26px;margin-left:auto;"></image>
        </div>
      </div>
      <image class="detail-img" src="../../assets/v100/detail-icon.png" @click="processBtnClick('detail')"></image>
    </div>
    <!-- 返回腾讯系App的button -->
    <block if="{{computedIsBackAppButtonVisible}}">
      <back-app-button
        btn-text="{{back_name}}"
        back-url="{{back_url}}"
        package-name="{{back_pkg}}"
        report-pag-name="{{pageDetail.pageName}}"
      ></back-app-button>
    </block>
    <!-- 百度返回button -->
    <block if="{{computedIsBackAppButtonVisibleBd}}">
      <back-app-button-bd
        btn-text="{{back_name}}"
        back-url="{{back_url}}"
        back-report="{{back_report}}"
      ></back-app-button-bd>
    </block>
    <complaint-icon if="{{tacticsStatus}}" top="250"></complaint-icon>
    <exit-icon if="{{isShowQuitButton}}"></exit-icon>
  </div>
  </stack>
</template>

<script>
import { getBookContent } from '../../assets/data/book-content.js'
import { contentsData } from '../../assets/data/contents.js'
import { bookListData } from '../../assets/data/book-list.js'
import appUtils from '../../utils/app_page_utils.js'

export default pageMixin({
  public: {
    tacticsStatus: 0,
    isShowComplaintButton:false,
    isShowComplaintPop:false,
    isShowQuitButton: false,
    bookId: 1,
    bookTitle: '红楼梦',
    chapterId: 1,
    total: 10,
    chapterArr: [],
    intent: 0,
    back_name: '',
    back_url: '',
    back_pkg : '',
    back_report: '',
    computedIsBackAppButtonVisible: false,
    computedIsBackAppButtonVisibleBd: false,
  },
  private: {
    isShowBoostComp: true,
    pageDetail: {
      pageRoute: '/subPages/read',
      pageName: '阅读页',
      pageUrl: '阅读页',
      pageCode: 'page_read',
      pageOrigin: ""
    },
    isJump: false,
    pathUrl: '',

    content: '',
    chapterTitle: '',
    userSetting: {
      fontsize: 40,
      bgColor: '#EAEAEF',
      nightMode: false,
      fontFamily: 'default'
    },
    colors: ['#EAEAEF', '#FAF9DE', '#E3EDCD', '#DCE2F1', '#FFF2E2'],
    actionBtn: [
      { text: '上一话', img: 'prev.png', action: 'prev' },
      { text: '目录', img: 'catalog.png', action: 'catalog' },
      { text: '下一话', img: 'next.png', action: 'next' },
      { text: '详情', img: 'detail-icon.png', action: 'detail' }
    ],
    readLength: 0,
    timerId: [], // 计时器的所有id
    timerSetStatus: false,
    bookList: bookListData,
  },
  computed: {
    bgColor() {
      if (this.userSetting.nightMode) return 'rgb(29,29,31)'
      else return this.userSetting.bgColor
    },
    titleStyle() {
      return {
        fontFamily: this.userSetting.fontFamily,
        color: this.userSetting.nightMode ? 'rgb(126,129,134)' : '#000000'
      }
    },
    contentStyle() {
      return {
        fontFamily: this.userSetting.fontFamily,
        color: this.userSetting.nightMode ? 'rgb(126,129,134)' : '#000000',
        fontSize: this.userSetting.fontsize
      }
    },
    pageTitle() {
      return contentsData[this.bookId][Number(this.chapterId) - 1];
    }
  },
  async onInit() {
    try {
      await appUtils.page_ad_comp_init.call(this, true,'read');
    } catch (error) {
      console.log("@@@1",error);
    }
    // 如果从渠道进去则去应用配置取书的ID
    if (this.intent == 2) {
      this.bookId = sdk.tactics.getCustomParams('cartoon_id') ?? 1;
      this.chapterId = sdk.tactics.getCustomParams('chapter_id') ?? 1;
      console.log("@@@",this.bookId,this.chapterId);
    }
    
    // 参数传入时会变成string类型，需要转换类型
    this.chapterId = Number(this.chapterId)
    let book = this.bookList.find(item => item.id == this.bookId);
    this.chapterArr = book.chapterArr
    this.content = getBookContent(
      this.bookId,
      this.chapterId,
      this.chapterArr[this.chapterId - 1]
    )
    this.isShowComplaintButton = true
  },
  async onReady() {
    //获取用户自定义的样式,不存在自定义样式时使用默认样式
    const data = await $utils.getStorage('user-setting')
    if (!!data) this.userSetting = JSON.parse(data)
  },
    onHide() {
    this.isJump = true
    AD_UTILS.viewHideHandler()
  },
  onShow() {
    this.$element('stack')?.requestFullscreen({ screenOrientation: 'portrait' })
    this.switchChapterByCatalog()
    AD_UTILS.viewShowHandler()
    if (this.isJump) {
      this.isJump = false
    }
  },
  onDestroy() {
    if (this.$valid) {
      this.clearAllTimer();
    }
  },
  //接受自定义通用组件控制器派发的数据
  nativeBoostGroupEventDispatchHandler(evt) {
    LOG('VIEW', `nativeBoostGroupEventDispatchHandler======>`, evt)
    appUtils.native_boost_event_dispatch_handler.call(this,evt)
  },
  onBackPress() {
    return sdk.backPress.handleBackPress()
  },
  backIconHandle() {
    return sdk.backPress.handleBackPress()
  },
  /* -------------------SelfCustomEvent------------------ */
  onContentScroll(evt) {
    if (evt.scrollY == 0 || this.timerSetStatus) return;
    this.timerSetStatus = true;
    this.setUnlockTimer();
  },
  // 设置时间解锁弹窗计时器
  setUnlockTimer() {
    let timerStr = sdk.tactics.getCustomParams('read_unlock_timer');
    if (!timerStr || timerStr == '0') return;
    timerStr.split(',').forEach(ele => {
      let timer = setTimeout(() => {
        POP_TOOLS.commonFunc2PatchPopShow({ actionCode: 'READ_UNLOCK_POP', code: '', customAction: {} })
      }, ele * 1000);
      this.timerId.push(timer);
    });
  },
  clearAllTimer() {
    this.timerId.forEach((element) => {
      clearTimeout(element);
    });
    this.timerId = [];
  },
  onImageError(index) {
    LOG('onImage Error', index)
    this.content.splice(index, 1);
  },
  async switchChapter(type) {
    if (!this.content) return;
    if (type === 'next' && this.chapterId == this.total) {
      return $utils.showToast('已经是最后一章了')
    }
    if (type === 'prev' && this.chapterId == 1) {
      return $utils.showToast('已经是第一章了')
    }
    if (type === 'next') {
      POP_TOOLS.commonFunc2PatchPopShow({ actionCode: 'READ_NEXT_CHAPTER', code: '', customAction: {} })
    }
    // 调用接口，用bookId和chapterId查询下一页的内容，更新content和title。以下代码为模拟翻页。
    this.chapterId = type === 'next' ? this.chapterId + 1 : this.chapterId - 1
    this.content = getBookContent(
      this.bookId,
      this.chapterId,
      this.chapterArr[this.chapterId - 1]
    )
    this.$element('list').scrollTo({ index: 0 })
    if (type === 'next') {
      this.readLength ++;
    }

    this.clearAllTimer();
    this.timerSetStatus = false;
    
    COMMON_REPORT_UTILS.page_click_report(
      type === 'next' ? '下一话' : '上一话', '', `${this.bookId}`, `${this.chapterId}`, '', '', this.readLength
    );
  },
  switchChapterByCatalog() {
    if (!this.$app.$def.selectChapter) return
    this.chapterId = this.$app.$def.selectChapter
    this.$app.$def.selectChapter = ''
    this.content = getBookContent(
      this.bookId,
      this.chapterId,
      this.chapterArr[this.chapterId - 1]
    )
    this.$element('list').scrollTo({ index: 0 })
    POP_TOOLS.commonFunc2PatchPopShow({ actionCode: 'READ_NEXT_CHAPTER', code: '', customAction: {} })
    this.clearAllTimer();
    this.timerSetStatus = false;
  },
  goToContents() {
    COMMON_REPORT_UTILS.page_click_report('目录', '', `${this.bookId}`, `${this.chapterId}`);
    $utils.routetheUrl('subPages/contents', {
      bookId: this.bookId,
      bookTitle: this.bookTitle,
      chapterArr: this.chapterArr
    })
  },
  processBtnClick(type) {
    if (!$utils.dom_click_vali_shake(`processBtnClick_${type}${this.__id__}`, 1000))
      return
    switch (type) {
      case 'prev':
      case 'next':
        this.switchChapter(type)
        break
      case 'catalog':
        this.goToContents()
        break
      case 'detail':
        this.detailClick();
        break
      default:
        break
    }
  },
  detailClick() {
    COMMON_REPORT_UTILS.page_click_report('详情', '', `${this.bookId}`, `${this.chapterId}`);
    let timer = setTimeout(() => {
      clearTimeout(timer);
      if ($utils.stacksRouter('/subPages/book-detail')) {
        $utils.goBack();
      } else {
        $utils.routetheUrl('/subPages/book-detail', {bookId: this.bookId});
      }
    }, 100);
  },

})
</script>

<style lang="less">
@import '../../assets/styles/index.less';
.stack-wrapper {
  width: 100%;
  height: 100%;
  flex-direction: column;
}
.book-content {
  width: 100%;
  flex: 1;
  background-color: #eaeaef;

  .flex-box-mixins(column, flex-start, flex-start);
  .book-title {
    .title;
    margin: 60px 0 @app-padding 0;
  }
  .g22kjdgy {
    color: #ffffff;
  }
  .book-text {
    margin: @gap-3 0;
  }
}
.btn-bottom {
  .btn-primary;
  flex: 1;
  text-align: center;
}
.btn-margin {
  margin: 0 @app-padding;
}
.btn-bottom-disabled {
  .btn-disabled;
}
.pop-layer {
  .page-column;
  .header {
    .flex-box-mixins(row, flex-start, center);
    width: 100%;
    height: 30 * @size-factor;
    background-color: @layer-bg;
    padding: 0 @app-padding;
    image {
      width: 10 * @size-factor;
      height: 10 * @size-factor;
      margin-right: @app-padding;
    }
    text {
      font-size: 8 * @size-factor;
      color: @white;
    }
  }
  .footer {
    padding-top: @gap-3;
    .flex-box-mixins(column, flex-start, center);
    width: 100%;
    background-color: @layer-bg;
    .font-size-change {
      width: 90%;
      padding: @gap-1 @app-padding;
      .flex-box-mixins(row, space-between, center);
      .btn {
        font-size: 10 * @size-factor;
        color: @white;
      }
      .btn:disabled {
        opacity: 0.5;
      }
      .size {
        font-size: 8 * @size-factor;
        color: @white;
      }
    }
    .bg-color-change {
      width: 90%;
      padding: @gap-1 @app-padding;
      .flex-box-mixins(row, space-between, center);
      .color-item {
        width: 20 * @size-factor;
        height: 10 * @size-factor;
        border-radius: 8px;
      }
      .selected {
        border: 5px solid @brand;
      }
    }

    .font-change {
      width: 90%;
      padding: @gap-1 @app-padding;
      .flex-box-mixins(row, space-between, center);
      flex-wrap: wrap;
      .font-item {
        .btn-primary;
        color: @grey;
        border-color: @grey;
        width: 45%;
        margin-bottom: 2 * @size-factor;
      }
      .selected {
        color: @brand;
        border-color: @brand;
        border-width: 3px;
      }
    }
  }
  .middle {
    width: 100%;
    height: 100%;
    flex: 1;
    .float-btn {
      position: absolute;
      right: 10 * @size-factor;
      .flex-box-mixins(row, center, center);
      width: 20 * @size-factor;
      height: 20 * @size-factor;
      background-color: @layer-bg;
      border-radius: 100%;
      image {
        width: 10 * @size-factor;
        height: 10 * @size-factor;
      }
    }
  }
}

.content-img {
  width: 100%;
}

.action-wrapper {
  position: absolute;
  width: 100%;
  height: 120px;
  bottom: 0;
  left: 0;
  background-color: @white;

  .action-main {
    width: 307px;
    height: 74px;
    background-color: #f5f5f5;
    border-radius: 37px;
    margin-top: 12px;
    justify-content: center;
    align-items: center;
    margin-left: 21px;

    image {
      width: 34px;
      height: 34px;
    }

    text {
      height: 100%;
      color: @text-black;
      font-size: 30px;
      font-weight: bold;
      flex: 1;
      text-align: center;
    }

    .btn-wrapper {
      flex: 1;
      height: 100%;
      align-items: center;
    }
  }

  .detail-img {
    width: 58px;
    height: 58px;
    margin-top: 20px;
    margin-left: 298px;
  }

}
  .banner-view-class {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}

.native-boost-group{
  position: absolute;
  top: 0px;
  left: 0px;
  width: 750px;
  height: 100%;
}
</style>
