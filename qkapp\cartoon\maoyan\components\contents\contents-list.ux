<template>
  <list class="chapter-list">
    <list-item
      class="chapter-list-item"
      type="chapter"
      for="{{list}}"
      style="margin-top:{{$idx==0?0:30}}px;"
      @click="detail($idx)"
    >
      <image src="{{$item}}"></image>
      <text>{{ chapterTitle[$idx] }}</text>
    </list-item>
    <list-item type="bottom" style="width:100%;height:174px;"></list-item>
  </list>
</template>

<script>
export default {
  props: {
    list: {},
    chapterTitle: {
      default: []
    }
  },
  /* -------------------SelfCustomEvent------------------ */
  detail(id) {
    this.$emit('detail', { id })
  }
}
</script>

<style lang="less">
@import '../../assets/styles/index.less';

.chapter-list {
  flex: 1;
  margin-top: 30px;
  width: 100%;
  padding: 0 @app-padding;
}

.chapter-list-item {
  align-items: center;
  height: 114px;

  image {
    width: 192px;
    height: 114px;
    border-radius: 4px;
    margin-right: 20px;
  }

  text {
    height: 30px;
    color: #333333;
    font-size: 30px;
  }
}
</style>
