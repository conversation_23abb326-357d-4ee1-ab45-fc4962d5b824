<template>
  <!-- template里只能有一个根节点 -->
  <div style="width: 100%; flex: 1">
    <div class="native-boost-group boots-hide">
      <custom-pop-group
        if="{{isShowBoostComp}}"
        onevent-watch="nativeBoostGroupEventDispatchHandler"
        onappear="popComponmentAppearHandler"
      ></custom-pop-group>
    </div>
    <tabs index="{{selectIndex}}" onchange="ydReadTabChangeHandler" style="width: 100%; flex: 1;background-color:#ffffff;">
      <tab-content scrollable="{{false}}" style="flex:1;">
        <Shelf
          select-index="{{mySelectIndex}}"
          view-show-times="{{viewShowTimes}}"
          shelf-guide-second="{{shelfGuide2}}"
          @book-list-handle="{{loadShelfGuide}}"
          is-show-add-desktop="{{isShowAddDesktop}}"
          add-desktop-height="{{addDesktopHeight}}"
          @page-jump-handle="{{pageJumpHandle}}"
        ></Shelf>
        <Store
          id="store"
          select-index="{{mySelectIndex}}"
          view-show-times="{{viewShowTimes}}"
          @show-interesting="{{processInterestDialog}}"
          is-show-add-desktop="{{isShowAddDesktop}}"
          add-desktop-height="{{addDesktopHeight}}"
          is-keep-read="{{lastReadBookInfo ? 1 : 0}}"
          @plus-view-show-times="plusViewShowTimes"
          @page-jump-handle="{{pageJumpHandle}}"
        >
        </Store>
        <div></div>
        <Classify
          select-index="{{mySelectIndex}}"
          view-show-times="{{viewShowTimes}}"
          is-show-add-desktop="{{isShowAddDesktop}}"
          add-desktop-height="{{addDesktopHeight}}"
          @page-jump-handle="{{pageJumpHandle}}"
        ></Classify>
        <User
          select-index="{{mySelectIndex}}"
          view-show-times="{{viewShowTimes}}"
          is-show-add-desktop="{{isShowAddDesktop}}"
          add-desktop-height="{{addDesktopHeight}}"
          @page-jump-handle="{{pageJumpHandle}}"
        ></User>
      </tab-content>
      <tab-bar mode="fixed" class="tab-bar-wrapper">
        <div class="{{$idx == 2 ? 'tab-item-2' : 'tab-item'}}" for="tabList">
           <block if="{{mySelectIndex == $idx && mySelectIndex != 2}}">
            <block>
              <lottie id="tab-{{$idx}}" class="tab-lottie" source="../../lottie/main-{{$idx}}.json" autoplay={{false}} loop="{{false}}"></lottie>
            </block>
          </block> 
          <block else>
            <image
              class="{{$idx == 2 ? 'tab-icon-2' : 'tab-icon'}}"
              src="../images/main/tab-{{$idx}}-normal.png"
            ></image>
          </block>
          <text
            class="tab-title {{mySelectIndex == $idx ? 'change-tab-color' : 'normal-tab-color'}}"
            >{{ tabList[$idx] }}</text
          >
        </div>
      </tab-bar>
    </tabs>
    <image src="../images/main/tab-bar.png" style="position:absolute;width:750px;height:29px;left:0;bottom:100px;"></image>
    <keep-read-banner
      if="{{lastReadBookInfo && mySelectIndex == 1}}"
      add-desktop="{{isShowAddDesktop ? 1 : 0}}"
      book-info="{{lastReadBookInfo}}"
      onkeep-read="keepReadClick"
      desktop-height="{{addDesktopHeight}}"
    ></keep-read-banner>
    <!-- 投诉悬浮按钮 -->
    <complaint-icon if="{{tacticsStatus}}" top='200'></complaint-icon>
    <!-- 加桌悬浮条 -->
    <div
      class="add-desktop-container {{addDesktopAnimationName}}"
      style="height:{{addDesktopHeight}}px;"
      if="{{isShowAddDesktop}}"
    >
      <add-desktop-banner
        onevent-watch="addDeskTopEventHandle"
      ></add-desktop-banner>
    </div>
    <!-- 返回腾讯系App的button -->
      <back-app-button
        if="{{computedIsBackAppButtonVisible}}"
        btn-text="{{back_name}}"
        back-url="{{back_url}}"
        package-name="{{back_pkg}}"
      ></back-app-button>
    <!-- 兴趣爱好弹窗 -->
    <interest-settings if="{{isShowDialog}}" @close-dialog="closeDialog"></interest-settings>
    <!-- 投诉弹窗 -->
    <!-- 退出应用按钮 -->
    <exit-icon if="{{isShowExIcon}}"></exit-icon>
    <!-- 书架页引导 -->
    <shelf-guide if="{{shelfGuideCanShow && shelfBook !== '' && selectIndex == 0}}" 
      book="{{shelfBook}}" @close-guide="{{closeGuide}}" 
    ></shelf-guide>
    <my-toast if="{{customToastShow}}" toast-text="{{customToastText}}"></my-toast>
  </div>
</template>
<import name="Shelf" src="../Shelf/index"></import>
<import name="Store" src="../Store/index"></import>
<import name="Classify" src="../Classify/index"></import>
<import name="User" src="../User/index"></import>
<import name="back-app-button" src="../../components/back-app-button"></import>
<import name="add-desktop-banner" src="../../components/add-desktop-banner/index.ux"></import>
<import name="keep-read-banner" src="../components/keep-read-banner/index.ux"></import>
<import name="interest-settings" src="../../components/interest-settings"></import>
<import name="shelf-guide" src="../components/shelf-guide"></import>
<import name="my-toast" src="../../components/my-toast"></import>
<import name="exit-icon" src="../../components/exit-icon/index.ux"></import><!-- 投诉按钮 -->
<import name="complaint-icon" src="../../components/complaint-icon/index.ux"></import>
<import name="custom-pop-group" src="../../cy-sdk/action/components/custom-pop-group/index.ux"></import>

<script>
import shortcut from '@system.shortcut'
import { PAGE_INIT } from '../../cy-sdk/constant/events'
import appUtils from '../../utils/app_page_utils.js'

export default pageMixin({
  data: {
    pageName: '首页',
    tabList: ['书架', '书城', '福利', '分类', '我的'],
    isHomeLoad: false,
    isShowBoostComp: true,
    isCheckBackHandler: false,
    isShowPreloadImg: false, //是否开启图片预加载组件
    showCustomFill: false,
    boostTimer: null,
    nativeBoostGroupController: {popShow: false},
    backPressRefuseReport: false,//物理返回 初始化拦截上报状态
    pageDetail: {
      pageRoute: '/pagesA/Main?selectIndex=1',
      pageType: '一级页面',
      pageName: '书城页',
      pageUrl: '书城页',
      pageCode: 'READ_MAIN',  //READ_WELFARE READ_MAIN
      pageOrigin: 'READ_READINFO'
    },

    isDeleteBookItemDialogShow: 1,//是否书架页的删除弹窗显示 1 未显示 2 显示
    isJump: false, //页面是否跳转push
    isDeleteShow: false, //书架页删除弹窗
    isIcon: true, //是否加桌
    isCloseIcon: false, //是否关闭过加桌横幅
    lastReadBookInfo: null, //继续阅读
    isShowDeskFloat: false,
    pathUrl: "",
    mySelectIndex: 1,
    selectIndex: 1,
    viewShowTimes: 0,
    pushId: null,
    isNeedLogin:false,
    intent:0,
    isShowDialog: false,
    addDesktopHeight:80,
    addDesktopAnimationName: '',
    statusBarHeight: 0,
    shelfBook: '',
    shelfGuide2: false,
    shelfGuideCanShow: false,
    tacticsStatus:0, //策略是否获取完毕
    customToastText: '',
    customToastShow: false,
    setPageBackComplete:false,
    showAddDesktopPop: false,
    tabTimer: '',
    isShowComplaintPop: false,//投诉弹窗
    stackReadyFlag: 0,
    isShowExIcon: false,
    back_name: '', // 广点通 流量App名称
    back_url: '', // 广点通 流量App schema
    back_pkg: '', // 广点通 流量App包名
    computedIsBackAppButtonVisible: true,
  },


  async onInit() {
    let that = this
    that.$app.$def.tabListType = [0, 0, 0, 0, 0]
    await appUtils.page_ad_comp_init.call(this, true, 'main')

    that.selectIndex = Number(that.$page.query.selectIndex??that.selectIndex)
    that.mySelectIndex = Number(that.selectIndex) == 2 ? 1 : Number(that.selectIndex)
    sdk.tactics.setQuery(`selectIndex=${that.selectIndex}`)
    POP_TOOLS.commonFunc2PatchPopShow({actionCode: "PAGE_ENTRY", code: '', customAction:{}})

    that.stackReadyFlag++
    if (Number(sdk.tactics.getCustomParams('is_show_exit_icon'))) {
      that.isShowExIcon = true
    }
    // 应用配置是否加桌
    that.isShowDeskFloat = Number(sdk.tactics.getCustomParams('is_show_desk_float')) == 1 ? true : false

    that.addDesktopHeight = Number(sdk.tactics.getCustomParams('add_desktop_height'))
    that.getLastReadBook()
    try {
      let animationType = sdk.tactics.getCustomParams('add_desktop_animation')
      if (animationType == '1') this.addDesktopAnimationName = 'scale-up-down-center'
      if (animationType == '2') this.addDesktopAnimationName = 'bounce-bottom'
    } catch (error) {

    }

    that.shelfGuideCanShow = !(await $utils.getStorage('shelf_guide_has_show')) // 从storage取是否展示过书架页引导
  },
  onReady(){
    this.stackReadyFlag++
  },
  commonGetConfig(key) {
    let getValue = ''
    try {
        getValue = sdk.tactics.getCustomParams(key)
    } catch (error) {

    }
    return getValue
  },
  onShow() {
    // 如果不是第一次show 全屏flag加加
    let that = this
    if (this.viewShowTimes != 0) this.stackReadyFlag++
    // 广告跳转回应用 后续操作
    AD_UTILS.viewShowHandler()
    
    if (that.mySelectIndex == 2) {
      that.mySelectIndex = 1
      that.selectIndex = 1
      LOG('onShow=======================>', that.mySelectIndex, that.selectIndex)
    }
    // sdk.tactics.setQuery(`selectIndex=${this.selectIndex}`)
    POP_TOOLS.changeCurrentView(this)
    if (that.mySelectIndex !== 1 && that.viewShowTimes == 0) {
      that.viewShowTimes += 2
    } else {
      that.viewShowTimes++
    }
    //onshow 是否为跳转触发
    if (that.isJump) {
      that.isJump = false
      that.getLastReadBook()
    }
    if (that.isNeedLogin) {
      that.isNeedLogin = false
      that.$app.$def.login()
    }
    that.install()
    this.checkShowInterestDialog()
    
  },

  async checkShowInterestDialog() {
    if (
      this.mySelectIndex == 1
      && this.$app.$def.pageCodeInfo.pageOrigin == 'READ_BOOKINFO'
      && !this.$app.$def.interestHasShow
    ) {
      let res = await $apis.example.getHasInterest()
      if (res.code == 200) {
        this.$app.$def.interestHasShow = true
        if (res.data.have_interest == -1) this.isShowDialog = true
      }
    }
  },

  async processInterestDialog() {
    if (this.isShowDialog) return
    this.isShowDialog = true
  },

  closeDialog() {
    this.isShowDialog = false
  },

  ydReadTabChangeHandler(evt) {
    LOG('ydReadTabChangeHandler---------', this.mySelectIndex, evt);
    clearTimeout(this.tabTimer) 
    this.tabTimer = setTimeout(() => {
      this.$element(`tab-${evt.index}`) && this.$element(`tab-${evt.index}`).play()
    }, 50)
    if (this.mySelectIndex == evt.index) return
    // tab点击 埋点
    this.uploadMainPageClick(evt.index)
    if (evt.index == 2) {
      this.mySelectIndex = evt.index
      this.selectIndex = this.mySelectIndex
      this.isJump = true
      $utils.routetheUrl('/pagesA/WelfareNew', { pathUrl: 'tabbar福利按钮' }, false)
      return
    }
    if (this.redPacketTimer) {//清除倒计时计时器
      clearInterval(this.redPacketTimer)
      this.redPacketTimer = null
    }

    this.mySelectIndex = evt.index
    this.selectIndex = this.mySelectIndex
    this.isDeleteShow = !this.isDeleteShow
    console.log('ydReadTabChangeHandler,this.selectIndex',this.selectIndex)
    sdk.tactics.setQuery(`selectIndex=${this.selectIndex}`)
    sdk.emitter.emit(PAGE_INIT, this)
    POP_TOOLS.commonFunc2PatchPopShow({actionCode: "PAGE_ENTRY", code: '', customAction:{}})
    switch (evt.index) {
      case 0:
        this.$page.setStatusBar({ textStyle: 'dark' })
        break;
      case 1:
        this.$page.setStatusBar({ textStyle: 'dark' })
        break;
      case 3:
        this.$page.setStatusBar({ textStyle: 'dark' })
        break;
      case 4:
        this.$page.setStatusBar({ textStyle: 'dark' })
        break;
      default:
        break;
    }
  },
  uploadMainPageClick(index) {
    let elementName = ''
    switch (index) {
      case 0:
        elementName = "书架tab"
        break
      case 1:
        elementName = "书城tab"
        break
      case 2:
        elementName = "福利tab"
        break
      case 3:
        elementName = "分类tab"
        break
      case 4:
        elementName = "我的tab"
        break
    }
    COMMON_REPORT_UTILS.page_click_report(elementName) //点击上报
  },
  computed: {
    isShowAddDesktop() {
      return !this.isIcon && this.isDeleteBookItemDialogShow == 1 && !this.isCloseIcon && this.isShowDeskFloat
    }
  },
  updatePageUrl2Sdk(index) {
    let _page_name = ''
    switch (index) {
      case 0:
        _page_name = '书架页'
        break;
      case 1:
        _page_name = '书城页'
        break;
      case 2:
        _page_name = '福利页'
        break;
      case 3:
        _page_name = '我的页'
        break;
      default:
        _page_name = '首页'
        break;
    }
    switch (Number(index)) {
      case 0:
        // 书架
        this.pageDetail.pageCode = 'READ_SHELF'
        this.pageDetail.pageRoute = '/pagesA/Main?selectIndex=0'
        this.pageDetail.pageName = '书架页'
        this.pageDetail.pageUrl = '书架页'
        break
      case 1:
        // 书城
        this.pageDetail.pageCode = 'READ_MAIN'
        this.pageDetail.pageRoute = '/pagesA/Main?selectIndex=1'
        this.pageDetail.pageName = '书城页'
        this.pageDetail.pageUrl = '书城页'
        break
      case 2:
        // 福利
        this.pageDetail.pageCode = 'READ_WELFARE'
        this.pageDetail.pageRoute = '/pagesA/WelfareNew'
        this.pageDetail.pageName = '福利页'
        this.pageDetail.pageUrl = '福利页'
        break
      case 3:
        // 我的
        this.pageDetail.pageCode = 'READ_CLASSIFY'
        this.pageDetail.pageRoute = '/pagesA/Main?selectIndex=3'
        this.pageDetail.pageName = '分类页'
        this.pageDetail.pageUrl = '分类页'
        break
      case 4:
        // 我的
        this.pageDetail.pageCode = 'READ_MINE'
        this.pageDetail.pageRoute = '/pagesA/Main?selectIndex=4'
        this.pageDetail.pageName = '我的页'
        this.pageDetail.pageUrl = '我的页'
        break
    }
    return _page_name
  },
  checkAdUtilsFunc(arg1, arg2, arg3) {
    LOG(`${arg1}_${arg2}_${arg3}_${this.mySelectIndex}`)
  },
  // exitback-pop end
  // 获取加桌状态
  install() {
    var that = this
    let timer = setTimeout(() => {
      shortcut.hasInstalled({
        success: function (ret) {
          that.isIcon = ret
        }
      })
      clearTimeout(timer);
    }, 500)
  },
  /**
   * 加桌组件事件
   */
  addDeskTopEventHandle(evt) {
    switch (evt.detail.eventName) {
      case 'addDesktopRes':
        this.addDesktopDataHandle(evt.detail.isAddSuccess, evt.detail.errMsg)
        break
      case 'addDesktopClick':
        this.addDesktop()
        break
      case 'addDesktopClose':
        this.isCloseIcon = true
        break
    }
  },
  addDesktopDataHandle(isAdd, code = '未知') {
    COMMON_REPORT_UTILS.page_click_report(`一键加桌`) //点击上报
    if (isAdd) {
      $apis.example.addDeskTopInfo({}).then(res => { })
      this.$app.$def.addDeskTopStatus = 1
      this.$app.$def.setAddDesktopInfo(3)
      COMMON_REPORT_UTILS.add_icon_report('成功', '悬浮横幅', this.pathUrl)
    } else {
      COMMON_REPORT_UTILS.add_icon_report(`失败-${code}`, '悬浮横幅', this.pathUrl)
    }
  },
  // 默认加桌方式
  addDesktop() {
    COMMON_REPORT_UTILS.page_click_report(`加桌`)
    let that = this
    that.$app.$def.addDeskTopTimes++
    shortcut.install({
      success: function () {
        that.addDesktopDataHandle(true)
      },
      fail: function (data, code) {
        that.addDesktopDataHandle(false, code)
      }
    })
  },
  async getLastReadBook() {
    let is_show_last_read_banner = Number(sdk.tactics.getCustomParams('is_show_last_read_banner'))
    LOG('getLastReadBook======>', this.$app.$def.lastReadStatus, is_show_last_read_banner)
    if (this.$app.$def.lastReadStatus == 2 || !is_show_last_read_banner) return
    let res = await $apis.example.readHistoryApi({ page: 1, page_size: 1 })
    if (res.code == 200) {
      if (res.data.today && res.data.today.length > 0) {
        this.lastReadBookInfo = res.data.today[0]
      } else if (res.data.ago.list && res.data.ago.list.length > 0) {
        this.lastReadBookInfo = res.data.ago.list[0]
      }
      this.$app.$def.lastReadStatus = 1
      COMMON_REPORT_UTILS.pop_show_report('继续阅读', '无', 1, this.pathUrl, "", `${this.lastReadBookInfo.bookId}`)
    } else {
      this.$app.$def.lastReadStatus = 2
    }
  },
  keepReadClick(evt) {
    LOG('keepReadClick=========>', evt)
    if (evt.detail.eventName == 'keepRead') {
      COMMON_REPORT_UTILS.pop_click_report('继续阅读', '', 1, `${this.lastReadBookInfo.bookId}`, '小说', '')
      var params = {
        bookId: this.lastReadBookInfo.bid,
        chapterId: this.lastReadBookInfo.chapter.zid,
        pathUrl: "书城页"
      }
      $utils.routetheUrl('/pagesC/Read', params, false)
    } else {
      COMMON_REPORT_UTILS.pop_click_report('继续阅读', '', 1, `${this.lastReadBookInfo.bookId}`, '关闭', '')
    }
    this.$app.$def.lastReadStatus = 2
    this.lastReadBookInfo = null
  },
  onHide() {
    AD_UTILS.viewHideHandler()

    LOG('main-hide===========>')
  },
  onBackPress() {
    console.log('onBackPress')
    if(this.isShowComplaintPop){
       this.isShowComplaintPop=false
       return  true
    }
    return sdk.backPress.handleBackPress()
  },
  backPressHandler() {
    let that = this
    if (that.isShowDialog) {
      that.isShowDialog = false
      return true
    }
  },


  goBack() {
    this.isExitDialog = false
    this.$app.exit()
  },
  goCancel() {
    this.isExitDialog = false
  },
  // 页面销毁
  onDestroy() {
  },


  addBoostComp() {
  },
  /**
   * 接受自定义通用组件控制器派发的数据
   */
  nativeBoostGroupEventDispatchHandler(evt) {
    let that = this
    LOG('VIEW', `nativeBoostGroupEventDispatchHandler======>`, evt)
    const { action, actionName, advertiser, eventName, popData, isExitKey } = evt.detail
    const { popConfig, advConfig } = popData || {}
    const { actionCode, customAction, popKey, actionConfig } = popConfig || {}
    switch (evt.detail.eventName) {
      case 'noBoostComp4Use': //没有可使用的补量组件
        sdk.pop.adShowErrorHandler({
            actionCode: actionCode,
            code: actionConfig.triggerAdComponent,
            customAction: customAction
          })
      break
      case 'noPopCode':
        POP_TOOLS.commonFunc2PatchPopShow({actionCode: "PAGE_BACK", code: '', customAction:{}})
        break
      case 'refresh':

       POP_TOOLS.commonFunc2PatchPopShow({
          actionCode: actionCode,
          code: actionCode == 'POPUP_CLOSE' ? actionConfig.triggerAdComponent : '',
          customAction: { popType: 'refresh' }
        })
        break
      case 'setPageBackComplete':
        try {
          LOG('CUSTOM', 'pageBackCompleteHandler', that.setPageBackComplete)
          LOG('v207', '返回场景预加载结束')
          if (that.setPageBackComplete) {
          } else {
            that.setPageBackComplete = true
          }
        } catch (error) {
          LOG('CUSTOM', '返回场景广告获取结束', error)
        }
        break
      case 'closePopData':
        sdk.pop.eventDispatch(JSON.parse(JSON.stringify(evt.detail)), { ...that.pageDetail }, that.$app.$def)
        break
      case 'popShow':

        if (Number(this.commonGetConfig('is_show_exit_icon'))===1) {
          console.log('is_show_exit_icon1111')
          that.isShowQuitButton = false
          setTimeout(() => {
            that.isShowQuitButton = true
          }, 1500)
        }
        break
      case 'patchPopkey':
        if (Number(this.commonGetConfig('is_show_exit_icon'))===1) {
          that.isShowQuitButton = false
          setTimeout(() => {
            that.isShowQuitButton = true
          }, 1500)
        }
        break
    }
  },

  loadShelfGuide(evt) {
    switch (evt.detail.eventName) {
      case 'guideFirst':
        this.shelfBook = evt.detail && evt.detail.bookInfo
        $utils.setStorage('shelf_guide_has_show', true) // 从storage取是否展示过书架页引导
        break;
      case 'closeGuideSecond':
        this.shelfGuide2 = false
        break;
      default:
        break;
    }

  },
  closeGuide(evt) {
    if (this.shelfBook !== '') this.shelfBook = ''
    this.shelfGuideCanShow = false
    if (!this.shelfGuide2) this.shelfGuide2 = true
  },
  showMyToast(content, duration = 2500) {
    if (this.customToastShow) return
    this.customToastText = content
    this.customToastShow = true
    let timer = setTimeout(() => {
      this.customToastShow = false
      this.customToastText = ''
      clearTimeout(timer)
    }, duration);
  },
  plusViewShowTimes() {
    this.viewShowTimes++
  },
  pageJumpHandle(evt) {
    this.isJump = true
    $utils.routetheUrl(evt.detail.pageUrl, evt.detail.pageData)
  },
  stackReadyFlagHandler(newVal, oldVal) {
    if (newVal >= 2) {
      this.hideMenuBar()
    }
  },
  hideMenuBar() {
    
  }
})
</script>

<style lang="less">
@import '../../assets/styles/style.less';
@import './index.less';
@import '../../assets/styles/animation.less';
</style>
