######## 文件格式 ########
# 获取暂存区中的所有文件
echo "开始检查文件路径"
files=$(git diff --cached --name-only --diff-filter=ACMR)

# 定义允许的文件名格式：小写字母和中划线
regex='^[a-z0-9\-]+$'

# 遍历所有文件，检查文件名格式
for file in $files; do
  # 获取文件名的最后部分（去掉路径和扩展名）
  filename=$(basename "$file" | sed 's/\.[^.]*$//')

  # 如果文件名不符合规则，退出并提示错误
  if ! [[ $filename =~ $regex ]]; then
    echo "❌ Error: 文件 '$filename' 不符合规则."
    echo "   - 文件名只能包含小写字母和中划线."
    exit 1
  fi
done

echo "✅ All filenames are valid."

######## eslint ########
npx lint-staged
