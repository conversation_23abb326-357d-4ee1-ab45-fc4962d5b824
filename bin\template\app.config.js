/**
 * 对接配置
 * 分为 单 APP 配置和 分厂商配置，如果两者都配置了同一个字段，则以app配置为准
 * @type {{app: {}, brand: {}}}
 */
const config = {
  // 系统数据
  app: {
    // 系统内部数据，都写在这里，之后会统一放在 $config 变量下
    // 支持dev和prod，如果一致，则不用写 dev prod，直接写一个即可

    // app 秘钥
    appSecreteKey: {
      dev: '',
      prod: ''
    },
    // 协议地址，V2为隐藏公司名称地址
    protocolAddress: {
      UserAgreement: '',
      PrivacyPolicy: ''
    },
    // 小说阅读页
    middleAltLight: '', // 中插白天背景图
    middleAltDark: '', // 中插黑夜背景图
    imageOss: '', // 阅读页图片OSS地址
    // 优量汇APP ID
    ylhAppId: '',
    videoAdUnitId: {
      xiaomi: '',
      hw: '',
      oppo: '',
      vivo: '',
      honor: ''
    }
  },

  // 打包过程的配置
  build: {
    // 项目打包过程，替换APP路径，eg '/page' ''
    copyPathReplace: ['', '']
  },

  // manifest 配置
  manifest: {},

  // 分厂商配置，如果有相同字段，则以分厂商配置为准
  brand: {
    xiaomi: {
      manifest: {}
    },
    hw: {
      manifest: {}
    },
    oppo: {
      manifest: {}
    },
    vivo: {
      manifest: {}
    },
    honor: {
      manifest: {}
    }
  }
}

module.exports = config
