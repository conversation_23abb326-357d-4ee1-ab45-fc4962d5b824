// ==================== 重新生成SDK ===========================
const { consoleSplit } = require('../../utils/console')
const fs = require('fs')
const path = require('path')
const { projectPath } = require('../constant')
const { execSync } = require('child_process')

function deleteSdk() {
  consoleSplit('删除sdk')
  const sdkPath = path.resolve(projectPath, 'node_modules', 'ad-sdk')

  if (fs.existsSync(sdkPath)) {
    fs.rmSync(sdkPath, { recursive: true })
  }

  console.log('删除文件 node_modules/ad-sdk 成功')
}

/**
 * 更新 package.json 中 devDependencies 的指定依赖地址并安装依赖
 * @param {string} packageName - 要更新的依赖名称
 * @param {string} newAddress - 新的依赖地址
 * @param {string} packageJsonPath - package.json 文件路径
 */
function updateDevDependency(packageName, newAddress) {
  try {
    consoleSplit('更新依赖')
    console.log(`Updated ${packageName} to ${newAddress} in package.json`)
    execSync(`npm install ${packageName}@${newAddress} --force`, { stdio: 'inherit' })
    consoleSplit('SDK更新成功')
  } catch (error) {
    console.error('更新依赖Error:', error)
  }
}

module.exports = { deleteSdk, updateDevDependency }
