const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')
const { projectPath } = require('../main/constant')
const { getStore, setStore } = require('../store')
function checkandCloneSDK(sdkVersion) {
  const sdkPath = path.resolve(projectPath, `./packages/ad-sdk/${sdkVersion}`)
  if (!fs.existsSync(sdkPath)) {
    execSync(`git clone -b ${sdkVersion} https://gitlab.ghfkj.cn/cy/fre/quickapp/ad-sdk.git ${sdkPath}`)
  } else {
    // 先恢复工作目录中的更改
    execSync(`cd ${sdkPath} && git restore .`)
    // 再拉取最新的代码
    execSync(`cd ${sdkPath} && git pull`)
  }

  checkSdkVersion(sdkVersion) // 检查sdk上报版本号是否正确
}

function checkSdkVersion(sdkVersion) {
  const selectedVersion = sdkVersion.replace(/^[^\d]+/, '') // 所选版本号
  let sdkVersionCode = '' // sdk代码里的版本号

  const sdkVersionPath = path.resolve(projectPath, `./packages/ad-sdk/${sdkVersion}/src/cy-sdk/index.js`)
  const fileContent = fs.readFileSync(sdkVersionPath, 'utf-8')

  const match = fileContent.match(/const\s+SDK_VERSION\s*=\s*['"]([^'"]+)['"]/)
  if (match) {
    sdkVersionCode = match[1]
  } else {
    // 匹配小米sdk
    const sdkConfig = require(
      path.resolve(projectPath, `./packages/ad-sdk/${sdkVersion}/scripts/config/cy/var-config.js`)
    )
    sdkVersionCode = sdkConfig.pro.SDK_VERSION_VALUE()
  }
  console.log('上报sdk版本号: ', sdkVersionCode)
  if (selectedVersion !== sdkVersionCode) {
    console.log('上报sdk版本号不匹配，请检查sdk代码')
    process.exit(0)
  }
}

function dependencyInstallerHandler(packageName, version, storeKey) {
  if (version && getStore(storeKey) !== version) {
    const installCommand = version.startsWith('git+')
      ? `npm install ${version}`
      : `npm install ${packageName}@${version}`

    execSync(installCommand)

    setStore(storeKey, version)
  }
}

function getYLHandBaiduV2(sdkVersion) {
  const packageJson = require(path.resolve(projectPath, `./packages/ad-sdk/${sdkVersion}/package.json`))
  const baiduVersion = packageJson.dependencies['union-quick-app-ad']
  const ylhVersion = packageJson.dependencies['ylh-quick-app-ad-sdk']

  dependencyInstallerHandler('union-quick-app-ad', baiduVersion, 'baiduVersion')

  dependencyInstallerHandler('ylh-quick-app-ad-sdk', ylhVersion, 'ylhVersion')

  return {
    baiduVersion,
    ylhVersion
  }
}

function getYLHandBaidu(sdkVersion) {
  const packageJson = require(path.resolve(projectPath, `./packages/ad-sdk/${sdkVersion}/package.json`))

  // 获取版本号
  const getVersion = dep => {
    if (!dep) {
      return {
        version: '',
        isClone: false
      }
    }
    let isClone = false // 是否使用clone方式
    isClone = dep.includes('https://gitlab.ghfkj.cn/cy/fre/quickapp/')
    return isClone ? { version: dep.split('#')[1], isClone } : { version: dep, isClone }
  }
  const baiduVersionInfo = getVersion(packageJson.dependencies['union-quick-app-ad'])
  const ylhVersionInfo = getVersion(packageJson.dependencies['ylh-quick-app-ad-sdk'])

  // 复制SDK工具函数
  const copySDK = (name, version, repoName) => {
    const srcPath = path.resolve(projectPath, `./packages/${name}/${version}`)
    const destPath = path.resolve(projectPath, `./node_modules/${repoName}`)

    if (!fs.existsSync(srcPath)) {
      execSync(`git clone -b ${version} https://gitlab.ghfkj.cn/cy/fre/quickapp/${repoName}.git ${srcPath}`)
    } else {
      // 先恢复工作目录中的更改
      execSync(`cd ${srcPath} && git restore .`)
      // 再拉取最新的代码
      execSync(`cd ${srcPath} && git pull`)
    }

    if (fs.existsSync(destPath)) {
      fs.rmSync(destPath, { recursive: true, force: true })
    }

    fs.cpSync(srcPath, destPath, { recursive: true })
    console.log(`复制成功：${srcPath} -> ${destPath}`)
  }

  // 复制百度SDK
  if (baiduVersionInfo.isClone) {
    copySDK('baidu', baiduVersionInfo.version, 'union-quick-app-ad')
    setStore('baiduVersion', baiduVersionInfo.version)
  } else {
    dependencyInstallerHandler('union-quick-app-ad', baiduVersionInfo.version, 'baiduVersion')
  }

  // 复制优量汇SDK
  if (ylhVersionInfo.isClone) {
    copySDK('ylh', ylhVersionInfo.version, 'ylh-quick-app-ad-sdk')
    setStore('ylhVersion', ylhVersionInfo.version)
  } else {
    dependencyInstallerHandler('ylh-quick-app-ad-sdk', ylhVersionInfo.version, 'ylhVersion')
  }

  return {
    baiduVersion: baiduVersionInfo.version,
    ylhVersion: ylhVersionInfo.version
  }
}

module.exports = {
  checkandCloneSDK,
  getYLHandBaidu,
  getYLHandBaiduV2
}
