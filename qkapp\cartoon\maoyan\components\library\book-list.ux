<import name="my-load-more" src="apex-ui/components/load-more/index"></import>

<template>
  <list class="list-container">
    <list-item type="custom">
      <slot></slot>
    </list-item>
    <list-item type="title" class="mg">
      <text class="list-title">98%的萌新都入坑了</text>
    </list-item>
    <list-item type="book-item" class="book-item mg" for="{{item in subList}}">
      <div
        class="item-wrapper"
        for="{{item}}"
        @click="goToDetail($item)"
        style="margin-left:{{$idx%3==0?0:14}}px;"
      >
        <div class="list-image">
          <image src="{{$item.image}}"></image>
        </div>
        <text class="book-title">{{ $item.title }}</text>
        <text class="book-author">{{ $item.author }}</text>
      </div>
    </list-item>
    <list-item type="noMore" class="load-status">
      <my-load-more tip="没有更多内容了" loading="{{ false }}"></my-load-more>
    </list-item>
  </list>
</template>

<script>
export default {
  props: {
    list: {},
    showLoad: {
      default: true
    },
    title: {
      default: '图书列表'
    }
  },
  computed: {
    subList() {
      return $utils.array2group(this.list, 3)
    }
  },
  /* -------------------SelfCustomEvent------------------ */
  goToDetail(item) {
    // 根据id查询详情
    COMMON_REPORT_UTILS.page_click_report('漫画书');
    this.$emit('updatePageHide');
    $utils.routetheUrl('subPages/book-detail', { info: item })
  }
}
</script>

<style lang="less">
@import '../../assets/styles/index.less';

.rank-title {
  margin-bottom: @gap-1;
  .list-title;
}
.rank-item {
  width: 100%;
  margin: @gap-2 0;
  .rank-icon {
    height: 60px;
    width: 60px;
    border-radius: 30px;
    .flex-box-mixins(row, center, center);
  }
  .rank-bg {
    border-radius: 30px;
    height: 60px;
    width: 100%;
  }
  .rank-text {
    width: 100%;
    padding-left: 80px;
    padding-right: 30px;
    .flex-box-mixins(row, space-between, center);
  }
  text {
    color: @white;
  }
}
.rank-0 {
  .rank-icon {
    background-color: #4f586e;
  }
  .rank-bg {
    background-color: #77819c;
  }
}
.rank-1 {
  .rank-icon {
    background-color: #2048af;
  }
  .rank-bg {
    background-color: #5873bd;
  }
}
.rank-2 {
  .rank-icon {
    background-color: #306df1;
  }
  .rank-bg {
    background-color: #6695fa;
  }
}

.book-item {
  .list-item;

  .list-image {
    width: 224px;
    height: 300px;
    border: 1px solid #f0f0f0;
    border-radius: 14px;

    image {
      width: 100%;
      height: 100%;
      border-radius: 14px;
    }
  }
}

.item-wrapper {
  flex-direction: column;
}

.book-title {
  color: #333333;
  height: 30px;
  font-size: 30px;
  margin-top: @gap-2;
  lines: 1;
  text-overflow: ellipsis;
}

.book-author {
  height: 24px;
  color: #999999;
  font-size: 24px;
  margin-top: 14px;
  lines: 1;
  text-overflow: ellipsis;
}

.list-image {
  width: 224px;
  height: 300px;
  border: 1px solid #f0f0f0;
  border-radius: 14px;

  image {
    width: 100%;
    height: 100%;
    border-radius: 14px;
  }
}

.mg {
  margin: 0 @app-padding;
}
</style>
