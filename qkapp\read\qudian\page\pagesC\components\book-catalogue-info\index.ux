<import name="common-header" src="../../../pagesA/components/common-header/index.ux"></import>

<template>
  <!-- template里只能有一个根节点 -->
  <div class="catalogue-container">
    <div class="{{showMaskAnimation}}" style="position: absolute;left: 0;top: 0;width:100%;height:100%;background-color: rgba(0, 0, 0, .6);"></div>
    <div class="content-empty" @click="emptyClickHandler"></div>
    <div class="catalogue-content {{showEditorAnimation}}">
      <div class="catalogue-title">
        <div style="height:1px"></div>
        <text>章节目录</text>
        <image @click="emptyClickHandler" src="https://img.qdreads.com/v163/<EMAIL>" style="width:42px;height:42px;"></image>
      </div>
      <list
        id="catalogue-list"
        style="flex: 1;"
        onscrollbottom="scrollBottomHandler"
        onscrolltop="scrollTopHandler"
      >
        <list-item type="catalogue-item-top">
          <text style="height: 10px; width: 100%"></text>
        </list-item>
        <list-item
          type="catalogue-item"
          for="(index,item) in catalogueData"
          class="catalogue-item"
          @click="catalogueItemClick(item)"
        >
          <text
            class="item-title {{currentId == item.chapterId ? 'item-title-2' : !item.read ? 'item-title-3' : 'item-title-1'}}"
            >{{ item.title }}</text
          >
          <image
            show="{{item.lock == 1}}"
            class="item-icon"
            src="https://img.qdreads.com/v155/read/read-icon-2.png"
          ></image>
        </list-item>
        <!-- 加载更多，type属性自定义命名为loadMore -->
        <list-item
          type="loadMore"
          class="load-more"
          if="{{page !== totalPage}}"
        >
          <progress type="circular"></progress>
          <text>加载更多</text>
        </list-item>
      </list>
    </div>
  </div>
</template>
<script>
export default {
  data: {
    showEditorAnimation: '',
    showMaskAnimation: '',
    scrollStep:0
  },
  onInit() {
    console.log(this.currentId)
    this.$watch('viewScrollType', 'viewScrollTypeHandler')
    this.showEditorAnimation = 'showEditorAnimation'
    this.showMaskAnimation = 'showMaskAnimation'
  },
  viewScrollTypeHandler(newVal, oldVal) {
    let curNewVal = newVal.split('_')[0]
    if (curNewVal == 'pop') {
      setTimeout(() => {
        this.$element('catalogue-list').scrollTo({ index: 100, smooth: false })
      }, 200)
    }
  },
  onReady() {
    let that = this
    setTimeout(() => {
      this.$element('catalogue-list').getBoundingClientRect({
        success: function (data) {
          const { height } = data;
          that.scrollStep = Math.floor(height /216)-1
          console.log('scrollStep============>', that.scrollStep)
          let scrollIndex = that.currentIndex % 100
          scrollIndex = that.scrollStep < scrollIndex ? scrollIndex - that.scrollStep : scrollIndex
          that.$element('catalogue-list').scrollTo({ index: scrollIndex, smooth: false })
        }
      })
    }, 50)

  },
  /**
   * 章节列表点击
   * @param {Number} item 章节
   */
  catalogueItemClick(item) {
    this.showEditorAnimation = 'hideEditorAnimation'
    this.showMaskAnimation = 'hideMaskAnimation'
    this.$emit('compClick', { name: 'chapterClick', curItem: item })
  },
  /**
   * 空白处点击
   */
  emptyClickHandler() {
    this.showEditorAnimation = 'hideEditorAnimation'
    this.showMaskAnimation = 'hideMaskAnimation'
    this.$emit('compClick', { name: 'empty' })
  },
  /**
   * 滚动到底部
   */
  scrollBottomHandler() {
    this.$emit('compClick', { name: 'scrollBottom' })
    if (this.page >= this.totalPage) {
      $utils.showToast('已经到底了')
      return
    }
  },
  /**
   * 滚动到顶部
   */
  scrollTopHandler() {
    if (this.page == 1) {
      $utils.showToast('已经到顶了')
      return
    }
    this.$emit('compClick', { name: 'scrollTop' })
  },
  props: {
    catalogueData: {
      type: Array,
      default: null
    },
    currentId: {
      type: Number,
      default: 0
    },
    bookName: {
      type: String,
      default: ''
    },
    page: {
      type: Number,
      default: 1
    },
    currentIndex: {
      type: Number,
      default: 0
    },
    viewScrollType: {
      type: String,
      default: 'push'
    },
    totalPage: {
      type: Number,
      default: 1
    }
  }
}
</script>
<style lang="less">
@import './index.less';
</style>
