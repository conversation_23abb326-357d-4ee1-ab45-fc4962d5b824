<template>
  <div class="wrapper">
    <text class="text-wrapper">{{toastText}}</text>
  </div>
</template>

<script>
export default {
  data: {
    
  },
  
  props: ['toastText'],
  
  onInit() {},

  
}
</script>

<style>
.wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 750px;
  height: 100%;
  justify-content: center;
  align-items: center;
}

.text-wrapper {
  background-color: rgba(0,0,0,0.70);
  border-radius: 20px;
  padding: 0 50px;
  font-size: 30px;
  color: #ffffff;
  height: 98px;
}
</style>
