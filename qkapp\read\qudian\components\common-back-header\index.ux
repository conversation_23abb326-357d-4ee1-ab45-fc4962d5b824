<template>
  <!-- template里只能有一个根节点 -->
  <div>
    <div
      class="common-back-header-wrapper"
      style="background-color:{{backgroudColor}}"
    >
      <div
        class="common-back-header"
        style="padding-top:{{deviceInfo.statusBarHeight}}px;height:{{deviceInfo.statusBarHeight + 88}}px;align-items:center;"
      >
        <div if="{{backIconShow && backImgUrl}}"
            @click="backClick"
            class="{{backSize?'common-back-icon-normal':'common-back-icon-qd'}}">
          <image
            src="{{backImgUrl}}"
          ></image>
        </div>
        
         <image
          elif="{{backIconShow && !backImgUrl}}"
          @click="backClick"
          class="common-back-icon"
          src="{{textColor === '#FFFFFF'? 'https://img.qdreads.com/v163_2/<EMAIL>' : 'https://img.qdreads.com/v163_2/<EMAIL>' }}"
        ></image>
        <text
          class="common-title"
          style="color:{{textColor}};font-size:{{fontSize}}px;font-weight:{{textWeight}};text-align:{{textCenter ? 'center' : 'left'}}"
          >{{ title }}</text
        >
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data: {
    pageName: '书城',
    deviceInfo: {
      statusBarHeight: 40
    }
  },
  onReady() {
    const { pageCode } = sdk.tactics.getPageInfo() || {}
    this.deviceInfo.statusBarHeight = sdk.env.device.statusBarHeight
    if (pageCode == 'READ_READINFO' && sdk.env.device.brand == 'huawei') {
      this.deviceInfo.statusBarHeight = 0;
    }
  },
  backClick() {
    this.$emit('backClick')
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    backgroudColor: {
      type: String,
      default: 'transparent'
    },
    textCenter: {
      type: Boolean,
      default: true
    },
    textColor: {
      type: String,
      default: '#333333'
    },
    textWeight: {
      type: Number,
      default: 600
    },
    fontSize: {
      type: Number,
      default: 36
    },
    backIconShow: {
      type: Boolean,
      default: true
    },
    backImgUrl:{
      type: String,
      default: ''
    },
    backSize:{
      type: String,
      default: ''
    }
  }
}
</script>

<style lang="less">
.common-back-header-wrapper {
  .common-back-header {
    width: 750px;
    flex-direction: row;
    padding: 0 30px;
    .common-back-icon {
      width: 30px;
      height: 30px;
      margin-right: 10px;
      object-fit: fill;
    }
    .common-title {
      font-size: 34px;
      font-weight: bold;
      text-align: center;
      flex: 1;
      padding-right: 68px;
      lines: 1;
      text-overflow: ellipsis;
      height: 88px;
    }
  }
}
.common-back-icon-normal{
  flex-direction: column;
  width: 50px;
  height: 88px;
  padding: 20px 10px;
  align-content: center;
  text-align: center;
  justify-content: center;
  image{
    width: 30px;
    height: 30px;
  }
}
.common-back-icon-qd{
  flex-direction: column;
  width: 50px;
  height: 88px;
  padding: 20px 0 20px 5px;
  align-content: center;
  text-align: center;
  justify-content: center;
  image{
    width: 12px;
    height: 20px;
    margin-left: 23px;
  }
}
</style>
