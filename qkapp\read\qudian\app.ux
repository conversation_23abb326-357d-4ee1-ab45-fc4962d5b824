<script>
import commonApp from "./utils/app_ux_public.js"

const hook2global = global.__proto__ || global
hook2global.$shelfList = []
hook2global.$collectList = []

export default appMixin({
  $shelfList: hook2global.$shelfList,
  $collectList: hook2global.$collectList,
  tabListType:[0,0,0,0,0],
  readIndoTakeVideTime: 0,
  ...commonApp,
  onCreate: commonApp.onCreatePublic,
  onShow: function () {
    commonApp.onShowPublic(this)
  },
  onHide: function () {
    commonApp.onHidePublic(this)
  },
  onError: function (err) {
    commonApp.onErrorPublic(this, err)
  },
  onDestroy: function () {
    commonApp.onDestroyPublic(this)
  },
  /**
 * 添加日历时 获取权限提示
 */
  isNeedShowDialogForCalendarClick() {
    return new Promise((reslove, reject) => {
      $utils.showDialog('提示', '趣点阅读需要获取您的日历权限，以便您正常使用签到提醒等服务', [
        { text: '确定', color: '#333333' },
        { text: '取消', color: '#333333' }], res => {
          if (res.index == 0) {
            reslove()
          } else {
            $utils.showToast("您已取消")
          }
        })
    })
  },
  /**
* 获取 app 缓存数据
* @param key
*/
  getAppData(key) {
    let isHas = this.dataCache.hasOwnProperty(`${key}`);
    if (isHas) {
      return this.dataCache[key]
    }
    return ''
  },
  /**
 * 设置 APP 缓存数据
 * @param key
 * @param val
 */
  setAppData(key, val) {
    this.dataCache[key] = val;
  },
  /**
 * 检查是否被动加桌
 */
  async checkUserInstallShortCut() {
    try {
      let userShortCutData = JSON.parse(await $utils.getStorage('_SD_BD_ICON_STATE_'))
      if (userShortCutData && typeof userShortCutData == 'object') {
        if (userShortCutData.last_state.state !== userShortCutData.tod_state.state && userShortCutData.tod_state.state == 1) {
          this.setAddDesktopInfo(1)
          COMMON_REPORT_UTILS.add_icon_report('成功', '被动加桌', '桌面')
        }
      }
    } catch (error) {

    }

  },
  /**
 * 保存加桌信息
 */
  async setAddDesktopInfo(t) {
    LOG('加桌--', t)
    this.addDeskTopStatus = 1
    await $utils.setStorage('addDesktopTimestamp', new Date().getTime())
    await $utils.setStorage('addDesktopUserInfo', { linkId: this.linkId, channelId: this.channelId, creativeid: this.utm_content, addTime: $utils.dateToString() })
  },

  /**
* 福利任务数据更新
*/
  taskInfoUpdateHandle(taskInfo) {
    return new Promise((resolve, reject) => {
      $apis.example.taskUpdataApi(taskInfo).then(res => {
        if (res.code == 200) {
          resolve()
          console.log('任务上报成功===>', taskInfo)
        } else {
          reject()
        }
      })
    })
  },
  //列表展示上报
  uploadListShow(dataList, sectionName) {
    if (dataList.length > 0) {
      var boodIds = [], bookNames = []
      dataList.forEach(item => {
        boodIds.push(`${item.bookId}`)
      })
      boodIds.sort((a, b) => a - b)
      LOG('uploadListShow', boodIds, sectionName)
      COMMON_REPORT_UTILS.list_show_report('1', boodIds, sectionName) //点击上报
    }
  },
  // 加桌状态检测
  getAddDesktopStatus(isShow = false) {
    let that = this
    if (that.$def.addDeskTopStatus || (that.$def.appIshow && isShow)) return
    that.$def.appIshow = true
    require("@system.shortcut").hasInstalled({
      success: function (ret) {
        if (isShow && ret) {
          that.$def.addDeskTopStatus = 1
        } else {
          if (ret && that.$def.addDeskTopStatus == 0) {
            that.$def.addDeskTopStatus = 1
            that.setAddDesktopInfo(2)
            COMMON_REPORT_UTILS.add_icon_report('成功', '系统加桌', '')
          }
        }
      }
    })
  },
  // 获取用户信息
  async getUserInfo() {
    await $utils.getStorage('userInfo').then(res => {
      if (res != '' && typeof res != "undefined") {
        let data = JSON.parse(res)
        this.isVip = data.isvip
        this.sex = data.sex
      }
    })
  },
  // 更新storage的用户信息, 传参为需要覆盖的object
  async updateUserInfo(obj) {
    let res = await $utils.getStorage('userInfo');
    if (res != '' && typeof res != "undefined") {
      res = JSON.parse(res);
      $utils.setStorage('userInfo', Object.assign(res, obj));
    }
  },
  async login(loginParam = {}) {
    let param = {
      androidId: sdk.env.identity.androidId,
      oaid: this.oaid,
      sex: this.sex,
      brand: sdk.env.device.brand,
      regId: this.regId,
      versionCode: this.versionCode,
      versionName: this.versionName,
      intent: this.intent,
      isSetSex: 0,
      model: encodeURIComponent(this.model)
    }
    return new Promise((resolve, reject) => {
      $apis.example.login(Object.assign(param, loginParam)).then(res => {
        if (res.code == 200) {
          this.auditDesk = res.data.auditDesk
          this.isPay = res.data.isPay
          this.isVip = res.data.isvip
          this.loginStatus = res.data.loginStatus
          this.sex = res.data.sex
          this.isSetSex = res.data.isSetSex
          $utils.setStorage('token', res.data.online_token)
          $utils.setStorage('userInfo', res.data)
        }
        resolve(res)
      }).catch(error => {
        reject(error)
      })
    })
  },
  checkLogin() {
    let needLogin = Number(sdk.tactics.getCustomParams('is_recharge_login'));
    if (needLogin == 1 || this.auditOnline == 1) {
      if (this.loginStatus == 0) {
        $utils.showToast("为避免书币丢失，请先登录", 0);
        $utils.routetheUrl('/pagesB/Login', { isNormalBack: 1 });
        return false;
      }
    }
    return true;
  }
})

</script>