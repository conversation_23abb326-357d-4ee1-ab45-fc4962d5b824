const { getAppBasePath } = require('../config/app-base')
const path = require('path')
const fs = require('fs')
const parser = require('@babel/parser')
const traverse = require('@babel/traverse').default

let versionNameIndex = { start: 0, end: 0 }
let versionCodeIndex = { start: 0, end: 0 }

function getVersionName(brand = 'xiaomi') {
  const appBasePath = getAppBasePath()
  const appConfigPath = path.resolve(appBasePath, 'brand-config.js')
  let appConfig = fs.readFileSync(appConfigPath, 'utf8')

  parseBrandConfig(appConfig, brand)
  const originVersionName = appConfig.slice(versionNameIndex.start, versionNameIndex.end)
  const originVersionCode = appConfig.slice(versionCodeIndex.start, versionCodeIndex.end)
  return {
    versionName: originVersionName,
    versionCodeIndex,
    versionCode: originVersionCode
  }
}

function setVersionCode(newVersionCode, versionCodeIndex) {
  if (versionCodeIndex.start === 0) {
    console.log('没有找到版本号,需手动添加')
    return
  }
  if (!newVersionCode) {
    console.log('没有找到对应规则,请检查归属厂商')
    return
  }
  const appBasePath = getAppBasePath()
  const appConfigPath = path.resolve(appBasePath, 'brand-config.js')
  let appConfig = fs.readFileSync(appConfigPath, 'utf8')
  appConfig = appConfig.slice(0, versionCodeIndex.start) + newVersionCode + appConfig.slice(versionCodeIndex.end)
  fs.writeFileSync(appConfigPath, appConfig)
}

function parseBrandConfig(appConfig, brand) {
  const ast = parser.parse(appConfig, {
    sourceType: 'module',
    plugins: ['jsx']
  })
  traverse(ast, {
    ObjectProperty(path) {
      if (path.node.key.name === brand && path.node.value.properties) {
        path.node.value.properties.forEach(prop => {
          if (prop.key.name === 'manifest') {
            prop.value.properties.forEach(manifestProp => {
              if (manifestProp.key.name === 'versionName' || manifestProp.key.value === 'versionName') {
                versionNameIndex.start = manifestProp.value.start
                versionNameIndex.end = manifestProp.value.end
                console.log('versionNameIndex', versionNameIndex)
              }
              if (manifestProp.key.name === 'versionCode' || manifestProp.key.value === 'versionCode') {
                versionCodeIndex.start = manifestProp.value.start
                versionCodeIndex.end = manifestProp.value.end
                console.log('versionCodeIndex', versionCodeIndex)
              }
            })
          }
        })
      }
    }
  })

  return ast
}

module.exports = { getVersionName, setVersionCode }
