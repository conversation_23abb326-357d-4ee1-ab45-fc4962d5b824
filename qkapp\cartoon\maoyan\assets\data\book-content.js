export const OSSBaseUrl = 'https://img.sytangshiwl.top/jingyue'

export function getBookContent(bookId, chapterId, total) {
  let url = `${OSSBaseUrl}/book-${bookId}/book-${bookId}_chapter-${chapterId}`
  let content = [],
    i = 0
  while (i < total) {
    content.push(url + `_${i + 1}.jpg`)
    i++
  }
  return content
}

export function getChapterImg(bookId, total) {
  let url = `${OSSBaseUrl}/swiper`
  let content = [],
    i = 0
  while (i < total) {
    content.push(url + `/book-${bookId}.png`)
    i++
  }
  return content
}
