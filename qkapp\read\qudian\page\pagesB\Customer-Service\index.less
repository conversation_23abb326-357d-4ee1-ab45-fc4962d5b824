.complaint {
    width: 100%;
    flex-direction: column;
    align-items: center;
    background-color: #f3f5f7;
    .back_img{
        width: 100%;
        height: 714px;
        background: linear-gradient(180deg,#d1ebfe, #f3f5f7 100%);
        position: absolute;
        top: 0;
    }
    .nav-wrapper{
      width:100%;
    }
    .nav-back {
        flex-shrink: 0;
        margin-top: 80px;
        align-self: flex-start;
        margin-left: 20px;
        image {
            width: 40px;
            height: 40px;
          }
        text{
            color: #333333;
            font-size: 32px;
            line-height: 32px;
            font-weight: 600;
        }
    }

    .blank {
        width: 100%;
        height: 26px;
        border-top: 2px solid #f5f5f5;
    }

    .content {
        margin-top: 30px;
        border-radius: 20px;
        flex-direction: column;
        padding: 0 30px;
        background-color: #ffffff;
        width: 686px;
        padding: 30px;
        height: 566px;
        .title {
            text{
                font-size: 36px;
                font-weight: 600;
                color: #333333;
            }
        }
        .reasons{
            height: 66px;
            width: 100%;
            margin-top: 32px;
            margin-bottom: 30px;
            .reason-item{
                height: 100%;
                border-radius: 14px;
                background-color: #f5f6fa;
                display: flex;
                align-items: center;
                margin-right: 30px;
                padding: 0 20px;
                text{
                    font-size: 26px;
                    font-weight: 400;
                    color: #333;
                    text-align: center;
                    line-height: 26px;
                }
            }
        }
        .textarea {
            width: 100%;
            height: 340px;
            background-color: #f3f5f7;
            border-radius: 20px;
            textarea {
                width: 100%;
                height: 350px;
                padding: 30px;
                font-size: 28px;
            }

            text {
                color: #cccccc;
                right: 20px;
                bottom: 20px;
            }
        }
    }

    .upload {
        padding: 0 30px;
        flex-direction: column;

        .font_16 {
            padding-top: 60px;
            padding-bottom: 15px;
        }

        .imgs {
            width: 100%;
            height: 176px;
            justify-content: flex-start;

            .item {
                padding-right: 2px;
                width: 180px;
                height: 176px;

                .img {
                    width: 162px;
                    height: 162px;
                    margin-top: 14px;
                }

                .del {
                    width: 30px;
                    height: 30px;
                    border-radius: 15px;
                    margin-left: 147px;

                    image {
                        width: 30px;
                        height: 30px;
                    }
                }
            }

            .imgDiv {
                width: 176px;
                height: 176px;
                justify-content: center;
                align-items: center;

                div {
                    width: 162px;
                    height: 162px;
                    background-color: #f6f6f6;
                    justify-content: center;
                    align-items: center;
                    margin-top: 14px;

                    image {
                        width: 50px;
                        height: 50px;
                    }
                }
            }

        }
    }

    .info {
        flex-direction: row;
        width: 686px;
        height: 100px;
        background-color: #fff;
        border-radius: 20px;
        text-align: center;
        margin-top: 30px;
        padding: 0 30px;
        display: flex;
        justify-content: space-between;
        input {
            width: 500px;
            height: 100px;
            line-height: 28px;
            font-size: 28px;
        }
        .xuantian{
            // width: 60px;
            text{
                font-size: 28px;
                color: #999;
            }
        }
    }

    .submitBtn {
        width: 100%;
        justify-content: center;
        margin-top: 50px;

        text {
            width: 660px;
            height: 102px;
            line-height: 102px;
            background-color: #0c84ff;
            border-radius: 102px;
            opacity: 0.5;
            font-size: 38px;
        }

        .red {
            opacity: 1;
        }
    }

    .popup {
        position: fixed;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: rgba(0, 0, 0, 0.7);
        justify-content: center;
        align-items: center;

        .closeBtn {
            width: 26px;
            height: 26px;
            position: absolute;
            top: 76px;
            right: 26px;

            image {
                width: 26px;
                height: 26px;
            }
        }

        .subPic {
            width: 130px;
            height: 130px;

            image {
                width: 130px;
                height: 130px;
            }
        }

        .popupContent {
            flex-direction: column;
            width: 580px;
            height: 406px;
            background-color: #fff;
            border-radius: 30px;
            margin-top: 60px;
            padding-top: 38px;

            .title {
                padding-top: 60px;
                color: #333;
                font-size: 40px;
            }

            .tips {
                width: 100%;
                line-height: 40px;
                color: #858585;
                font-size: 30px;
                padding: 30px 80px 0;
            }

            .btn {
                width: 100%;
                height: 80px;
                margin-top: 70px;
                justify-content: center;
                align-items: center;
                flex-direction: row;

                text {
                    width: 240px;
                    height: 80px;
                    color: #fff;
                    border-radius: 40px;
                    font-size: 36px;
                    line-height: 80px;
                }

                .btn1 {
                    background: linear-gradient(180deg, #FFDC7F 0%, #FF9A16 100%);
                }

                .btn2 {
                    margin-left: 20px;
                    background: linear-gradient(180deg, #FE8E2D 0%, #FF6016 100%);
                }
            }
        }
    }

    .loading {
        position: fixed;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        justify-content: center;
        align-items: center;

        div {
            width: 220px;
            padding: 30px 0;
            flex-direction: column;
            align-items: center;
            background-color: rgba(0, 0, 0, 0.7);
            border-radius: 10px;

            .loadingImg {
                width: 80px;
                height: 80px;
                animation-name: rotateMove;
                animation-duration: 2s;
                animation-iteration-count: infinite;
                animation-timing-function: linear;
            }

            text {
                font-size: 28px;
                color: #ffffff;
            }

            @keyframes rotateMove {
                0% {
                    transform: rotate(0deg);
                }

                50% {
                    transform: rotate(180deg);
                }

                100% {
                    transform: rotate(360deg);
                }
            }
        }
    }
}
.wrapper {
    display: flex;
    align-items: center;
    border-radius: 10px;
    padding: 0 28px;
    width: 686px;
    flex-direction: row;
    justify-content: space-between;
    .text-content{
        display: flex;
        flex-direction: column;
        .greeting{
            color:#333333;
            font-size: 36px;
            font-weight: 600;
            line-height: 36px;
            margin-bottom: 20px;
        }
        .message{
            color:#333333;
            font-size: 28px;
            font-weight: 400;
            line-height: 28px;
        }
    }
}

.image {
    width: 226px;
    height: 197px;
}


.bold {
    font-weight: bold;
}
.c3 {
    color: #333;
}
.font_16 {
    font-size: 32px;
}
.pa {
    position: absolute;
}
.font_14 {
    font-size: 28px;
}
.white {
    color: #fff;
}
.tc {
    text-align: center;
}