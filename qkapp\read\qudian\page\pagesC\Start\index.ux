<template>
  <!-- template里只能有一个根节点 -->
  <div>
    <div class="start-wrapper">
        <image class="bg" src="https://img.qdreads.com/v163/common/start-bg.png"></image>
        <div class="logo-box">
          <image class="logo" src="https://img.qdreads.com/v163/common/start-logo.png"></image>
          <text class="des">趣点阅读</text>
        </div>
    </div>
  </div>
</template>
<import name="back-app-button" src="../../components/back-app-button"></import>

<script>
import storage from '@system.storage'
import share from '@service.share'
import app from '@system.app'
import push from '@service.push'
// import devConfig from '../../helper/devConfig.js'

export default pageMixin({
  public: {
    modeId: 0,
    intent: 1, //
    shareType: '',
    shareId: '', // 邀请人id
    typeMap: ['', 'morn_share', 'noon_share', 'night_share', 'is_invite', 'read_share'],// 1早上分享 2中午分享 3晚上分享 4邀请好友
    pathUrl: "桌面",
    pageDetail: {
      pageName: '启动页',
      pageUrl: '桌面',
      pageCode: 'DESK_ROOT',
      pageOrigin: ""
    },
    bookId: '',
  },

  async onInit() {
    LOG('onInit========>StartPage')
    this.intent = this.intent
    await this.initData()
    await this.$app.$def.checkUserInstallShortCut()
  },
  // 初始化SDK 相关
 async initData() {
    let that = this;

    that.routerPatch()
  },
  routerPatch() {
    if (this.intent == 1) {
      $utils.getStorage('userInfo').then(res => {
        if (res == '' || res == undefined) {
          $utils.routeReplacetheUrl('/pagesB/Sex', { pathUrl: "桌面" })
        } else {
          var info = JSON.parse(res)
          if (!info.androidId) {
            $utils.routeReplacetheUrl('/pagesB/Sex', { pathUrl: "桌面" })
            return
          }
          this.$app.$def.android = info.androidId
          this.$app.$def.oaid = info.oaid
          this.$app.$def.sex = info.sex
          this.$app.$def.login({isSetSex:0}).then(res => {
            if (res.code == 200) {
              $utils.getStorage('on_destroy_read').then(data => {
                if (data) {
                  data = JSON.parse(data)
                  if (data.readBookId && data.expireTime && $utils.adFreeTimeDifference(data.expireTime)) {
                    $utils.deleteStorage('on_destroy_read')
                    $utils.routeReplacetheUrl('/pagesC/Read', { bookId: data.readBookId, chapterId: data.readChapterId })
                    return
                  }
                }
                $utils.routeReplacetheUrl('/pagesA/Main', { pathUrl: "桌面" })
              })
            } else if (res.code == 409) {
              $utils.routeReplacetheUrl('/pagesB/Login', { pathUrl: "桌面" })
            }
          })
        }
      })
    } else if (this.intent == 4) {
      this.$app.$def.login({
        sex: 1,
        isSetSex:0,
        inviteId: this.shareId,
        shareType: this.typeMap[this.shareType],
      }).then(res => {
        if (res.code == 200) {
          if (this.bookId) {
            $utils.routeReplacetheUrl('/pagesC/Read', { bookId: this.bookId })
          } else {
            this.$app.$def.tabListType[4] = 0
            $utils.routeReplacetheUrl('/pagesA/Main')
          }
        } else if (res.code == 409) {
          $utils.routeReplacetheUrl('/pagesB/Login')
        }
      })
    }
  },
  onShow() {
    let that = this
    
  },
  onHide() {
    
  },
})
</script>

<style lang="less">
.start-wrapper {
  width: 100%;
  height: 100%;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: fixed;
  .bg {
    width: 490px;
    height: 490px;
    object-fit: fill;
  }
  .logo-box {
    position:absolute;
    bottom: 90px;
    align-items: center;
    justify-content: center;
    height: 58px;
    .logo {
      width: 58px;
      height: 58px;
    }
    .des {
      font-size: 40px;
      font-weight: bold;
      color: #333;
      line-height: 40px;
      height: 40px;
      margin-left: 20px;
    }
  }
}


</style>
