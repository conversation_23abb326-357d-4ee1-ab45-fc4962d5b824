.welfare-wrapper {
    width: 750px;
    flex-direction: column;
    align-items: center;
    background-color: #FF522E;
}
.page-stack-1 {
    width: 100%;
    height: 884px;
}
.page-stack-2 {
    width: 100%;
    margin-top: 276px;
}
.page-stack-3{
    width: 100%;
    height: 370px;
    justify-content: center;
    align-items: flex-end;

    .back-image{
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0px;
        left: 0px;
    }
}
.page-stack-4 {
    height: 276px;
    flex-direction: column;
    .page-title {
        font-size: 36px;
        font-weight: 500;
        color: #ffffff;
    }

    .coin-detail {
        height: 50px;
        margin-top: auto;
        margin-bottom: 50px;
        background-color: rgba(255, 255, 255, 0.75);
        border-radius: 200px;
        align-items: center;
        margin-left: 24px;
        .coin-img{
            width: 48px;
            height: 50px;
        }
        .coin-text {
            height: 24px;
            font-size: 24px;
            font-weight: 700;
            color: #333333;
            margin-left: 10px;
        }
        .coin-button{
            align-items: center;
            margin-left: 12px;
            margin-right: 20px;
            text{
            height: 24px;
            font-size: 24px;
            font-weight: 500;
            color: #ee2833;
            }
            image{
                width: 14px;
                height: 16px;
                margin-left: 4px;
            }
        }
    }
 }
.top-stack-new {
    width: 100%;
    height: 518px;
    .activity-1{
        width: 116px;
        height: 174px;
        position: absolute;
        top: 324px;
        left: 20px;
        align-items: center;
        justify-content: center;
    }
    .activity-2{
        width: 116px;
        height: 174px;
        position: absolute;
        top: 324px;
        right: 20px;
        align-items: center;
        justify-content: center;
    }
}


.task-section-header {
    width: 750px;
    height: 94px;
    .img {
        position: absolute;
        top: 0px;
        left: 0px;
        width: 100%;
        height: 100%;
    }
    .title {
        width: 100%;
        margin-top: 20px;
        font-size: 36px;
        font-weight: 700;
        text-align: center;
        color: #bf7402;
        height: 36px;
    }
    .arrow{
        width: 40px;
        height: 40px;
        top: 36px;
        right: 24px;
        position: absolute;
    }
}
.task-section-content{
    border-top-left-radius: 28px;
    border-top-right-radius: 28px;
    position: absolute;
    top: 16px;
    left: 0px;
    bottom: 0px;
    width: 100%;
    background-color: #FF522E;
}
.header-bg-image {
    background-image: url('https://img.qdreads.com/image%2F2022-11-08%2Fwelfare-header-new.png'); 
    background-size: contain;
    background-position: top;
    background-repeat: no-repeat;
    background-color: #FF522E;
    position: absolute;
    top: 0;
    left: 0;
    // height: 884px;
    height: 100%;
    width: 750px;
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
}
.header-content {
    width: 750px;
    height: 884px;

    .header-button {
        width: 440px;
        height: 174px;
        position: absolute;
        top: 600px;
        left: 156px;
    }
    .activity-1{
        width: 116px;
        height: 174px;
        position: absolute;
        top: 600px;
        left: 20px;
        align-items: center;
        justify-content: center;
    }
    .activity-2{
        width: 116px;
        height: 174px;
        position: absolute;
        top: 600px;
        right: 20px;
        align-items: center;
        justify-content: center;
    }
}

.task-wrapper {
    padding: 32px;
    margin-top: 36px;
    width: 702px;
    border-radius: 20px;
    flex-direction: column;
    background-color: #fff;
}

.new-user-content {
    width: 100%;
    flex-direction: column;
    align-items: center;
}

.new-user-task-item {
    width: 618px;
    height: 144px;
    align-items: center;
    .new-user-task-icon {
        width: 84px;
        height: 84px;
    }
}

.new-user-info-wrap {
    flex-grow: 1;
    flex-direction: column;
    margin-left: 20px;
    .new-user-task-title {
        margin-bottom: 16px;
        font-size: 32px;
        font-weight: 700;
        color: #953600;
        line-height: 32px;
        height: 32px;
    }
    .new-user-desc-wrap {
        align-items: center;
        >text {
            font-size: 24px;
            color: #bc7b55;
            line-height: 24px;
            height: 24px;
        }
        >image {
            width: 28px;
            height: 26px;
            margin: 0 8px 0 5px;
        }
    }
}

.new-user-task-coin {
    margin-top: 30px;
    width: 100%;
    justify-content: space-between;
}

.new-user-task-button {
    width: 136px;
    height: 56px;
    border-radius: 30px;
    font-size: 26px;
    font-weight: 700;
    text-align: center;
    color: #fff8eb;
    line-height: 26px;
}

.button-color-1 {
    background: linear-gradient(180deg, #feac00, #ff9401 100%);
}

.button-color-2 {
    background: linear-gradient(270deg, #ff624e 0%, #ff3e48);
}

.button-color-3 {
    background: linear-gradient(180deg, #eaeaea, #e1e1e1 100%);
    color: #b9b8b8;
}

.new-user-text-color {
    color: #FF9401;
}

.new-user-text-color-2 {
    color: #F95B31;
}

.welfare-text-1 {
    font-size: 28px;
    font-weight: 500;
    text-align: left;
    color: #666666;
    line-height: 28px;
    margin-left: 6px;
}

.welfare-text-2 {
    font-size: 40px;
    font-weight: 700;
    text-align: left;
    line-height: 40px;
}

.daily-task-content {
    align-items: center;
    padding: 0px 12px;
    flex-direction: column;
}

.wl-ad-wrapper {
    height: 554px;
    width: 100%;
    align-items: center;
    justify-content: center;
}

.back-icon-img {
    width: 32;
    height: 32;
    object-fit: fill;
    margin-left: 24px;
    margin-top: 4px;
}

.box-fixed-container {
    position: fixed;
    right: 1px;
    bottom: 454px;
    width: 130px;
    height: 130px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.box-fixed-image {
    width: 129px;
    height: 96px;
}

.box-fixed-button-icon {
    width: 117px;
    height: 37px;
    object-fit: fill;
    margin-top: 79px;
    margin-left: 6px;
}

.box-fixed-text {
    width: 117px;
    height: 37px;
    margin-top: 79px;
    text-align: center;
    line-height: 37px;
    font-size: 20px;
    color: #FA2D18;
    margin-left: 6px;
}

.boots-hide {
    width: 750px; 
    padding: 0 0 0 24px;
    opacity:0;
}

.boots-show {
    width: 750px;
    padding: 15px 0 15px 24px;
}

.task-title-1 {
    align-items: center;
    margin-bottom: 32px;

    > image {
        width: 128px;
        height: 30px;
    }

    > text {
        color: #999999;
        height: 24px;
        font-size: 24px;
        margin-left: 12px;
    }
}
.native-boost-group{
    position: absolute;
    bottom: 0px;
    left: 0px;
    width: 750px;
    height: 500px;
}
.circle-boost-wrap{
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    bottom: 0;
}