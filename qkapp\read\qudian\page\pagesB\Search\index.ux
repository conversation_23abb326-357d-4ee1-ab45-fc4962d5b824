<import name="common-header" src="../../components/common-back-header/index.ux"></import>
<template>
  <div class="search-wrapper">
    <div>
      
    </div>
    <!-- 状态栏 -->
    <common-header
      style=""
      title="搜索"
      text-center="{{backTitleIsCenter}}"
      onback-click="pageBack"
    ></common-header>
    <!-- 搜索栏 -->
    <div class="search-input-section">
      <div class="search-input-box">
        <image
          if="{{keyWord}}"
          class="search-input-icon"
          src="https://img.qdreads.com/v155/common-pagesb/search-input.png"
        ></image>
        <image
          else
          class="search-input-icon"
          src="https://img.qdreads.com/v155/common-pagesb/common-search.png"
        ></image>
        <input
          id="search-text"
          class="search-input"
          type="text"
          placeholder="{{placeholderKey}}"
          enterkeytype="search"
          onchange="keyWordChange"
          onenterkeyclick="enterkeyclickHandler"
          value="{{keyWord}}"
        />
        <image
          class="search-del"
          src="https://img.qdreads.com/v155/search/search-del.png"
          if="{{keyWord}}"
          @click="searchClear"
        ></image>
      </div>
    </div>

    <!-- <block if="isShowSearchTop"> -->
      <list show="{{isShowSearchTop}}" style="align-items: center;flex:1;"  onscroll="hideKeyboard">
        <!-- 热门搜索 -->
        <list-item type="search-hot" class="search-hot-section" style="padding-bottom:0px;" onclick="hideKeyboard">
          <div class="search-hot-title">
            <text class="search-hot-name">推荐搜索</text>
            <text style="flex: 1"></text>
            <!-- <text class="search-hot-change" @click="searchTopChange">换一换</text> -->
          </div>
          <div class="search-hot-content">
            <text
              class="search-hot-label"
              for="(index,item) in searchHotList"
              @click="searchHotSelected(item)"
            >
              <span class="search-hot-text">{{ item.word }}</span>
            </text>
          </div>
        </list-item>
        <!-- 热门搜索排行榜 -->
        <list-item type="search-top" class="search-top-section">
          <div class="search-top-title">
            <image
              style="width: 140px; height: 35px"
              src="https://img.qdreads.com/v163/<EMAIL>"
            ></image>
            <text class="search-top-all" @click="toPageTop">更多热搜</text>
            <div style="padding-top: 4px">
              <image
                class="top-all-image"
                src="https://img.qdreads.com/v163/<EMAIL>"
              ></image>
            </div>
          </div>
          <div
            style="width:690px;height:118px;align-items:center;margin-top:{{index==0?'0':'40px'}};padding: 0 30px;"
            for="(index,item) in searchTopList"
            @click="bookInfo(item)"
          >
            <text
              style="height: 28px;font-size: 28px;font-weight:bold;width:34px;
          color:{{index == 0?'#f74d5d':index == 1?'#ffa14a':index == 2?'#ffdc27':'#999999'}}"
              >{{ index + 1 }}</text
            >
            <image
              src="{{item.bookIconUrl}}"
              style="
                width: 84px;
                height: 118px;
                margin: 0 20px 0 18px;
                border-radius: 6px;
              "
            ></image>
            <div style="flex-direction: column; justify-content: center">
              <text
                style="
                  height: 32px;
                  font-size: 32px;
                  color: #333333;
                  lines: 1;
                  text-overflow: ellipsis;
                "
                >{{ item.bookName }}</text
              >
              <text
                style="
                  height: 22px;
                  font-size: 22px;
                  color: #adadad;
                  margin-top: 30px;
                "
                >{{ item.bookType }}·{{
                  item.bookNum | readNumHandle
                }}人阅读</text
              >
            </div>
            <block if="index == 0 || index == 1">
              <image
                if="index == 0"
                style="width: 27px; height: 38px; margin-left: auto"
                src="https://img.qdreads.com/image%2F2023-04-12%2F1681292500259_sc_bqb%402x.png"
              ></image>
              <image
                else
                style="width: 27px; height: 38px; margin-left: auto"
                src="https://img.qdreads.com/image%2F2023-04-12%2F1681292500346_sc_bqr%402x.png"
              ></image>
            </block>
          </div>
        </list-item>
      </list>
    <!-- </block> -->

    <!-- <block else> -->
      <!-- 搜索结果页 -->
      <div show="{{!isShowSearchTop}}" class="search-result-section">
        <div class="search-result-total" if="searchResultList.length">
          <text>共</text>
          <text class="result-total-num">{{ total }}</text>
          <text>本电子书</text>
        </div>
        <list class="search-result-list" onscroll="hideKeyboard">
          <block if="searchResultList.length">
            <list-item
              type="lodeMore"
              class="result-item"
              for="(index,item) in searchResultList"
              @click="bookInfo(item, 'serchBookList')"
            >
              <image
                class="result-item-book-pic"
                src="{{item.bookIconUrl}}"
              ></image>
              <div class="result-item-content">
                <text class="result-item-name">{{ item.bookName }}</text>
                <text class="result-item-des">{{ item.bookContenUrl }}</text>
                <text class="result-item-read-num"
                  >{{ item.bookNum | readNumHandle }}人已读</text
                >
              </div>
            </list-item>
          </block>
          <list-item
            class="search-empty-content"
            type="search-empty"
            if="{{!searchResultList.length}}"
          >
            <div class="search-empty" onclick="hideKeyboard">
              <image
                class="search-empty-img"
                src="https://img.qdreads.com/v173/<EMAIL>"
              ></image>
              <text class="search-empty-des">暂时没有内容</text>
            </div>
            <block if="{{guessLikeList}}">
              <novel-list-row-one
                novel-list="{{guessLikeList.data}}"
                title="{{guessLikeList.title}}"
                @comp-click="compClickHandler(guessLikeList.title)"
                @to-recommand-page="toRecommandPage"
              ></novel-list-row-one>
            </block>
          </list-item>
        </list>
      </div>
    <!-- </block>     -->
    <common-loading loading="{{loading}}"></common-loading>
    <!-- 返回腾讯系App的button -->
    <back-app-button
      if="{{$app.$def.isTencentBackButtonVisible}}"
      btn-text="{{$app.$def.tencentBackName}}"
      back-url="{{$app.$def.tencentBackUrl}}"
      package-name="{{$app.$def.tencentBackPkg}}"
    ></back-app-button>
  </div>
</template>
<import name="back-app-button" src="../../components/back-app-button"></import>
<import name="common-loading" src="../../components/loading/index"></import>
<import name="novel-list-row-one" src="../../components/book-list/novel-list-row-one/index.ux"></import>
<script>
export default pageMixin({
  data: () => ({
    loading: false,
    backTitleIsCenter: false, // 状态栏文字居左
    page: 0,
    total: 0,
    keyWord: "", // 搜索关键字
    searchResultList: [], // 搜索结果list
    searchHotList: [], // 热门搜索数据
    isShowSearchTop: true, // 是否显示排行榜  默认显示，在搜索情况下，不显示排行榜，显示搜索结果
    searchTopList: [], // 热门搜索排行榜数据
    pathUrl: '',
    pageDetail: {
      pageUrl: '小说搜索页',
      pageName: '小说搜索页',
      pageCode: 'READ_COMMON',
      pageOrigin: ''
    },
    placeholderKey: '无上神帝',
    placeholderInterval: null, //提示文案轮播器
    placeholderIndex: 0, //提示文案下表
    headerTabIndex: 0, //书城页当前Tab
    guessLikeList: '',
    searchKeyList: [], // 热门搜索数据
  }),
  /**
   * 页面初始化
   */
  onInit() {
    this.getSearchHotList(); // 获取热门推荐数据
    this.getSearchHotTopList(); // 获取热门推荐排行榜数据
    
  },
  onReady() {
    this.$element('search-text').focus({ focus: true })
  },
  /**
   * 页面显示
   */
  onShow() {
    // 更新页面来源
    this.pathUrl = curObj.pathUrl
    
  },
  onHide() {
    
  },
  onDestroy() {
    clearInterval(this.placeholderInterval)
    this.clearInterval = null
  },
  /**
   * 状态栏-返回
   */
  pageBack() {
    COMMON_REPORT_UTILS.page_click_report('返回') //点击上报
    $utils.goBack()
  },

  /**
  * 取消搜索
  */
  searchClear() {
    this.keyWord = ""
    this.isShowSearchTop = true;
    this.searchResultList = []
    COMMON_REPORT_UTILS.page_click_report('取消') //点击上报
  },
  /**
   * 搜索change回调
   */
  keyWordChange(data) {
    this.keyWord = data.value;
    if (!this.keyWord) {
      this.isShowSearchTop = true;
      this.searchResultList = []
      return
    } else {
      this.isShowSearchTop = false;
    }
    COMMON_REPORT_UTILS.page_click_report('搜索', '', '', '', `${this.keyWord.trim()}`) //点击上报
    this.getSearchList()
  },
  enterkeyclickHandler(data) {
    let tempWord = data.value.trim()
    this.keyWord = tempWord ? tempWord : this.placeholderKey
    this.hideKeyboard()
  },
  /**
   * 搜索
   */
  getSearchList() {
    $apis.example.searchData({
      keyword: this.keyWord,
      lastId: ""
    }).then(res => {
      if (res.code == 200) {
        this.isShowSearchTop = false;
        this.total = res.data.total;
        this.searchResultList = res.data.data;
        if (this.searchResultList.length == 0) {
          this.getGuessLikeDataByNoSearchData()
        }
      }
    })
  },
  /**
   * 热门搜索 - 换一换
   */
  searchTopChange() {
    COMMON_REPORT_UTILS.page_click_report('换一换') //点击上报
    this.page++
    this.getSearchHotList()
  },
  /**
   * 热门搜索标签点击选中
   * @param item 选中的热门标签内容
   */
  searchHotSelected(item,evt) {
    this.isShowSearchTop = false;
    this.keyWord = item.word;
    COMMON_REPORT_UTILS.page_click_report('热门搜索', '', `${item.bookId}`) //点击上报
    evt && evt.stopPropagation()
  },
  /**
   * 获取热门推荐数据
   */
  getSearchHotList() {
    $apis.example.searchreData({ page: this.page,version:1}).then(res => {
      if (res.code == 200) {
        this.searchHotList = res.data.keywords;
        this.searchKeyList = res.data.search;
        if (!this.placeholderInterval && this.searchKeyList.length) {
          this.placeholderIndex = 0
          this.placeholderKey = this.searchKeyList[0].word
          this.placeholderInterval = setInterval(() => {
            this.placeholderIndex = this.placeholderIndex >= this.searchKeyList.length ? 0 : this.placeholderIndex
            this.placeholderKey = this.searchKeyList[this.placeholderIndex].word
            this.placeholderIndex++
          }, 5000)
        }
      }
    })
  },
  /**
   * 获取热门推荐排行榜数据
   */
  getSearchHotTopList() {
    this.loading = true
    $apis.example.searchRankBookApi({ sex: this.$app.$def.sex }).then(res => {
      this.loading = false
      if (res.code == 200) {
        this.searchTopList = res.data;
      }
    }).catch(err => {
      this.loading = false
    })
  },
  /**
   * 书籍详情页
   */
  bookInfo(data, type) {
    if (type && type == 'serchBookList') {
      COMMON_REPORT_UTILS.page_click_report('搜索结果列表', '', `${data.bookId}`) //点击上报
    } else {
      COMMON_REPORT_UTILS.page_click_report('热门排行榜', '', `${data.bookId}`) //点击上报
    }
    let params = {
      bookId: data.bookId,
      pathUrl: "搜索页"
    }
    $utils.routetheUrl('/pagesC/Info', params, false)
  },
  /**
   * 书架排行榜
   */
  toPageTop() {
    COMMON_REPORT_UTILS.page_click_report('查看完整榜单') //点击上报
    $utils.routetheUrl('/pagesB/Top', { pathUrl: '搜索页' }, false)
  },
  onBackPress() {
    COMMON_REPORT_UTILS.back_click_report('', '', '跳转页面')
    return false
  },
  /**
 * 获取搜索无结果时 首页tab对应的猜你喜欢
 */
  getGuessLikeDataByNoSearchData() {
    if (this.guessLikeList) return
    $apis.example.bookinfo({
      type: Number(this.headerTabIndex) + 1,
      version: 2,
      only_guess: 1
    }).then(res => {
      if (res.code == 200) {
        this.guessLikeList = res.data.data[0]
      }
    }).catch(err => {
    })
  },
  //阅读人数处理 万级 处理成小数点
  readNumHandle(readNum) {
    if (Number(readNum) > 10000) {
      return (Number(readNum) / 10000).toFixed(1) + '万'
    }
    return readNum
  },
  // 隐藏键盘
  hideKeyboard(evt) {
    if(evt && evt.scrollState && evt.scrollState != 2) return
    if (evt && evt.scrollY === 0) return
    if(!CLICK_UTILS.dom_click_vali_shake(`hideKeyboard_${this.__id__}`,500)) return
    this.$element('search-text') && this.$element('search-text').focus({ focus: false })
  },
  /**
 * 组件内书籍item点击事件监听
 */
  compClickHandler(sectionName, evt) {
    console.log('evt.detail', evt.detail, sectionName)
    this.bookItemClick(evt.detail, sectionName)
  },
  /**
   * 书籍item点击通用事件
   */
  bookItemClick(bookInfo, sectionName) {
    COMMON_REPORT_UTILS.list_click_report('1', [`${bookInfo.bookId}`], `${sectionName}`) //点击上报
    $utils.routetheUrl('/pagesC/Info', { bookId: bookInfo.bookId, pathUrl: "书城页" }, false)
  },
  toRecommandPage(evt) {
    $utils.routetheUrl('/pagesA/recommand-book', {bookIds: evt.detail.bookIds})
  },
})
</script>

<style lang="less">
@import './index.less';
</style>
