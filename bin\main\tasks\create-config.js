const { consoleSplit } = require('../../utils/console')
const path = require('path')
const { getAppBasePath } = require('../../config/app-base')
const fs = require('fs')
const { projectPath } = require('../constant')
const { biggerThanCurVersion } = require('../../utils/get-sdk-version')

const APP_CONFIG_NAME = 'app-config.json'

/**
 * 更新 manifest 配置
 * @param manifestConfig
 * @param sdkVersion
 */
function updateManifest(manifestConfig, sdkVersion) {
  consoleSplit('更新 manifest 配置')
  const manifestPath = path.resolve(projectPath, 'src/manifest.json')
  const originManifest = require(path.resolve(getAppBasePath(), 'manifest.json'))

  deepMerge(originManifest, manifestConfig)
  addReviveActionPage(originManifest, sdkVersion)
  fs.writeFileSync(manifestPath, JSON.stringify(originManifest, null, 2))
  console.log('manifest---start', JSON.stringify(originManifest, null, 2))
  console.log('manifest---end')
}

/**
 * 添加活动页V3
 * 根据 SDK 版本添加活动页
 * @param originManifest
 * @param sdkVersion
 */
function addReviveActionPage(originManifest, sdkVersion) {
  if (biggerThanCurVersion(sdkVersion, '5.1.0')) {
    const routerObj = originManifest.router.pages
    if (!routerObj['pages/Back']) {
      routerObj['pages/Back'] = {
        component: 'index'
      }
    }
  }
  if (biggerThanCurVersion(sdkVersion, '4.1.1') || biggerThanCurVersion(sdkVersion, '5.1.0')) {
    const routerObj = originManifest.router.pages
    if (!routerObj['pages/Revive']) {
      routerObj['pages/Revive'] = {
        component: 'index'
      }
    }
  }
}

function deepMerge(target, source) {
  for (const key in source) {
    if (source.hasOwnProperty(key)) {
      // 如果是对象，递归合并
      if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
        if (!target[key]) {
          target[key] = {} // 如果目标对象中没有该键，创建一个空对象
        }
        deepMerge(target[key], source[key]) // 递归合并子对象
      } else if (Array.isArray(source[key])) {
        // 如果是数组，则添加
        if (!target[key]) {
          target[key] = []
        }
        target[key] = target[key].concat(source[key]).reduce((acc, curr) => {
          if (!acc.find(item => JSON.stringify(item) === JSON.stringify(curr))) {
            acc.push(curr)
          }
          return acc
        }, [])
      } else {
        // 如果不是对象或数组，直接赋值
        target[key] = source[key]
      }
    }
  }
}

// ====================== APP 全局配置 ===========================
function appGlobalConfig(appConfig) {
  consoleSplit('生成 APP 全局配置')
  const appConfigPath = path.resolve(projectPath, `src/${APP_CONFIG_NAME}`)

  fs.writeFileSync(appConfigPath, JSON.stringify(appConfig, null, 2))
  console.log('生成 APP 全局配置成功')
}

module.exports = {
  updateManifest,
  appGlobalConfig
}
