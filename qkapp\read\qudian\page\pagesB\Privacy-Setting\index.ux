<import name="common-header" src="../../components/common-back-header/index"></import>
<template>
  <div class="wrapper">
    <!-- 状态栏 -->
    <common-header
      title="隐私设置"
      text-center="{{backTitleIsCenter}}"
      onback-click="pageBack"
    ></common-header>
    <div class="privacy-setting-content">
      <div class="privacy-item">
        <div class="item-left">
          <text class="text-1">个性化内容推荐</text>
          <text class="text-2" style="line-height:24px;height:24px">关闭后，您将不会看到个性化书籍推荐</text>
        </div>
        <div class="switch-box">
          <switch
            if="{{$app.$def.brand == 'huawei'}}"
            class="switch-view"
            checked="{{privacySettingType1}}"
            style="thumb-color: #ffffff; track-color: #23b382"
            @change="switchChange"
          />
          <apex-switch
            else
            value="{{Boolean(privacySettingType1)}}"
            @change="switchChange"
          ></apex-switch>
        </div>
      </div>
      <div class="privacy-item">
        <div class="item-left">
          <text class="text-1">个性化广告推荐</text>
          <text class="text-2"
            >关闭后，您看到的广告数量不会减少，但广告 相关度会降低</text
          >
        </div>
        <div class="switch-box">
          <switch
            if="{{$app.$def.brand == 'huawei'}}"
            checked="{{privacySettingType2}}"
            style="thumb-color: #ffffff; track-color: #23b382"
            @change="switchChange1"
          />
          <apex-switch
            else
            value="{{Boolean(privacySettingType2)}}"
            @change="switchChange1"
          ></apex-switch>
        </div>
      </div>
    </div>
  </div>
</template>
<import name="apex-switch" src="../../components/apex-ui/switch/index"></import>

<script>
export default {
  public: {
    backTitleIsCenter: false, // 状态栏标题是否居中
    pathUrl: '',
    privacySettingType1: true,
    privacySettingType2: true,
    pageDetail: {
      pageUrl: '隐私设置页',
      pageName: '隐私设置页',
      pageCode: 'READ_COMMON',
      pageOrigin: ''
    },
  },

  onReady() {
    let that = this
    setTimeout(()=>{
      $utils.getStorage('privacySettingType1').then(res => {
        if (res) {
          that.privacySettingType1 = Number(res) == 1 ? true : false
        } else {
          $utils.setStorage('privacySettingType1', '1')
          that.privacySettingType1 = true
        }
      })
      $utils.getStorage('privacySettingType2').then(res => {
        if (res) {
          that.privacySettingType2 = Number(res) == 1 ? true : false
        } else {
          $utils.setStorage('privacySettingType2', '1')
          that.privacySettingType2 = true
        }
      })
    }, 100)

    this.pathUrl = curObj.pathUrl
  },
  switchChange(data) {
    let checked = this.$app.$def.brand == 'huawei' ? data.checked : data.detail.checked
    this.privacySettingType1 = checked
    $utils.setStorage('privacySettingType1', checked ? '1' : '0')
  },
  switchChange1(data) {
    let checked = this.$app.$def.brand == 'huawei' ? data.checked : data.detail.checked
    this.privacySettingType2 = checked
    $utils.setStorage('privacySettingType2', checked ? '1' : '0')
  },
  /**
   * 状态栏返回功能
   */
  pageBack() {
   COMMON_REPORT_UTILS.page_click_report('返回') //点击上报
    $utils.goBack()
  },
  /**
   * 物理返回
   */
  onBackPress() {
    COMMON_REPORT_UTILS.back_click_report('', '', '跳转页面')
  }
}
</script>

<style lang="less">
.wrapper {
  flex-direction: column;
}
.privacy-setting-content {
  flex: 1;
  flex-direction: column;
  padding-top: 10px;
}
.privacy-item {
  width: 750px;
  flex-direction: row;
  padding: 30px;
  align-items: center;
  margin-bottom: 30px;
  .item-left {
    width: 480px;
    flex-direction: column;
    .text-1 {
      font-size: 30px;
      line-height: 30px;
      height: 30px;
      margin-bottom: 30px;
      color: #000;
    }
    .text-2 {
      font-size: 24px;
      line-height: 38px;
      color: #999;
    }
  }
  .switch-view {
    width: 110px;
    height: 54px;
  }
  .switch-box {
    position: absolute;
    right: 30px;
    top: 45px;
  }
}
</style>
