const { findCodeMatch } = require('./base/match')
const { astReplace } = require('./base/code-split')

/**
 * app.ux 替换
 * @param content 文件代码
 * @param oldCode 需要替换的代码
 * @param newCode 替换后的代码
 */
function appUxReplace({ content, oldCode, newCode }) {
  const importCode = findCodeMatch('import commonApp from "./utils/app_ux_public.js"', content)

  if (importCode) {
    return content
  }

  const code = astReplace(content, ast => {
    return [
      {
        start: ast.start,
        end: ast.end,
        type: 'replace',
        content: newCode
      }
    ]
  })

  return code
}

module.exports = appUxReplace
