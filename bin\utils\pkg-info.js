module.exports.parsePkg = pkgInfo => {
  const result = []
  Object.keys(pkgInfo).forEach(path1 => {
    const info1 = pkgInfo[path1]
    Object.keys(info1).forEach(code2 => {
      const info2 = info1[code2]

      result.push({
        category: path1,
        code: code2,
        ...info2
      })
    })
  })

  return result
}

module.exports.parseManifest = manifest => {
  return Object.keys(manifest).map(key => {
    return {
      name: key,
      ...manifest[key]
    }
  })
}
