<template>
  <!-- 一排两个 横向排列 -->
  <div class="novel-list-wrapper">
    <div class="list-title" style="margin-left:30px;">
      <div>
        <div class="title-underline-{{ itemType }}"></div>
        <text class="title-desc">{{ title }}</text>
      </div>
    </div>
    <div style="height:632px;">
      <list class="novel-list">
        <list-item type="novel-item" for="subList" class="novel-item" style="margin-right:{{$idx==subList.length-1?'0':'70'}}px;">
          <block for="(index,item) in $item">
            <div class="nitem-wrapper" @click="compClickHandler(item)">
              <stack style="margin-right:11px;width:98px;">
                <image src="https://img.qdreads.com/v173/sjyy.png" style="width:98px;height:126px;margin-top:7px;"></image>
                <image class="nitem-cover" src="{{item.bookIconUrl}}"></image>
              </stack>
              <text class="num-text" 
                style="height:28px;color:{{$idx==0?'#d5af76':'#333333'}};font-size:28px;font-weight:bold;width:{{$idx>1?34:16}}px;"
              >{{$idx * subLength + index + 1}}</text>
              <div class="nitem-info-wrapper">
                <text class="nitem-info-name" style="width:{{$idx==subList.length-1?'496':'236'}}px;margin-top:2px;">{{item.bookName}}</text>
                <div style="align-items:center;height:22px;margin-top: 16px;">
                  <text style="color:#C8AB80" class="nitem-info-author">{{item.fcate}}</text>
                  <div style="width:4px;height:4px;border-radius:50%;background-color:#adadad;margin:2px 4px 0;"></div>
                  <text class="nitem-info-author">{{item.xstype == 1 ? '完结' : '连载'}}</text>
                  <div style="width:4px;height:4px;border-radius:50%;background-color:#adadad;margin:2px 4px 0;"></div>
                  <text class="nitem-info-author">{{item.bookNum | readNumHandle}}</text>
                  <text class="nitem-info-author">人阅读</text>
                </div>
              </div>
            </div>
          </block>
        </list-item>
      </list>
      <div class="left-border"></div>
      <div class="right-border"></div>
    </div>
  </div>
</template>


<script>
export default {
  props: {
    novelList: {
      type: Array,
      default: []
    },
    title: {
      type: String,
      default: ''
    },
    itemType:{
      type:String,
      default:'default'
    }
  },
  data: {
    subLength: 4,
  },
  compClickHandler(item) {
    if(!CLICK_UTILS.dom_click_vali_shake(`commonNovelRowItem_${this.__id__}`,500)) return

    this.$emit('compClick', { bookId: item.bookId , bookName: item.bookName})
  },
  computed: {
    subList() {
      return TOOLS_UTILS.array2group(this.novelList, this.subLength)
    }
  },
  //阅读人数处理 万级 处理成小数点
  readNumHandle(readNum) {
    if (Number(readNum) > 10000) {
      return (Number(readNum) / 10000).toFixed(1) + '万'
    }
    return readNum
  }
}
</script>

<style lang="less">
.novel-list-wrapper {
  width: 690px;
  /* height: 728px; */
  background-color: #ffffff;
  border-radius: 24px;
  padding: 30px;
  padding-left: 0px;
  flex-direction: column;
}

.title-desc {
    height: 36px;
    width: 146px;
    font-size: 36px;
    font-weight: 600;
    color: #333333;
}

.title-underline-default {
    background: linear-gradient(90deg,#ffd1d1 0%, #ffffff 100%);
    background-repeat: no-repeat;
    height: 8px;
    width: 146px;
    position: absolute;
    top: 28px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}
.title-underline-vip {
    background: linear-gradient(90deg,#e6b598, #f4dcc2 100%);
    background-repeat: no-repeat;
    height: 8px;
    width: 146px;
    position: absolute;
    top: 28px;
    border-top-left-radius: 8px;
    border-bottom-right-radius: 8px;
}

.title-underline-male {
    background: linear-gradient(90deg,#B0D9FF 0%, #ffffff 100%);
    background-repeat: no-repeat;
    height: 8px;
    width: 146px;
    position: absolute;
    top: 28px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}

.title-underline {
    background: linear-gradient(90deg,#ffd1d1 0%, #ffffff 100%);
    background-repeat: no-repeat;
    height: 8px;
    width: 146px;
    position: absolute;
    top: 28px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}

.novel-list {
    width: 100%;
    flex-direction: row;
    height: 632px;

    .novel-item {
        flex-direction: column;
        /* width: 490px; */
        margin-right: 70px;
        margin-left: 23px;
    }
}

.nitem-wrapper {
    margin-top: 33px;

    .nitem-number {
        width: 44px;
        height: 44px;
    }

    .nitem-cover {
        width: 84px;
        height: 118px;
        border-radius: 6px;
        margin-left: 7px;
    }
}

.nitem-info-wrapper {
    flex-grow: 1;
    flex-direction: column;

    .nitem-info-name {
        width: 236px;
        font-size: 26px;
        color: #333333;
        line-height: 37px;
        lines: 2;
        text-overflow: ellipsis;
    }

    .nitem-info-author {
        font-size: 22px;
        color: #ADADAD;
        height: 22px;
        lines: 1;
        text-overflow: ellipsis;
    }
}

.item-bg {
    width: 68px;
    height: 90px;
    background-color: rgba(0,0,0,0.08);
    border-radius: 8px;
    margin-left: 18px;
}

.right-border {
  position: absolute;
  right: 0;
  top: 0;
  width: 44px;
  height: 100%;
  background: linear-gradient(90deg,rgba(255,255,255,0) 0%, #ffffff 100%);
}

.left-border {
  position: absolute;
  left: 0;
  top: 0;
  width: 44px;
  height: 100%;
  background: linear-gradient(90deg,#ffffff 0%, rgba(255,255,255,0) 100%);
}
.num-text {
  margin-top: 8px;
  margin-right: 12px;
}
</style>
