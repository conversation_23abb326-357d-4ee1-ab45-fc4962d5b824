/**
* page : 排行榜页面
* author: yangyang
* date: 2022-03-09
*/
<import name="common-header" src="../../components/common-back-header/index"></import>
<template>
  <div class="top-wrapper">
    <!-- 状态栏 -->
    <common-header
      text-center="{{ backTitleIsCenter }}"
      onback-click="pageBack"
      text-color="#FFFFFF"
    ></common-header>
    <!-- 分类栏 -->
    <div class="top-sort-section" style="margin-top: {{statusBarHeight}}px">
      <div class="top-sort-item" >
        <text class="top-sort-name {{activeType == 1? 'avtive': ''}}" @click="topSortSelected(1)" 
          >男频</text
        >
        <text class="top-sort-name {{activeType == 2? 'avtive': ''}}" @click="topSortSelected(2)"
          >女频</text
        >
      </div>
    </div>

    <!-- 排行榜 -->
    <div class="top-content-section">
      <list class="top-list">
        <list-item type="top-title" style="justify-content:center;">
          <image src="https://img.qdreads.com/v163/sc_wz.png" style="width: 456px;height: 60px;margin:30px 0;"></image>
        </list-item>
        <block>
          <list-item
            type="lodeMore"
            style="width:690px;align-items:center;background-color:#ffffff;"
            class="top-item {{index==0?'index-0':''}} {{index==topList.length - 1?'index-last':''}}"
            for="(index,item) in topList"
            @click="bookInfo(item)"
          >
            <text style="height: 28px;font-size: 28px;font-weight:bold;width:34px;
            color:{{index == 0?'#f74d5d':index == 1?'#ffa14a':index == 2?'#ffdc27':'#999999'}}">{{index + 1}}</text>
            <image src="{{item.bookIconUrl}}" style="width: 84px;height: 118px;margin: 0 20px 0 18px;border-radius:6px;"></image>
            <div style="flex-direction:column;justify-content:center;">
              <text style="height:32px;font-size:32px;color:#333333;lines:1;text-overflow:ellipsis;">{{item.bookName}}</text>
              <text style="height:22px;font-size:22px;color:#adadad;margin-top:30px;">{{item.bookType}}·{{item.bookNum | readNumHandle}}人阅读</text>
            </div>
            <block if="index == 0 || index == 1">
            <image if="index == 0" style="width: 27px;height: 38px;margin-left:auto;" src="https://img.qdreads.com/image%2F2023-04-12%2F1681292500259_sc_bqb%402x.png"></image>
            <image else style="width: 27px;height: 38px;margin-left:auto;" src="https://img.qdreads.com/image%2F2023-04-12%2F1681292500346_sc_bqr%402x.png"></image>
            </block>
          </list-item>
        </block>
        <list-item type="bottom" style="height:30px;width:100%;"></list-item>
      </list>
    </div>
    <common-loading loading="{{loading}}"></common-loading>
    <!-- 返回腾讯系App的button -->
    <back-app-button
      if="{{$app.$def.isTencentBackButtonVisible}}"
      btn-text="{{$app.$def.tencentBackName}}"
      back-url="{{$app.$def.tencentBackUrl}}"
      package-name="{{$app.$def.tencentBackPkg}}"
    ></back-app-button>
  </div>
</template>
<import name="common-loading" src="../../components/loading/index"></import>
<import name="back-app-button" src="../../components/back-app-button"></import>

<script>

export default pageMixin({
  data: () => ({
    loading: false,
    backTitleIsCenter: false, // 状态栏文字居左
    activeType: 1,// 分类高亮  1：男频 2： 女频
    loadMore: true,
    page: 1,
    totalPage: 0,
    topList: [],
    pathUrl: '',
    pageDetail: {
      pageUrl: '排行页',
      pageName: '排行页',
      pageCode: 'READ_COMMON',
      pageOrigin: ''
    },
    statusBarHeight: 40
  }),
  onInit() {
    this.activeType = this.$app.$def.sex;
    this.getTopList()
    
  },
  onReady() {
    this.statusBarHeight = sdk.env.device.statusBarHeight

  },
  onShow() {
    // 更新页面来源
    this.pathUrl = curObj.pathUrl
    

  },
  onHide() {
    
  },
  /**
   * list加载更多（上拉加载）
   */
  scrollbottom() {
    this.page++
    if (this.page > this.totalPage) {
      this.loadMore = false
      $utils.showToast('没有更多数据了！', 0)
      return
    } else {
      this.getTopList(this.page)
    }
  },
  /**
   * 男频、女频选中-更新数据
   * @param i    1：男频  2： 女频
   */
  topSortSelected(i) {
    if (this.activeType == i) return
    COMMON_REPORT_UTILS.page_click_report(`${i == 1 ? '男频' : '女频'}`)
    this.$app.$def.sex = i
    this.$app.$def.tabListType[1] = 0
    this.$app.$def.tabListType[3] = 0
    //向服务器更新性别偏好
    $apis.example.uploadInfo({ sex: this.$app.$def.sex })
    this.activeType = i;
    this.page = 1
    this.topList = []
    this.totalPage = 0
    this.getTopList()
  },

  /**
   * 排行榜数据
   */
  getTopList() {
    this.loading = true
    $apis.example.rankbookInfo({
      page: this.page,
      sex: this.activeType // 分类  1：男频 2： 女频
    }).then(res => {
      this.loading = false
      if (res.code == 200) {
        this.topList = this.topList.concat(res.data.list);
        this.$app.$def.uploadListShow(res.data.list,'排行榜')
        this.page = res.data.page;
        this.totalPage = res.data.totalPage;
      }
    }).catch(err => {
      this.loading = false
    })
  },

  /**
   * 书籍详情页
   */
  bookInfo(data) {
    // COMMON_REPORT_UTILS.page_click_report( `小说`,'',`${data.bookId}`)
    COMMON_REPORT_UTILS.list_click_report('1',[`${data.bookId}`],`小说排行`) //点击上报
    let params = {
      bookId: data.bookId,
    }
    $utils.routetheUrl('/pagesC/Info', params, false)
  },
  /**
   * 返回
   * 
   */
  pageBack() {
    $utils.goBack()
    COMMON_REPORT_UTILS.page_click_report( `返回`)
  },
  onBackPress() {
    COMMON_REPORT_UTILS.back_click_report('','','跳转页面')
    this.goBackclick(true)
    return true
  },
  /**
   * 返回
   * @param {Boolean} isNeedReportBackPress 是否上报物理返回 默认false
   */
  goBackclick(isNeedReportBackPress = false) {
    if (this.pushId != null && this.pushId != undefined) {
      $utils.routeReplacetheUrl('/pagesA/Main', { selectIndex: 1, pathUrl: "分类页" })
    } else {
      $utils.goBack()
    }
  },
  //阅读人数处理 万级 处理成小数点
  readNumHandle(readNum) {
    if (Number(readNum) > 10000) {
      return (Number(readNum) / 10000).toFixed(1) + '万'
    }
    return readNum
  }

})
</script>

<style lang="less">
@import './index.less';
</style>
