<import
  name="book-item"
  src="../../components/book-detail/book-item.ux"
></import>
<import name="inner-ad-sub" src="../../components/inner-group/inner-ad-template/index.ux"></import>
 
<template>
  <div class="page-wrapper rank-wrapper">
    <div
      class="top-wrapper"
      style="{{bgImg}}"
    >
      <div class="top-bg-div">
        <div class="custom-title-bar">
          <div style="align-items:center;">
            <div class="img-wrapper" @click="goBack">
              <image src="../../assets/v100/back-icon.png"></image>
            </div>
            <text>{{ pageTitle }}</text>
          </div>
        </div>
        <text class="top-title">{{ pageTitle }}</text>
        <text class="top-date">每日 12:00更新</text>
        <div class="top-tips">
          <image src="../../assets/v100/tips.png"></image>
          <text>根据阅读用户的增长指数排序</text>
        </div>
      </div>
    </div>
    <list id="book" class="book-list">
      <list-item type="book" for="{{rankList}}">
        <book-item item="{{$item}}" @item-click="itemClick"></book-item>
      </list-item>
      <list-item type="bottom" style="width:100%;height:30px;"></list-item>
    </list>
    <inner-ad-sub id="rank-ad"></inner-ad-sub>
  </div>
</template>

<script>
import { bookListData } from '../../assets/data/book-list.js'

export default {
  public: {
    type: ''
  },
  private: {
    rankList: [],
    pageTitle: '',
    bgImg: {
      backgroundImage: ''
    },
  },
  async onInit() {
    this.pageTitle = this.type == 'new' ? '热门榜单' : '精选排行'
    this.bgImg.backgroundImage = this.type == 'new' ? 
      'https://img.sytangshiwl.top/jingyue/swiper/book-4.png' : 
      'https://img.sytangshiwl.top/jingyue/swiper/book-5.png';
    let arr = [...bookListData];
    $utils.shuffleArrayWithSeed(arr, this.type == 'new' ? 12 : 24);
    this.rankList = arr;
    setTimeout(() => {
      this.$broadcast('showSubAd')
    }, 100);
  },
  itemClick(evt) {
    $utils.routetheUrl('subPages/book-detail', {
      info: JSON.stringify(evt.detail.item)
    })
  },
  goBack() {
    $utils.goBack()
  }
}
</script>

<style lang="less">
@import '../../assets/styles/index.less';
.rank-wrapper {
  background-color: @white;
}

.description {
  padding: @app-padding;
  width: 100%;
  margin-bottom: @gap-2;
  background-color: @white;
  .flex-box-mixins(column, flex-start, flex-start);
  .description-title {
    .text-title;
    margin-bottom: @gap-2;
  }
  .g22kjdgy {
    color: #ffffff;
  }
}

.top-wrapper {
  width: 100%;
  height: 440px;
  .section-container;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  align-items: center;
}

.top-bg-div {
  width: 100%;
  height: 440px;
  align-items: center;
  .section-container;
  background-color: rgba(0,0,0,0.50);
}

.top-title {
  height: 44px;
  color: #ffffff;
  font-size: 44px;
  margin-top: 40px;
  font-weight: bold;
}

.top-date {
  height: 26px;
  color: #ffffff;
  font-size: 26px;
  margin-top: 30px;
}

.top-tips {
  margin-top: auto;
  margin-bottom: 20px;
  align-items: center;

  image {
    width: 24px;
    height: 24px;
    margin-right: 4px;
  }

  text {
    height: 26px;
    color: rgba(255, 255, 255, 0.6);
    font-size: 26px;
  }
}

.book-list {
  padding: 0 @app-padding;
  flex: 1;
}
</style>
