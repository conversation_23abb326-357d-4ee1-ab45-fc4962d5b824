const fs = require('fs')
const appUxReplace = require('./app-ux-replace')
const funcReplace = require('./func-replace')
const funcDelete = require('./func-delete')
const attrDelete = require('./attr-delete')
const stringReplace = require('./string-replace')
const { stringDeleteOneLine } = require('./string-delete')
const tagReplace = require('./tag-replace')
const pageMixAdd = require('./page-mix-add')
const appMixAdd = require('./app-mix-add')
/**
 * 修改文件内容
 * @param {string} file 文件路径
 * @param {Modification[]} modifications 修改配置
 * @returns {any}
 */
function modifyFile(file, modifications) {
  const originCode = fs.readFileSync(file, 'utf-8')

  let failCodeInfo = []
  const newContent = modifications.reduce((content, { type, oldCode, newCode, desc }, index) => {
    const baseHandlerArgs = { content, oldCode, newCode, failCodeInfo, desc }
    switch (type) {
      case 'replace':
        return stringReplace({ ...baseHandlerArgs, file })
      case 'deleteFun':
        return funcDelete(baseHandlerArgs)
      case 'appUxReplace':
        return appUxReplace(baseHandlerArgs)
      case 'replaceFun':
        return funcReplace(baseHandlerArgs)
      case 'deleteAttr':
        return attrDelete(baseHandlerArgs)
      case 'delete':
        return stringDeleteOneLine(baseHandlerArgs)
      case 'replaceTag':
        return tagReplace(baseHandlerArgs)
      case 'addPageMixin':
        return pageMixAdd(baseHandlerArgs)
      case 'addAppMixin':
        return appMixAdd(baseHandlerArgs)
      default:
        return content
    }
  }, originCode)

  fs.writeFileSync(file, newContent)

  console.log(`修改文件结束：${file}`, failCodeInfo)

  return failCodeInfo
}

module.exports = {
  modifyFile
}

/*
删除文件中的带有指定变量的行
*/
function removeLinesWithVariable(content, variableName) {
  // Split content into lines
  const lines = content.split('\n')

  // Filter out lines that contain the variable name
  const filteredLines = lines.filter(line => !line.includes(variableName))

  // Join the lines back together
  return filteredLines.join('\n')
}

/*
删除某个标签及内部内容
*/
function removeTagAndContent(content, startTag, endTag) {
  const lines = content.split('\n')
  let insideTag = false
  const filteredLines = []

  for (let line of lines) {
    if (line.includes(startTag)) {
      insideTag = true
    }
    if (!insideTag) {
      filteredLines.push(line)
    }
    if (line.includes(endTag)) {
      insideTag = false
    }
  }

  return filteredLines.join('\n')
}
