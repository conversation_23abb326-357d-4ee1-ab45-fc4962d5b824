const { default: inquirer } = require('inquirer')
const { copyFile } = require('../bin/utils')
const path = require('path')
async function main() {
  const answers = await inquirer.prompt([
    {
      type: 'input',
      name: 'choice',
      message: '输入版本号（数字类型）'
    }
  ])

  const version = answers.choice

  const from = path.resolve(__dirname, './version-template')
  const to = path.resolve(__dirname, `../bin/config/business/v${version}`)
  copyFile(from, to)
  console.log('复制完成')
}

main()
