const { default: inquirer } = require('inquirer')
inquirer.registerPrompt('autocomplete', require('../utils/auto-complete').default)
const { pinyin } = require('pinyin-pro')
const { parsePkg } = require('./pkg-info')
const fs = require('fs')
const path = require('path')
const pkgInfo = require('../config/pkg-info')
const businessConfig = require('../config/business/config')
const { BUSINESS, OPERATION } = require('../store/constant')
const { getStore, setStore } = require('../store')
const duijieConfig = require('../config/duijieConfig')

async function getAppName(inputApp) {
  const result = parsePkg(pkgInfo)

  if (inputApp) {
    return result.find(v => v.name === inputApp)
  }

  let defaultAppName = ''
  const appNamePath = path.resolve(__dirname, '../store/app.txt')
  if (fs.existsSync(appNamePath)) {
    const appName = fs.readFileSync(appNamePath, 'utf-8')
    if (appName) {
      defaultAppName = appName
    }
  }
  const answers = await inquirer.prompt([
    {
      type: 'autocomplete',
      name: 'choice',
      message: '选择一个应用',
      source: (answersSoFar, input) => {
        // 如果输入为空，则显示所有选项；否则过滤显示匹配的选项
        input = input || ''
        return new Promise(resolve => {
          const results = result
            .filter(v => {
              const handlePinyin = pinyin(v.name, { toneType: 'none' }).replace(/\s/g, '').includes(input.toLowerCase())
              const handleFirst = pinyin(v.name, { pattern: 'first', toneType: 'none' })
                .replace(/\s/g, '')
                .includes(input.toLowerCase())

              return handlePinyin || handleFirst
            })
            .map(v => v.name)

          results.splice(5, 0, new inquirer.Separator())
          results.push(new inquirer.Separator())
          resolve(results)
        })
      },
      default: defaultAppName
    }
  ])

  console.log(answers)
  const selectedAppName = answers.choice || ''

  fs.writeFileSync(appNamePath, selectedAppName)

  return result.find(v => v.name === selectedAppName)
}

async function questionBrand() {
  let defaultBrand = ''
  const brandPath = path.resolve(__dirname, '../store/brand.txt')
  if (fs.existsSync(brandPath)) {
    const brand = fs.readFileSync(brandPath, 'utf-8')
    if (brand) {
      defaultBrand = brand
    }
  }

  const answers = await inquirer.prompt([
    {
      type: 'list',
      name: 'choice',
      message: '选择一个厂商',
      choices: ['xiaomi', 'vivo', 'oppo', 'hw', 'honor'],
      default: defaultBrand
    }
  ])

  console.log(answers)
  fs.writeFileSync(brandPath, answers.choice || '')
  return answers.choice || ''
}

/**
 * 业务代码升级选择
 * @return {Promise<string>}
 */
async function questionBusinessVersion() {
  const defaultBus = getStore(BUSINESS) || undefined
  const answers = await inquirer.prompt([
    {
      type: 'select',
      name: 'version',
      message: '请选择升级的版本',
      choices: businessConfig.map(v => v.name),
      default: defaultBus
    }
  ])

  if (answers.version) {
    setStore(BUSINESS, answers.version)
  }

  return businessConfig.find(v => v.name === answers.version).file
}

/**
 * 执行命令选择
 * @returns
 */
async function questionSelection() {
  const defaultSelections = getStore(OPERATION) || undefined
  const selection = await inquirer.prompt([
    {
      type: 'select',
      name: 'selection',
      message: '选择要进行的操作',
      choices: duijieConfig.map(v => v.name),
      default: defaultSelections
    }
  ])
  if (selection.selection) {
    setStore(OPERATION, selection.selection)
  }
  return duijieConfig.find(v => v.name === selection.selection).operation
}

async function questionSdkVersion() {
  const result = await fetchSdkGitLabBranches()

  let defaultSDK = ''
  const SdkPath = path.resolve(__dirname, '../store/sdk.txt')
  if (fs.existsSync(SdkPath)) {
    const SdkBranch = fs.readFileSync(SdkPath, 'utf-8')
    if (SdkBranch) {
      defaultSDK = SdkBranch
    }
  }

  const answers = await inquirer.prompt([
    {
      type: 'autocomplete',
      name: 'choice',
      message: '选择SDK分支',
      source: (answersSoFar, input) => {
        // 如果输入为空，则显示所有选项；否则过滤显示匹配的选项
        input = input || ''
        return new Promise(resolve => {
          const results = result.filter(branch => {
            return branch.replace(/\./g, '').includes(input.toLowerCase())
          })
          input && results.push(input)
          resolve(results)
        })
      },
      default: defaultSDK
    }
  ])

  console.log(answers)
  const selectedSdk = answers.choice || ''

  fs.writeFileSync(SdkPath, selectedSdk)

  return selectedSdk
}

async function fetchSdkGitLabBranches() {
  try {
    let allBranches = [];
    let page = 1;
    let hasMoreData = true;

    while (hasMoreData) {
      const response = await fetch(
        `https://gitlab.ghfkj.cn/api/v4/projects/112/repository/branches?per_page=100&page=${page}&search=^release`,
        {
          headers: {
            'PRIVATE-TOKEN': '**************************'
          }
        }
      )
      const data = await response.json()

      if (data.length === 0) {
        hasMoreData = false;
      } else {
        allBranches = allBranches.concat(data);
        page++;
      }
    }

    const regex = /^release-v\d(?:\.(\d+))+[a-z]?$/
    const releaseBranches = allBranches.filter(branche => regex.test(branche.name)).map(branche => branche.name)

    return sortVersions(releaseBranches)
  } catch (e) {
    console.log(e)
    return ''
  }
}

/**
 * 根据版本字符串的最后四个数字组件对数组进行降序排序。
 *
 * @param {string[]} versions - 版本字符串数组，格式为 'release-vX.X.X.X'。
 * @returns {string[]} 一个新的排序后的版本字符串数组。
 */
function sortVersions(versions) {
  return versions.sort((a, b) => {
    const versionA = a.replace('release-v', '').split('.').map(Number)
    const versionB = b.replace('release-v', '').split('.').map(Number)

    // 填充版本号，使其长度一致
    while (versionA.length < 4) versionA.push(0)
    while (versionB.length < 4) versionB.push(0)

    // 逐位比较版本号
    for (let i = 0; i < 4; i++) {
      if (versionA[i] !== versionB[i]) {
        return versionB[i] - versionA[i]
      }
    }
    return 0 // 相等
  })
}

module.exports = {
  getAppName,
  questionBrand,
  questionBusinessVersion,
  questionSdkVersion,
  questionSelection
}
