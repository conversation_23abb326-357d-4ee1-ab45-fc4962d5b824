const { getAppBasePath } = require('../../config/app-base')
const path = require('path')
const fs = require('fs')
const parser = require('@babel/parser')
const traverse = require('@babel/traverse').default

let versionNameIndex = { start: 0, end: 0 }
let versionCodeIndex = { start: 0, end: 0 }

function updateVersion(brand = 'xiaomi') {
  const appBasePath = getAppBasePath()
  const appConfigPath = path.resolve(appBasePath, 'brand-config.js')
  let appConfig = fs.readFileSync(appConfigPath, 'utf8')

  parseBrandConfig(appConfig, brand)
  const originVersionName = appConfig.slice(versionNameIndex.start, versionNameIndex.end)
  console.log('原版本号', originVersionName)

  if (versionNameIndex.start === 0) {
    console.log('没有找到版本号,需手动添加')
    return
  }

  const { versionName, versionCode } = getVersion(originVersionName)

  if (versionCodeIndex.start < versionNameIndex.start) {
    // Update versionName first since it appears earlier in the file
    appConfig = appConfig.slice(0, versionNameIndex.start) + versionName + appConfig.slice(versionNameIndex.end)
    // Then update versionCode
    appConfig = appConfig.slice(0, versionCodeIndex.start) + versionCode + appConfig.slice(versionCodeIndex.end)
  } else {
    // Update versionCode first since it appears earlier in the file
    appConfig = appConfig.slice(0, versionCodeIndex.start) + versionCode + appConfig.slice(versionCodeIndex.end)
    // Then update versionName
    appConfig = appConfig.slice(0, versionNameIndex.start) + versionName + appConfig.slice(versionNameIndex.end)
  }

  console.log('更新版本号', versionName, versionCode)
  fs.writeFileSync(appConfigPath, appConfig)
  console.log('更新版本号成功')
}

function parseBrandConfig(appConfig, brand) {
  const ast = parser.parse(appConfig, {
    sourceType: 'module',
    plugins: ['jsx']
  })
  traverse(ast, {
    ObjectProperty(path) {
      if (path.node.key.name === brand && path.node.value.properties) {
        path.node.value.properties.forEach(prop => {
          if (prop.key.name === 'manifest') {
            prop.value.properties.forEach(manifestProp => {
              if (manifestProp.key.name === 'versionName') {
                versionNameIndex.start = manifestProp.value.start
                versionNameIndex.end = manifestProp.value.end
                console.log('versionNameIndex', versionNameIndex)
              }
              if (manifestProp.key.name === 'versionCode') {
                versionCodeIndex.start = manifestProp.value.start
                versionCodeIndex.end = manifestProp.value.end
                console.log('versionCodeIndex', versionCodeIndex)
              }
            })
          }
        })
      }
    }
  })

  return ast
}

function getVersion(version) {
  // Split version string into components
  const [x, y, z] = version.replace(/['"]/g, '').split('.').map(Number)
  // Increment z component
  let newZ = z + 1
  let newY = y
  let newX = x

  // Handle overflow from z to y
  if (newZ >= 10) {
    newZ = 0
    newY += 1
  }

  // Format version strings
  const versionName = `'${newX}.${newY}.${newZ}'`
  const versionCode = `${newX}0${newY}0${newZ}000`

  return {
    versionName,
    versionCode
  }
}

module.exports = { updateVersion }
