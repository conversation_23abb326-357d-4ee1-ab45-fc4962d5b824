<import name="header" src="../../components/header"></import> 

<template>
  <div class="privacy-wrapper">
    <header title="" @back-click="backIconHandle"></header>
    <div class="web_container">
      <web id="web" class="web" src="{{webUrl}}"></web>
    </div>
  </div>
</template>

<script>
export default {
  public: {
    backTitleIsCenter: false,
    type: 2, // 隐私政策类型  1： 用户协议  2：隐私政策
    webUrl: "",
    pathUrl: ''
  },
  onInit() {
    this.$page.setTitleBar({text: this.type == 1? '用户协议': '隐私政策'});
    let isShowCompany = sdk.tactics.getCustomParams('show_company_name') !== '0';
    const { UserAgreement, UserAgreementV2, PrivacyPolicy, PrivacyPolicyV2 } = $getAppConfig('protocolAddress');
    switch (Number(this.type)) {
      case 1:
        this.webUrl = isShowCompany ? UserAgreement : UserAgreementV2
        break
      case 2:
        this.webUrl = isShowCompany ? PrivacyPolicy : PrivacyPolicyV2
        break
    }
  },
  backIconHandle() {
    $utils.goBack();
  }
}
</script>

<style lang="less">
@import './index.less';
</style>
