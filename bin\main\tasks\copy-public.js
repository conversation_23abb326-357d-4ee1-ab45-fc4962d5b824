const { toolPages, storyPages, sdks } = require('../../config/tool-copy-des')
const path = require('path')
const { copyFile } = require('../../utils')
const { projectPath, SDK_PATH, SDK_PROJECT_NAME } = require('../constant')
const packageJson = require(path.resolve(projectPath, 'package.json'))

/**
 * 复制公共组件和SDK
 * @param appType
 */
function copyPublicFileAndSDK(appType, sdkVersion) {
  const pages = appType === 'tool' ? toolPages : storyPages
  const isSDK = packageJson.name === SDK_PROJECT_NAME
  const sdkProjectPath = isSDK ? projectPath : path.resolve(SDK_PATH, sdkVersion)

  pages.forEach(([source, target]) => {
    const from = path.resolve(sdkProjectPath, source)
    const to = path.resolve(projectPath, target)
    copyFile(from, to)

    console.log(`复制成功：${from} -> ${to}`)
  })

  if (!isSDK) {
    sdks.forEach(([source, target]) => {
      const from = path.resolve(sdkProjectPath, source)
      const to = path.resolve(projectPath, target)
      copyFile(from, to)

      console.log(`复制成功：${from} -> ${to}`)
    })
  }
}

module.exports = copyPublicFileAndSDK
