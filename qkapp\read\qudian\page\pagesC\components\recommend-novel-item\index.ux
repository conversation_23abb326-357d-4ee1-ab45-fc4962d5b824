<template>
    <div class="guess-like">
      <div class="guess-title-content">
        <text class="guess-title">猜你喜欢</text>
        <div class="guess-more" onclick="moreClick">
          <text>更多</text>
          <div style="padding-top:2px;">
            <image class="guess-more-img" src="https://img.qdreads.com/v163_2/info-icon-new.png"></image>
          </div>
        </div>
      </div>
      <div class="guess-book-content">
        <block for="(index,item) in recommendBookList">
          <div
            class="guess-book"
            style="margin-left:{{index%3 == 0 ? '0':'45'}}px"
            onclick="bookItemClick(item)"
          >
            <image src="{{item.bookIconUrl}}"></image>
            <text>{{item.bookName}}</text>
          </div>
        </block>
      </div>
    </div>
</template>
<script>
export default {
  props: {
    bookId: {
      type: Number | String,
      default: 0
    }
  },
  data: {
    recommendBookList: []
  },
  onInit() {
    this.getRecommendBook()
  },
  getRecommendBook() {
    $apis.example.bookRecommendApi({
      type: 1,
      page: 1,
      page_size: 7
    }).then(res => {
      if (res.code == 200) {
        res.data = res.data.filter(item => item.bookId != this.bookId).slice(0, 6)
        this.recommendBookList = res.data
        this.$app.$def.uploadListShow(res.data,'详情推荐')
      }
    }).catch(err => {
    })
  },
  bookItemClick(item){
    COMMON_REPORT_UTILS.list_click_report('1',[`${item.bookId}`],'详情推荐') //点击上报
    this.$emit('recommendAction', {eventName:'bookClick', data: item})
  },
  moreClick(){
    this.$emit('recommendAction', {eventName:'moreClick'})
  }
}
</script>
<style lang="less">
.guess-like {
  width: 100%;
  padding: 30px;
  flex-direction: column;
  .guess-title-content {
    width: 100%;
    align-items: center;
    justify-content: space-between;
    .guess-title {
      height: 36px;
      font-size: 36px;
      font-weight: 600;
      color: #333333;
    }
    .guess-more {
      align-items: center;
      height: 30px;
      justify-content: center;
      text {
        height: 24px;
        font-size: 24px;
        font-weight: 400;
        color: #959595;
        text-align: center;
      }
      .guess-more-img {
        width: 24px;
        height: 24px;
        object-fit: contain;
      }
    }
  }
  .guess-book-content {
    width: 100%;
    flex-wrap: wrap;
    .guess-book {
      width: 200px;
      height: 376px;
      flex-direction: column;
      margin-top: 30px;
      image {
        width: 200px;
        height: 266px;
        border-radius: 10px;
      }
      text {
        width: 100%;
        font-size: 28px;
        font-weight: 500;
        color: #333333;
        margin-top: 24px;
        lines: 2;
        text-overflow: ellipsis;
      }
    }
  }
}
.read-recommond {
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 24px;
}
</style>

