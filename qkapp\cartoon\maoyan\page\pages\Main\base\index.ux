<import name="book-shelf" src="../../components/bookshelf"></import>
<import name="library" src="../../components/library"></import>
<import name="category" src="../../components/category"></import>
<import name="mine" src="../../components/mine"></import>
<import name="privacy-pop" src="../../components/privacy-pop"></import>
<import name="teen-mode" src="../../components/teen-mode"></import>
<import name="complaint-icon" src="../../components/complaint-icon/index.ux"></import><!-- 投诉按钮 -->
<import name="exit-icon" src="../../components/exit-icon/index.ux"></import>
<import name="common-agreement" src="../../components/common-agreement/index"></import>
<import name="custom-pop-group" src="../../cy-sdk/action/components/custom-pop-group/index.ux"></import>
<!-- @position: comp import end -->

<template>
  <stack id="stack">
  <div class="page-wrapper">
    <div class="native-boost-group">
      <custom-pop-group
        onevent-watch="nativeBoostGroupEventDispatchHandler"
        onappear="popComponmentAppearHandler"
      ></custom-pop-group>
    </div>

    <tabs index="{{activeTab}}" onchange="changeTab">
      <tab-content style="background-color:#fff">
        <library refresh-data="{{refreshData}}" @change-select-index="changeSelectIndex"></library>
        <book-shelf
          shelf-list="{{shelfList}}"
          onupdate-shelf="updateShelf"
        ></book-shelf>
	      <category></category>
        <mine is-in-teen-mode="{{isInTeenMode}}" @event-handle="{{mineEventHandle}}" active-tab="{{activeTab}}"></mine>
      </tab-content>
      <tab-bar mode="fixed" class="tab-bar">
        <div class="tab-item" for="tabList">
          <image
            src="../../assets/v100/{{activeTab === $idx ? $item.activatedIcon : $item.icon}}.png"
          ></image>
          <text class="tab-title">{{ $item.title }}</text>
        </div>
      </tab-bar>
    </tabs>
    <teen-mode
      if="{{isShowTeenMode && !isShowPrivacy}}"
      @event-watch="teenModeHandle"
    ></teen-mode>
    <common-agreement if="{{tacticsStatus}}"></common-agreement>
    <complaint-icon if="{{tacticsStatus}}" top="300"></complaint-icon>
    <exit-icon if="{{tacticsStatus}}"></exit-icon>
    </div>    
  </stack>
</template>

<script>
import { bookListData } from '../../assets/data/book-list.js'
import appUtils from '../../utils/app_page_utils.js'
/* @position: import end */

export default pageMixin({
  /* @position: export start */
  private: {
    /* @position: private start */
    isShowComplaintButton:false,
    isShowComplaintPop:false,
    tabList: [
      {
        title: '书城',
        icon: 'store-unselect',
        activatedIcon: 'store-selected'
      },
      {
        title: '书架',
        icon: 'shelf-unselect',
        activatedIcon: 'shelf-selected'
      },
      {
        title: '分类',
        icon: 'category-unselect',
        activatedIcon: 'category-selected'
      },
      {
        title: '我的',
        icon: 'mine-unselect',
        activatedIcon: 'mine-selected'
      }
    ],
    activeTab: 0,
    defaultShelfList: bookListData.slice(2, 4),
    shelfList: [],
    isShowPrivacy: false,
    isShowTeenMode: false,
    isInTeenMode: false,
    refreshData: true,
    bookList: [],
    isShowBoostComp: true,
    isJump: false,
    isPageHide: false,
    pageDetail: {
      pageRoute: '/pages/Main',
      pageType: '一级页面',
      pageName: '首页',
      pageUrl: '首页',
      pageCode: 'page_home',
      pageOrigin: ""
    },
    /* @position: private end */
  },
  public: {
    /* @position: public start */
    intent: 0,
    pathUrl: '',
    activeTab: 0,
    tacticsStatus: 0,
    /* @position: public end */
  },
  
  async onInit() {
    /* @position: onInit start */
    this.activeTab = Number(this.activeTab);

    await appUtils.page_ad_comp_init.call(this, true,'home')
    
    this.$watch('activeTab', 'selectIndexChangeHandler');
    let cartoonPublishId = sdk.tactics.getCustomParams('cartoon_publish_id');
    const list = $utils.filterBookList(bookListData, cartoonPublishId.split(','));
    this.bookList = $utils.shuffleArrayWithSeed(list, 1);
    $utils.getStorage('bookshelf').then(value => {
      // 从storage获取
      if (!value) {
        this.shelfList = this.bookList.slice(0, 2);
        $utils.setShelfList(this.shelfList, true) // 保存到全局变量和storage
      } else {
        this.shelfList = JSON.parse(value)
        $utils.setShelfList(this.shelfList) // 保存到全局变量
      }
    });
    $utils.getStorage('is_show_teen_modal').then(res => {
      if (res && res == 1) {
        this.isShowTeenMode = false
      } else {
        this.isShowTeenMode = true
      }
    })
    $utils.getStorage('is_in_teen_mode').then(res => {
      if (res && res == 1) {
        this.isInTeenMode = true
      } else {
        this.isInTeenMode = false
      }
    })
    $utils.getStorage('privacyAgree').then(res => {
      if (res && res == 1) {
        this.isShowPrivacy = false
      } else {
        this.isShowPrivacy = true
      }
    })

    this.isShowComplaintButton = true
    /* @position: onInit end */
  },

  onShow() {
    /* @position: onShow start */
    this.isPageHide = false;
    if (this.isJump) {
      this.isJump = false
    }
    AD_UTILS.viewShowHandler()
    /* @position: onShow end */
  },
  onHide() {
    /* @position: onHide start */
    this.isJump = true
    AD_UTILS.viewHideHandler()
    /* @position: onHide end */
  },

  /* -------------------SelfCustomEvent------------------ */
  changeTab(evt) {
    if (this.activeTab === evt.index) return;
    let index = evt.index === undefined ? 1 : evt.index;
    this.activeTab = index;
    sdk.tactics.setQuery(`activeTab=${this.activeTab}`)
    COMMON_REPORT_UTILS.page_click_report(this.tabList[this.activeTab].title);
  },
  selectIndexChangeHandler(nVal, oVal) {
    POP_TOOLS.commonFunc2PatchPopShow({ actionCode: 'PAGE_ENTRY', code: '', customAction: {} })
  },
  //接受自定义通用组件控制器派发的数据
  nativeBoostGroupEventDispatchHandler(evt) {
    LOG('VIEW', `nativeBoostGroupEventDispatchHandler======>`, evt)
    appUtils.native_boost_event_dispatch_handler.call(this,evt)
  },
  onBackPress() {
    return sdk.backPress.handleBackPress()
  },

  updateShelf(param) {
    this.shelfList = param.detail.shelfList
    $utils.setShelfList(this.shelfList, true) // 保存到全局变量和storage
  },
  changeSelectIndex(evt) {
    this.activeTab = evt.detail.index
  },
  privacyHandle() {
    this.isShowPrivacy = false
    $utils.setStorage('privacyAgree', 1)
  },
  teenModeHandle(evt) {
    let action = evt.detail.action
    switch (action) {
      case 'agree':
        this.isShowTeenMode = false
        this.changeTeenMode(true)
        this.refreshData = false
        let timer = setTimeout(() => {
          this.refreshData = true
          clearTimeout(timer)
        }, 300)
        break
      case 'close':
        this.isShowTeenMode = false
        break
      default:
        break
    }
    $utils.setStorage('is_show_teen_modal', 1)
  },
  changeTeenMode(isIn) {
    this.isInTeenMode = isIn
    $utils.setStorage('is_in_teen_mode', isIn ? 1 : '')
  },
  mineEventHandle(evt) {
    this.changeTeenMode(evt.detail.checked)
  },
  updatePageHide(evt) {
    this.isPageHide = evt.detail.data;
  },
  /* @position: export end */
})
</script>

<style lang="less">
@import '../../assets/styles/index.less';
.tab-bar {
  background-color: @white;
  .border-top-mixins();
  padding: 15px 0;
  height: 110px;
  .tab-item {
    flex: 1;
    .flex-box-mixins(column, center, center);
    .iconfont {
      font-size: 50px;
      color: @text-grey;
      &:active {
        color: @brand;
      }
    }
    .g22kjdgy {
      color: #ffffff;
    }
    .tab-title {
      font-size: 20px;
      color: @text-grey;
      &:active {
        color: @brand;
      }
    }

    image {
      width: 58px;
      height: 58px;
    }
  }
}

.banner-view-class {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}
.native-boost-group{
  position: absolute;
  top: 0px;
  left: 0px;
  width: 750px;
  height: 100%;
}
</style>
