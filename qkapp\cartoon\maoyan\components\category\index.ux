<import name="header" src="../../components/header"></import>

<template>
  <div class="page-column">
    <header title="分类" show-icon="{{false}}"></header>
    <div class="tab-wrapper">
      <div class="text-wrapper" for="{{tabs}}" @click="tabClick($idx)">
        <text class="{{activeIndex==$idx?'active-tab':''}}">{{$item}}</text>
        <div if="{{activeIndex==$idx}}"></div>
      </div>
    </div>
    <list class="list-container padding-app">
      <list-item type="book-item" class="book-item" for="{{item in subList}}">
        <div
          class="item-wrapper"
          for="{{item}}"
          @click="goToDetail($item)"
          style="margin-left:{{$idx%3==0?0:14}}px;"
        >
          <div class="list-image">
            <image src="{{$item.image}}"></image>
          </div>
          <text class="book-title">{{ $item.title }}</text>
        </div>
      </list-item>
    </list>
  </div>
</template>

<script>
import { bookListData } from '../../assets/data/book-list.js';

export default {
  data: {
    tabs: ['全部', '连载', '完结'],
    activeIndex: 0,
    bookList: [],
    concatList: []
  },
  
  computed: {
    subList() {
      return $utils.array2group(this.bookList, 3);
    }
  },

  onInit() {
    this.concatList = [...bookListData];
    $utils.shuffleArrayWithSeed(this.concatList, 1);
    this.bookList = this.concatList;
  },

  goToDetail(item) {
    this.$emit('updatePageHide', { data: true });
    COMMON_REPORT_UTILS.page_click_report('漫画书');
    $utils.routetheUrl('subPages/book-detail', { info: item })
  },

  tabClick(index) {
    this.activeIndex = index;
    if (index == 0) {
      this.bookList = this.concatList;
    } else {
      this.bookList = this.concatList.filter(item => item.status == index);
    }
    COMMON_REPORT_UTILS.page_click_report(this.tabs[index]);
  }
}
</script>

<style lang="less">
@import '../../assets/styles/index.less';

.tab-wrapper {
  width: 702px;
  margin: 20px 24px 0;
  border-bottom: 1px solid #f3f3f3;
  padding-bottom: 20px;
}

.text-wrapper {
  width: 117px;
  height: 66px;
  flex-direction: column;
  align-items: center;

  text {
    margin-top: 20px;
    font-size: 26px;
    color: @text-black;
    height: 26px;
  }

  > div {
    width: 37px;
    height: 6px;
    background-color: @brand;
    border-radius: 10px;
    margin-top: 6px;
  }

  .active-tab {
    color: @brand;
    font-weight: bold;
  }
}


.book-item {
  .list-item;

  .list-image {
    width: 224px;
    height: 300px;
    border: 1px solid #f0f0f0;
    border-radius: 14px;

    image {
      width: 100%;
      height: 100%;
      border-radius: 14px;
    }
  }
}

.item-wrapper {
  flex-direction: column;
}

.book-title {
  color: #333333;
  height: 30px;
  font-size: 30px;
  margin-top: @gap-2;
  lines: 1;
  text-overflow: ellipsis;
}

.book-author {
  height: 24px;
  color: #999999;
  font-size: 24px;
  margin-top: 14px;
  lines: 1;
  text-overflow: ellipsis;
}

.list-image {
  width: 224px;
  height: 300px;
  border: 1px solid #f0f0f0;
  border-radius: 14px;

  image {
    width: 100%;
    height: 100%;
    border-radius: 14px;
  }
}
</style>
