/**
* page : 隐私页面
* author: yangyang
* date: 2022-03-04
*/
<import name="common-header" src="../../components/common-back-header/index"></import>
<template>
  <div class="privacy-wrapper">
    <!-- 状态栏 -->
    <common-header
      title="{{type == 1? '用户协议': '隐私政策'}}"
      text-center="{{backTitleIsCenter}}"
      onback-click="pageBack"
    ></common-header>
    <div class="web_container">
      <web id="web" class="web" src="{{webUrl}}"></web>
    </div>
    <!-- 返回腾讯系App的button -->
    <back-app-button
      if="{{$app.$def.isTencentBackButtonVisible}}"
      btn-text="{{$app.$def.tencentBackName}}"
      back-url="{{$app.$def.tencentBackUrl}}"
      package-name="{{$app.$def.tencentBackPkg}}"
    ></back-app-button>
  </div>
</template>
<import name="back-app-button" src="../../components/back-app-button"></import>

<script>
import webview from '@system.webview'
export default pageMixin({
  data: {
    backTitleIsCenter: false,
    type: 1, // 隐私政策类型  1： 用户协议  2：隐私政策
    webUrl: "",
    pathUrl: ''
  },
  onInit() {
    this.type = this.type
    switch (Number(this.type)) {
      case 1:
        this.webUrl = process.env.NODE_ENV == 'development' ? 'http://devapi.qdreads.com/licensing.html' : 'https://api.qdreads.com/licensing.html'
        break
      case 2:
        this.webUrl = process.env.NODE_ENV == 'development' ? 'http://devapi.qdreads.com/privacy.html' : 'https://api.qdreads.com/privacy.html'
        break
    }
  },
  onShow() {
    // 更新页面来源
    this.pathUrl = curObj.pathUrl
    

  },
  onHide() {
    
  },
  pageBack() {
    this.$page.finish()
  },
  onBackPress() {
    console.info(`触发：onBackPress`)
  }
})
</script>

<style lang="less">
@import './index.less';
</style>
