import $ajax from './ajax'

/**
 * @desc 在实际开发中，您可以将 baseUrl 替换为您的请求地址前缀；
 *
 * 已将 $apis 挂载在 global，您可以通过如下方式，进行调用：
 * $apis.example.getApi().then().catch().finally()
 *
 * 备注：如果您不需要发起请求，删除 apis 目录，以及 app.ux 中引用即可；
 */
let baseUrl = process.env.NODE_ENV == 'development' ? 'http://devapi.inheweb.com/' : 'https://api.inheweb.com/';

export default {

    loginApi(data) {
        return $ajax.post(`${baseUrl}${login_api}`, data)
    },
    //获取商品列表
    goodsList(data) {
        return $ajax.post(`${baseUrl}coupon/storage-coupon/list`, data)
    },
    //搜索商品
    searchGoods(data) {
        return $ajax.post(`${baseUrl}coupon/coupon/search`, data)
    },
    //搜索商品详情
    goodsDetail(data) {
        return $ajax.post(`${baseUrl}coupon/coupon/goods_detail`, data)
    },
    //列表商品详情
    listGoodsDetail(data) {
        return $ajax.post(`${baseUrl}coupon/storage-coupon/goods_detail`, data)
    },
}
