    .catalogue-container {
        width: 100%;
        height: 100%;
        position: fixed;
        left: 0;
        top: 0;
        flex-direction: column;
        .content-empty {
            width: 100%;
            flex: 1;
        }
        .catalogue-content {
            width: 100%;
            height: 90%;
            background-color: #ffffff;
            flex-direction: column;
            border-top-left-radius: 36px;
            border-top-right-radius: 36px;
            .catalogue-title {
                height: 120px;
                align-items: center;
                justify-content: space-between;
                padding: 0 30px;
                flex-shrink: 0;
                width: 100%;
                border-bottom: 1px solid #F0F0F0;
                > text {
                    font-size: 34px;
                    height: 34px;
                    font-weight: bold;
                    color: #333333;
                }
            }
            .catalogue-item {
                height: 110px;
                align-items: center;
                flex-direction: row;
                padding-left: 30px;
                padding-right: 30px;
                .item-title {
                    flex: 1;
                    height: 108px;
                    font-size: 32px;
                }
                .item-icon {
                    width: 22px;
                    height: 24px;
                    margin-left: 40px;
                }
                .item-title-1 {
                    color: #999999;
                    font-weight: 500;
                }
                .item-title-2 {
                    color: #f11212;
                    font-weight: 700;
                }
                .item-title-3 {
                    color: #333333;
                }
            }
        }
    }

    .showEditorAnimation {
        animation-name: showEditorAnimation;
        animation-duration: 300ms;
    }

    .hideEditorAnimation {
        animation-name: hideEditorAnimation;
        animation-duration: 300ms;
    }

    .showMaskAnimation {
        animation-name: showMaskAnimation;
        animation-duration: 100ms;
    }

    .hideMaskAnimation {
        animation-name: hideMaskAnimation;
        animation-duration: 100ms;
    }

    @keyframes showEditorAnimation {
        from {
            transform: translateY(650px);
        }
        to {
            transform: translateY(0px);
        }
    }

    @keyframes hideEditorAnimation {
        from {
            transform: translateY(0px);
        }
        to {
            transform: translateY(650px);
        }
    }

    @keyframes showMaskAnimation {
        from {
            opacity: 0;
        }
        to {
            opacity: 0.3;
        }
    }

    @keyframes hideMaskAnimation {
        from {
            opacity: 0.3;
        }
        to {
            opacity: 0;
        }
    }