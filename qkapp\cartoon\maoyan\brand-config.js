/**
 * 对接配置
 * 分为 单 APP 配置和 分厂商配置，如果两者都配置了同一个字段，则以app配置为准
 * @type {{app: {}, brand: {}}}
 */
const config = {
  // 系统数据
  app: {
    // 系统内部数据，都写在这里，之后会统一放在 $config 变量下
    // 支持dev和prod，如果一致，则不用写 dev prod，直接写一个即可

    // app 秘钥
    appSecreteKey: {
      dev: 'tpZ9Jw91uQ6az9au',
      prod: '4OFlOJIKVrS0O-E1'
    },
    // 协议地址
    protocolAddress: {
      UserAgreement: 'https://img.sytangshiwl.top/maoyan/licensing.html',
      PrivacyPolicy: 'https://img.sytangshiwl.top/maoyan/privacy.html',
      UserAgreementV2: '',
      PrivacyPolicyV2: ''
    },
    // 小说阅读页
    middleAltLight: '', // 中插白天背景图
    middleAltDark: '', // 中插黑夜背景图
    imageOss: '', // 阅读页图片OSS地址
    // 优量汇APP ID
    ylhAppId: ''
  },

  // 打包过程的配置
  build: {
    // 项目打包过程，替换APP路径，eg '/page' ''
    copyPathReplace: ['', '']
  },

  // manifest 配置
  manifest: {},

  // 分厂商配置，如果有相同字段，则以分厂商配置为准
  brand: {
    xiaomi: {
      manifest: {
        router: {
          pages: {
            'pages/Action': {
              launchMode: 'singleTask'
            }
          }
        },
        versionName: '2.5.3',
        versionCode: 205030
      }
    },
    hw: {
      manifest: {}
    },
    oppo: {
      manifest: {}
    },
    vivo: {
      manifest: {}
    },
    honor: {
      manifest: {}
    }
  }
}

module.exports = config
