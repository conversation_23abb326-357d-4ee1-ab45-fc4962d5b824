<template>
  <!-- template里只能有一个根节点 -->
  <div>
    <div class="novel-item" @click="bookItemClickHandler">
      <image src="{{novelData.bookIconUrl}}" class="novel-pic"></image>
      <div class="novel-detail">
        <div class="nd-name-wrapper">
          <text class="novel-title">{{ novelData.bookName }}</text>
          <text class="ly-ily" if="{{novelData.score && isShowScore}}">
            <span>{{ novelData.score | formatScore }}</span>
            <span style="font-size:22px;font-weight:normal">分</span>
          </text>
        </div>
        <text class="novel-desc">{{ novelData.bookContenUrl }}</text>
        <div class="novel-bottom-detail">
          <div class="novel-author">
            <text style="height: 22px;font-size: 22px;color: #adadad;">{{novelData.bookType}}</text>
            <div style="width:4px;height:4px;border-radius:50%;background-color:#adadad;margin:2px 4px 0;"></div>
            <text style="height: 22px;font-size: 22px;color: #adadad;">{{ novelData.view | readNumHandle }}人阅读</text>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
    props: {
    novelData: {
      type: Object,
      default: null
    },
    isShowType: {
      type: Boolean,
      default: false
    },
    isShowScore: {
      type: Boolean,
      default: false
    }
  },
  data: {
    list: [],
    randomCount: 0
  },
  onInit() {
    this.randomCount = Math.ceil(Math.random() * 5)
  },
  bookItemClickHandler() {
    this.$emit('compClick', { bookId: this.novelData.bookId })
  },
  formatScore(value) {
    if (!value) return ''
    return value / 10
  },
  //阅读人数处理 万级 处理成小数点
  readNumHandle(readNum) {
    if (Number(readNum) > 10000) {
      return (Number(readNum) / 10000).toFixed(1) + '万'
    }
    return readNum
  }
}
</script>

<style lang="less">
@import './index.less';
</style>
