<template>
  <div class="wrapper">
    <!-- 签到赚金币 -->
    <div type="sign-wrapper" class="sign-wrapper">
      <stack style="align-items:center;">
        <image
          class="sign-tab"
          src="https://img.qdreads.com/v155/welfare/welfare-tab.png"
        ></image>
        <text class="sign-label">连续签到，奖励更高</text>
      </stack>
      <div class="sign-list-container">
        <list class="sign-list">
          <list-item
            type="signItem"
            class="sign-item "
            style="margin-left: {{index == 0 ?'0':'16'}}px;"
            for="(index, item) in signTaskList"
          >
            <stack style="justify-content: center">
              <block>
                <text if="{{todayIndex == index}}" class="sign-day-text">今日</text>
                <image else class="sign-day-img" src="https://img.qdreads.com/v155/welfare/TS-{{index + 1}}@2x.png"></image>
              </block>
              <image
                class="sign-welfare-img"
                src="{{item.icon}}"
              ></image>
            </stack>
            <div if="{{item.isGet == 1}}" class="sign-mask">
              <text>已签到</text>
            </div>
          </list-item>
        </list>
      </div>
      <text class="sign-check-button {{signToday == 1 ? 'sign-check-button-disable' : ''}}" onclick="signCheckClickHandler">{{ signToday == 1 ? '已签到' : '立即签到' }}</text>
    </div>
  </div>
</template>

<script>
export default {
  data: {
    title: "Hello World. Quickapp Component."
  },

  props: {
    signTaskList: {
      default: [],
      type: Array
    },
    signToday: {
      default: 0,
    }
  },
  computed: {
    todayIndex() {
      if (this.signTaskList.length > 0) {
        let noSginFirst = this.signTaskList.findIndex((item) => item.isGet == 2 )
        // 如果已签到 已签到的最后一天为今天 未签到 则为未签到的第一天
        return this.signToday == 1 ? noSginFirst - 1 : noSginFirst
      }
      return 0
    }
  },

  onInit() { 
    LOG('WALFARE SIGN', this.signToday)
  },
  signCheckClickHandler() {
    if (this.signToday === null || this.signToday == 1) return
    if (!CLICK_UTILS.dom_click_vali_shake(`signCheckClickHandler${this.__id__}`, 500)) return
    this.$emit('signClick')
  },
  numberToWord(num) {
    let word = ['第一天', '第二天', '第三天', '第四天', '第五天', '第六天', '第七天']
    if (num < word.length) {
      return word[num]
    }
    return ''
  },
}
</script>

<style lang="less">
.wrapper {
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 750px;
}
.sign-wrapper {
  width: 702px;
  height: 424px;
  border-radius: 20px;
  flex-direction: column;
  align-items: center;
  background-color: #ffffff;
  .sign-tab {
    width: 702px;
    height: 94px;
  }
}

.sign-list-container {
  width: 642px;
  .sign-list {
    width: 100%;
    height: 158px;
    flex-direction: row;
  }
}

.sign-item {
  width: 120px;
  height: 158px;
  flex-direction: column;
  border-radius: 10px;
  background-color: #fff1dc;
  margin-left: 16px;
  align-items: center;
  .sign-day-img {
    width: 88px;
    height: 28px;
    margin-top: 14px;
  }
  .sign-day-text {
    width: 88px;
    height: 28px;
    font-size: 28px;
    font-weight: 700;
    text-align: center;
    color: #fa7d37;
    margin-top: 14px;
  }
}
.sign-item-welfare {
  background: linear-gradient(180deg, #fff1dc, #ffbbb1 100%);
}

.sign-welfare-img {
  margin-top: 38px;
  width: 120px;
  height: 120px;
}

.sign-mask {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  background-color: rgba(134, 134, 134, 0.5);
  border-radius: 10px;
  text {
    width: 100%;
    text-align: center;
    font-size: 28px;
    font-weight: 500;
    color: #ffffff;
  }
}

.sign-check-button {
  width: 518px;
  height: 96px;
  margin-top: 44px;
  background: linear-gradient(90deg, #fb8720, #f84a41 100%);
  border-radius: 200px;
  font-size: 36px;
  font-weight: 500;
  text-align: center;
  color: #fff8eb;
}
.sign-check-button-disable{
  background: linear-gradient(90deg,#dfdfdf, #d1d1d1 100%);
}

.sign-label {
  margin-left: 210px;
  font-size: 24px;
  height: 28px;
  color: #999999;
}
</style>
