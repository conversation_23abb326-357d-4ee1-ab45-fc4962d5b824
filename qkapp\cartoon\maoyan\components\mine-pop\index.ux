<template>
  <div class="prompt-pop-modal">
    <image
      class="closeIcon"
      src="https://img.sytangshiwl.top/jingyue/v100/closeIcon.png"
      @click="closeModal(1)"
    ></image>
    <div class="content" style="background-image: {{backgroundImagePath}}">
      <div>
        <slot></slot>
      </div>
      <image @click="closeModal(2)" src="https://img.sytangshiwl.top/jingyue/v100/pop_but.png"></image>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    backgroundImagePath: String
  },
  data: {},
  onInit() {},
  closeModal(type) {
    this.$emit('eventWatch', { eventName: type == 1 ? 'close' : 'sure' })
  }
}
</script>

<style lang="less">
@import '../../assets/styles/index.less';

.prompt-pop-modal {
  position: fixed;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.85);
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .closeIcon {
    width: 56px;
    height: 56px;
    margin-bottom: 23px;
  }

  .content {
    width: 530px;
    height: 590px;
    background-color: #d9d9d9;
    background-size: cover;
    border-radius: 40px;
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;

    div {
      margin-bottom: 74px;
    }

    image {
      width: 390px;
      height: 128px;
      margin-bottom: 6px;
    }
  }
}
</style>
