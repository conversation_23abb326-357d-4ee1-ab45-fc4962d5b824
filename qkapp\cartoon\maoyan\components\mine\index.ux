<import name="my-switch" src="../switch/index"></import>
<import name="header" src="../header"></import>
<import name="mine-pop" src="../mine-pop"></import>
<import name="inner-ad" src="../inner-group/inner-ad/index.ux"></import>
 
<template>
  <div class="page-column wrapper">
    <div class="page-column tip-top">
      <header title="个人中心" show-icon="{{false}}"></header>
      <div class="top-wrapper">
        <div class="avatar-wrapper">
          <image class="avatar" src="https://img.sytangshiwl.top/jingyue/v100/avatar.png"></image>
          <text class="text-title">叮～漫友</text>
          <div class="sign-in" @click="onSignIn">
            <image src="https://img.sytangshiwl.top/jingyue/v100/xmk_qd.png"></image>
            <text>{{ isSignInFlag ? '签到' : '已签' }}</text>
          </div>
          <div class="new-pop" if="isSignInFlag "><text>新人专属</text></div>
        </div>
        <stack class="free-card" @click="onLimited">
          <image src="https://img.sytangshiwl.top/jingyue/v100/new-user.png"></image>
          <text>{{
            remainingTime == 0 ? '全站不限风格漫画免费看' : remainingTimeString
          }}</text>
        </stack>
      </div>
    </div>
    <div class="grid-wrapper">
      <div class="grid-title">
        <text class="text-title">常用功能</text>
      </div>
      <div class="item-wrapper" for="{{list}}" @click="mineItemClick($item)">
        <image class="item-icon" src="{{$item.icon}}"></image>
        <text class="item-text">{{ $item.text }}</text>
        <image
          if="{{$item.routerPush}}"
          class="arrow-right"
          src="https://img.sytangshiwl.top/jingyue/v100/arrow-right.png"
        ></image>
        <block else>
          <text if="{{$item.content}}" class="item-content">{{
            $item.content
          }}</text>
          <block if="{{$item.code == 'teen'}}">
            <text class="teen-text">{{
              isInTeenMode ? '已开启' : '未开启'
            }}</text>
            <my-switch
              value="{{c_isInTeenMode}}"
              @change="onSwitchChange"
            ></my-switch>
          </block>
        </block>
      </div>
    </div>
    <inner-ad id="user-ad"></inner-ad>
      <a
      @click="goIcp"
      style="text-align:center;color: #3c87e9;text-decoration: underline;margin-top:50px;"
      >桂ICP备2022006283号-9K</a
    >

    <mine-pop
      if="{{islimitedPop}}"
      @event-watch="limitedHandle"
      background-image-path="https://img.sytangshiwl.top/jingyue/v100/limited_bj.png"
    >
      <text>全站全部风格漫画免费看</text>
    </mine-pop>
    <mine-pop
      if="{{isSignInPop}}"
      @event-watch="signInHandle"
      background-image-path="https://img.sytangshiwl.top/jingyue/v100/signIn_bj.png"
    >
      <text>获得限免卡时长<span style="color:#ff0000;">+1天</span></text>
    </mine-pop>
  </div>
</template>

<script>
import app from '@system.app'

export default {
  props: {
    isInTeenMode: {
       default: false
    },
    activeTab: {
       default: 0
    },
  },
  data: {
    list: [
      {
        icon: 'https://img.sytangshiwl.top/jingyue/v100/help.png',
        text: '投诉与反馈',
        routerPush: true,
        url: 'subPages/complaintUnit'
      },
      {
        icon: 'https://img.sytangshiwl.top/jingyue/v100/prevaicy.png',
        text: '隐私政策',
        routerPush: true,
        url: 'subPages/privacy',
        params: { type: 2 }
      },
      {
        icon: 'https://img.sytangshiwl.top/jingyue/v100/license.png',
        text: '用户协议',
        routerPush: true,
        url: 'subPages/privacy',
        params: { type: 1 }
      },
      {
        icon: 'https://img.sytangshiwl.top/jingyue/v100/version.png',
        text: '版本号',
        routerPush: false,
        url: '',
        content: 'V' + app.getInfo().versionName
      },
      {
        icon: 'https://img.sytangshiwl.top/jingyue/v100/teen-mode.png',
        text: '青少年模式',
        routerPush: false,
        url: '',
        code: 'teen'
      },
      {
        icon: 'https://img.sytangshiwl.top/jingyue/v100/teen-mode.png',
        text: '观看广告（支持一下）',
        routerPush: false,
        url: '',
        code: 'video'
      },
    ],
    islimitedPop: false,
    isSignInPop: 0,
    deadline: 0,
    isSignInFlag: true,
    c_isInTeenMode:false
  },
  computed: {
    remainingTime() {
      if (this.deadline - Date.now() > 0) {
        return this.deadline - Date.now()
      } else {
        return 0
      }
    },
    remainingTimeString() {
      return this.millisecondsToDaysAndHours(this.remainingTime)
    }
  },
  onInit() {
    $utils.getStorage('deadline').then(value => {
      if (value) {
        this.deadline = JSON.parse(value)
      }
    })
    $utils.getStorage('isSignInPop').then(value => {
      this.isSignInFlag = value && JSON.parse(value) == 1 ? false : true
    })
    this.$watch('isInTeenMode', 'isInTeenModeChange')
    this.$watch('activeTab', 'activeTabChange')
  },
  activeTabChange(newVal){
    console.log('activeTabChange',newVal);
    if(newVal === 3){
      this.$broadcast('showNativeAd')
    }
  },
  isInTeenModeChange() {
    setTimeout(()=>{
      this.c_isInTeenMode = this.isInTeenMode
    },200)
  },
  async mineItemClick(item) {
    let url = item.url
    if (url) $utils.routetheUrl(url, item.params || {})
    if(item.code == 'video'){
      this.$broadcast('showVideoAd')
    }
    COMMON_REPORT_UTILS.page_click_report(item.text);
  },
  onSwitchChange(evt) {
    this.$emit('eventHandle', { checked: evt.detail.checked })
  },
  onLimited() {
    $utils.getStorage('islimitedPop').then(value => {
      this.islimitedPop = value && JSON.parse(value) == 1 ? false : true
    })
    COMMON_REPORT_UTILS.page_click_report('限免卡');
  },
  onSignIn() {
    $utils.getStorage('isSignInPop').then(value => {
      this.isSignInPop = value && JSON.parse(value) == 1 ? false : true
    })
    COMMON_REPORT_UTILS.page_click_report('签到按钮');
  },
  limitedHandle(evt) {
    this.islimitedPop = false
    if (evt.detail.eventName == 'sure') {
      if (this.deadline == 0) {
        this.deadline = Date.now() + 604800000 // 七天
      } else {
        let newTime = this.deadline
        this.deadline = 0
        this.deadline = newTime + 604800000
      }
      $utils.setStorage('islimitedPop', 1)
      $utils.setStorage('deadline', this.deadline)
      COMMON_REPORT_UTILS.page_click_report('限免弹窗正向');
    } else {
      COMMON_REPORT_UTILS.page_click_report('限免弹窗负向');
    }
  },
  signInHandle(evt) {
    this.isSignInPop = false
    if (evt.detail.eventName == 'sure') {
      this.isSignInFlag = false
      if (this.deadline == 0) {
        this.deadline = Date.now() + 86400000 // 一天
      } else {
        let newTime = this.deadline
        this.deadline = 0
        this.deadline = newTime + 86400000
      }
      $utils.setStorage('isSignInPop', 1)
      $utils.setStorage('deadline', this.deadline)
      COMMON_REPORT_UTILS.page_click_report('签到弹窗正向');
    } else {
      COMMON_REPORT_UTILS.page_click_report('签到弹窗负向');
    }
  },
  // 将毫秒转换为x天x小时
  millisecondsToDaysAndHours(milliseconds) {
    const totalHours = Math.floor(milliseconds / (1000 * 60 * 60))
    const days = Math.floor(totalHours / 24)
    const hours = totalHours % 24

    return `剩余${days}天${hours}小时`
  },
  goIcp() {
    require('@system.webview').loadUrl({
      url: "https://beian.miit.gov.cn/"
    })
  },
}
</script>

<style lang="less">
@import '../../assets/styles/index.less';

.wrapper {
  background-color: #f8f9fb;
}

.top-wrapper {
  width: 100%;
  height: 325px;
  .section-container;
}

.tip-top {
  background-image: url('https://img.sytangshiwl.top/jingyue/v100/bj.png');
  background-size: cover;
}

.avatar-wrapper {
  align-items: center;
  margin-top: 70px;
  margin-left: 40px;

  .avatar {
    width: 122px;
    height: 122px;
    background-color: #d9d9d9;
    border-radius: 50%;
    margin-right: 30px;
  }

  .sign-in {
    position: absolute;
    right: 0px;
    width: 146px;
    height: 99px;
    justify-content: center;
    align-items: center;
    background-image: url('https://img.sytangshiwl.top/jingyue/v100/grzx_qd_bj.png');
    background-size: cover;

    image {
      width: 50px;
      height: 40px;
    }

    text {
      font-size: 26px;
    }
  }

  .new-pop {
    position: absolute;
    top: 0px;
    right: 0px;
    width: 104px;
    height: 41px;
    justify-content: center;
    align-items: center;

    background-image: url('https://img.sytangshiwl.top/jingyue/v100/grzx_xrzs.png');
    background-size: cover;

    text {
      margin-top: -4px;
      font-size: 20px;
      color: #ffffff;
    }
  }
}

.free-card {
  width: 702px;
  height: 88px;
  margin-left: @app-padding;
  margin-top: 40px;

  image {
    width: 100%;
    height: 100%;
  }

  text {
    height: 100%;
    color: #999999;
    font-size: 28px;
    margin-left: 250px;
  }
}

.grid-wrapper {
  .section-container;
  margin-top: 25px;
  width: 702px;
  border-radius: 30px;
  background-color: #ffffff;
  margin-left: 24px;
}

.grid-title {
  width: 100%;
  height: 84px;
  padding: @app-padding;
  align-items: center;
}

.item-wrapper {
  width: 100%;
  height: 100px;
  padding: @app-padding;
  align-items: center;

  .item-icon {
    width: 44px;
    height: 44px;
  }

  .item-text {
    font-size: 30px;
    height: 30px;
    color: @text-black;
    margin-left: 10px;
    margin-right: auto;
  }

  .arrow-right {
    width: 22px;
    height: 22px;
  }

  .item-content {
    height: 30px;
    color: @text-grey;
    font-size: 30px;
  }
}

.teen-text {
  height: 30px;
  color: #999999;
  font-size: 30px;
  margin-right: 10px;
}
</style>
