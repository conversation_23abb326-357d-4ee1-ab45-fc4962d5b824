const { findCodeMatch } = require('./base/match')
const { astReplace } = require('./base/code-split')
const traverse = require('@babel/traverse').default

/**
 * 通过方法名，完成方法的整个内容替换
 * @param content 文件代码
 * @param oldCode 需要替换的代码
 * @param newCode 替换后的代码
 */
function funcReplace({ content, oldCode, newCode }) {
  const insertedCode = findCodeMatch(newCode, content)

  if (insertedCode) {
    return content
  }

  const code = astReplace(content, ast => {
    const result = []
    traverse(ast, {
      ObjectMethod(path) {
        const methodNode = path.node

        if (methodNode.key && methodNode.key.name === oldCode) {
          result.push({
            start: methodNode.start,
            end: methodNode.end,
            type: 'replace',
            content: newCode
          })
        }
      }
    })

    return result
  })

  return code
}

module.exports = funcReplace
