/**
* page : 排行榜页面样式
* author: ya<PERSON><PERSON>
* date: 2022-03-09
*/
.top-wrapper {
    flex-direction: column;
    width: 750px;
    height: 100%;
    background-image: url(https://img.qdreads.com/v163/sc_bj.png);
    background-repeat: no-repeat;
    background-size: 100% auto;
    background-color: #f4f5f7;

    .top-sort-section {
        justify-content: center;
        align-items: center;
        position: absolute;
        height: 88px;
        width: 386px;
        left: 184px;
        top: 0;

        .top-sort-item {
            background-color: rgba(255,255,255,0.12);
            border-radius: 50px;

            .top-sort-name {
                height: 34px;
                font-size: 34px;
                font-weight: bold;
                text-align: center;
                color: #ffffff;
                width: 192px;
                height: 64px;
                
                border-radius: 50px;
            }
            .avtive {
                border: 1px solid #ffffff;
            }
        }
    }
    // 排行榜
    .top-content-section {
        flex-direction: column;
        flex: 1;
        align-items: center;

        .top-list {
            flex: 1;
            width: 690px;
            
            // 搜索空数据页面
            .top-empty {
                flex-direction: column;
                align-items: center;
                .top-empty-img {
                    width: 348px;
                    height: 348px;
                }
                .top-empty-des {
                    font-size: 24px;
                    color: #999;
                    margin-top: 24px;
                }
            }
        }
    }
}

.top-item {
    padding-left: 30px;
    padding-right: 30px;
    padding-top: 40px;
}

.index-0 {
    padding-top: 30px;
    border-top-left-radius: 24;
    border-top-right-radius: 24;
}

.index-last {
    padding-bottom: 30px;
    border-bottom-left-radius: 24;
    border-bottom-right-radius: 24;
}