<template>
  <!-- template里只能有一个根节点 --> 
  <div>
    <div class="user-wrapper">
      <div class="user-header-title" style="margin-top: {{statusBarHeight}}px;">
        <!-- <image src="https://img.qdreads.com/v155/comon/user-title.png"></image> -->
      </div>
      <!-- 头像、赚书劵 -->
      <div class="user-header-section">
        <div class="user-head">
          <image if="{{userInfo.sex}}"
            src="{{userInfo.sex == 1? 'https://img.qdreads.com/v163_2/grzx_tx_n.png' : 'https://img.qdreads.com/v163_2/grzx_tx_v.png'}}"
          ></image>
          <image else src="https://img.qdreads.com/v155/comon/user-header-default.png"></image>
        </div>
        <div class="user-info">
          <text class="user-info-phone">{{
            userInfo.loginStatus ? userInfo.phone : '游客' + userInfo.id
          }}</text>
          <!-- <text class="user-info-id" if="userInfo.loginStatus"
            >ID: {{ userInfo.id }}</text
          > -->
          <div if="!userInfo.loginStatus" style="align-items: center;">
            <text
              class="user-info-login-btn"
              @click="toLogin"
              >登录</text
            >
            <div style="padding-top: 2px;">
              <image style="width:24px;height:24px;" src="https://img.qdreads.com/v155/user/right.png"></image>
            </div>
          </div>
          
        </div>
        <div class="user-info-empty"></div>
      </div>
      <!-- 年卡会员 -->
      <!-- 金币信息 -->
      <div class="user-coupon-section">
        <div class="fdc">
          <text class="user-coupon-number">{{ userInfo.coupon }}</text>
          <text class="user-coupon-dec">金币余额</text>
        </div>
        <div class="fdc">
          <text class="user-coupon-number">{{userInfo.todayBookTickets}}</text>
          <text class="user-coupon-dec">今天收益</text>
        </div>
        <div class="user-make-coupon" @click="toWelfarePage">
          <text class="user-make-coupon-text">赚金币</text>
          <image class="right-icon" src="https://img.qdreads.com/v163/user/jump-red.png"></image>
        </div>
        <div class="user-make-coupon-line"></div>
      </div>
      <!-- 常用功能菜单 -->
      <div class="user-menu-section">
        <mine-grid @menu-item-click="menuItemClick"></mine-grid>
      </div>
      <div if="{{isShowAddDesktop}}" style="width:100%;height:{{addDesktopHeight + 20}}px"></div>
    </div>
  </div>
</template>
<import name="common-header" src="../components/common-header/index"></import>
<import name="mine-grid" src="../components/mine-grid/index.ux"></import>

<script>
export default {
  data: {
    loading: false,
    isLogin: 0, // 登录状态  0：未登录  1：已登录
    userInfo: {}, // 用户信息
    isSwiperReload: true, //swiper是否重新渲染
    statusBarHeight: 40,
  },
  onInit() {
    this.statusBarHeight = sdk.env.device.statusBarHeight

    this.$watch('selectIndex', 'selectIndexChangeHandler')
    this.$watch('viewShowTimes', 'viewShowTimesChangeHandler')
  },
  selectIndexChangeHandler(newVal, oldVal) {
    if (newVal == 4) {
      this.getUserInfo(); // 拉取用户信息
      this.$app.$def.tabListType[newVal] = 1
    }
  },
  viewShowTimesChangeHandler(newVal, oldVal) {
    if (newVal == 1) return
    // if (this.selectIndex !== 4) return
    this.selectIndexChangeHandler(4, 100)
  },
  props: {
    selectIndex: {
      type: undefined,
      default: 4
    },
    viewShowTimes: {
      type: Number,
      default: 0
    },
    backPopShow: {
      type: Number,
      default: 0
    },
    isShowAddDesktop: {
      default: false
    },
    addDesktopHeight: {
      default: 80
    },
  },
  // 去登录入口  跳转登录页
  toLogin() {
    COMMON_REPORT_UTILS.page_click_report('登录')
    this.$emit('pageJumpHandle',{pageUrl:'/pagesB/Login',pageData:{pathUrl: "我的页"}})
  },
  // 赚金币   跳转福利页
  toWelfarePage(name) {
    COMMON_REPORT_UTILS.page_click_report('赚金币')
    this.$emit('pageJumpHandle',{pageUrl:'/pagesA/WelfareNew',pageData:{pathUrl: "我的页"}})
  },
  // 获取用户信息
  getUserInfo() {
    this.loading = true
    $apis.example.userDataInfo({}).then(res => {
      this.loading = false
      if (res.code == 200) {
        this.userInfo = res.data;
        this.$app.$def.isVip = res.data.vipStatus; // // 将会员状态保存全局
        this.$app.$def.loginStatus = res.data.loginStatus; // 将登录状态保存全局
      }
    }).catch(err => {
      this.loading = false
    })
  },
  /**
   * 个人中心   菜单列表点击
   *  @param evt 自定义事件参数
   */
  menuItemClick(evt) {
    let item = evt.detail.item;
    COMMON_REPORT_UTILS.page_click_report(`${item.name}`)
    if (item.path == 'WELFARE') {
      this.$emit('pageJumpHandle', { pageUrl: '/pagesA/WelfareNew', pageData: { pathUrl: "我的页" } })
    } else {
      var params = {
        pathUrl: "我的页",
        loginStatus: this.$app.$def.loginStatus
      }
      this.$emit('pageJumpHandle', { pageUrl: item.path, pageData: params })
    }
  },
  checkLogin() {
    let needLogin = Number(sdk.tactics.getCustomParams('is_recharge_login'))
    if (needLogin == 1 || this.$app.$def.auditOnline == 1) {
      if (this.$app.$def.loginStatus == 0) {
        $utils.showToast("为避免书币丢失，请先登录", 0)
        this.toLogin()
        return false
      }
    }
    return true
  },
}
</script>

<style lang="less">
@import '../../assets/styles/style.less';
@import './index.less';
</style>
