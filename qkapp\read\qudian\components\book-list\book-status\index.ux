<template>
  <div class="wrapper bg-{{xstype}}">
    <text style="width:62px;height:22px;text-align:center;
      font-size:22px;color:#ffffff;margin-bottom:2px;">
      {{textArr[Number(xstype)]}}
    </text>
  </div>
</template>

<script>
export default {
  data: {
    textArr: ['连载', '完结', '推荐']
  },
  
  props: ['xstype'],
  
  onInit() {}
}
</script>

<style>
.wrapper {
  position:absolute;
  top:0;
  right:0;
  border-bottom-left-radius:6px;
  border-top-right-radius:6px;
  width:0px;
  height:0px;
  align-items: center;
}

.bg-1 {
  background-color: rgba(255,0,0,0.79);
  width:62px;
  height:32px;
}

.bg-0 {
  background-color: rgba(48,149,58,0.79);
  width:62px;
  height:32px;
}

.bg-2 {
  background-color: rgba(255,153,0,0.79);
  width:62px;
  height:32px;
}

</style>
