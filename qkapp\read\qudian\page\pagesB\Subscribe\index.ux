/**
* page : 订阅记录页面
* author: yangyang
* date: 2022-03-07
*/

<!-- <template> -->
  <!-- <div class="pay-record-wrapper"> -->
    

  <!-- </div> -->
<!-- </template> -->
<import name="common-header" src="../../components/common-back-header/index"></import>
<template>
  <div class="subscribe-record-wrapper">
    <!-- 状态栏 -->
    <common-header
      title="订阅记录"
      text-center="{{backTitleIsCenter}}"
      onback-click="pageBack"
    ></common-header>
    <list class="scubscribe-list" @scrollbottom="scrollbottom">
      <list-item for="(index,item) in listData" type="scubscribe-list-item">
        <div class="scubscribe-list-item" if="listData.length">
          <image
            class="scubscribe-list-item-image"
            src="{{item.book_pic}}"
            alt="https://img.qdreads.com/v155/common/error.png"
          ></image>
          <div class="scubscribe-list-item-content">
            <text class="content-book-name">{{ item.book_name }}</text>
            <text class="content-book-des">{{ item.book_desc }}</text>
            <text class="content-subscribe-time">{{
              item.subscribe_time
            }}</text>
          </div>
          <text class="scubscribe-list-item-btn" onclick="cancelSubscribe(item)"
            >取消订阅</text
          >
        </div>
      </list-item>
      <list-item class="load-more" if="{{loadMore}}" type="showmode">
        <progress type="circular"></progress>
        <text>加载更多！</text>
      </list-item>
      <list-item type="empty" style="justify-content: center;padding-top: 240px;">
        <div class="empty-section" if="!listData.length">
          <image
            class="empty-image"
            src="https://img.qdreads.com/v163/common/empty.png"
          ></image>
          <text class="empty-des">暂时没有内容</text>
        </div>
      </list-item>
    </list>
    <!-- 返回腾讯系App的button -->
    <back-app-button
      if="{{$app.$def.isTencentBackButtonVisible}}"
      btn-text="{{$app.$def.tencentBackName}}"
      back-url="{{$app.$def.tencentBackUrl}}"
      package-name="{{$app.$def.tencentBackPkg}}"
    ></back-app-button>
  </div>
</template>
<import name="back-app-button" src="../../components/back-app-button"></import>

<script>
export default pageMixin({
  private: {
    backTitleIsCenter: false, // 状态栏标题居左
    loadMore: true,
    page: 1,
    totalPage: 0,
    pathUrl: '',
    listData: [], // 订阅数据
    pageDetail: {
      pageUrl: '自动订阅页',
      pageName: '自动订阅页',
      pageCode: 'READ_COMMON',
      pageOrigin: ''
    },
  },
  onInit() {
    this.getSubscribeList()  // 获取订阅列表
    
  },
  onShow() {
    // 更新页面来源
    this.pathUrl = curObj.pathUrl
    

  },
  onHide() {
    
  },
  /**
   * 列表上拉加载更多
   */
  scrollbottom() {
    this.page++
    if (this.page > this.totalPage) {
      this.loadMore = false
      $utils.showToast('没有更多数据了！', 0)
      return
    } else {
      this.getSubscribeList(this.page)
    }
  },
  // 获取订阅列表
  getSubscribeList() {
    this.loading = true
    $apis.example.subscribeListApi().then(res => {
      this.loading = false
      this.loadMore = false
      if (res.code == 200) {
        this.listData = res.data;
        this.page = res.data.page;
        this.totalPage = res.data.totalPage;
      } else {
        this.listData = []
      }
    })
  },
  /**
   *  取消订阅
   * @param item 订阅书籍信息
   */
  cancelSubscribe(item) {
    $apis.example.subscribeCancelApi({
      book_id: item.book_id
    }).then(res => {
      if (res.code == 200) {
        COMMON_REPORT_UTILS.page_click_report(`取消订阅${item.book_id}`)
        this.getSubscribeList()
      }
    })
  },
  /**
   * 返回
   * 
   */
  pageBack() {
    COMMON_REPORT_UTILS.page_click_report(`返回`)
    $utils.goBack()
  },
  onBackPress() {
    console.info(`触发：onBackPress`)
    COMMON_REPORT_UTILS.back_click_report('', '', '跳转页面')
  },
  /**
   * 去书城
   */
  toPageStore() {
    COMMON_REPORT_UTILS.back_click_report('', '', '去书城')
    this.$app.$def.tabListType = [0, 0, 0, 0, 0]
    $utils.routetheUrl('/pagesA/Main', { selectIndex: 1, pathUrl: '订阅纪录' })
  }
})
</script>

<style lang="less">
@import './index.less';
</style>
