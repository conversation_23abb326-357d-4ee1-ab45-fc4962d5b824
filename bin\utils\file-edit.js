const fs = require('fs')

function editFile(filePath, oldValue, newValue) {
    if (!fs.existsSync(filePath)) {
        return
    }
    const fileContent = fs.readFileSync(filePath, 'utf-8')
    const newContent = fileContent.replaceAll(oldValue, newValue)
    console.log(`特殊处理：${filePath}, 旧值：${oldValue}, 新值：${newValue}`)
    fs.writeFileSync(filePath, newContent)
}


module.exports = {
    editFile
}
