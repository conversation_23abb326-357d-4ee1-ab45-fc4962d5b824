.page-container {
    flex-direction: column;
    background-color: #F5F5F5;
}

.fdc {
    flex-direction: column;
}

.flex_row {
    display: flex;
    flex-direction: row;
}

.flex_column {
    display: flex;
    flex-direction: column;
}

.jusBet_aliCen {
    justify-content: space-between;
    align-items: center;
}

.jusSta_aliCen {
    justify-content: flex-start;
    align-items: center;
}

.jusSta_aliSta {
    justify-content: flex-start;
    align-items: flex-start;
}

.jusCen_aliCen {
    justify-content: center;
    align-items: center;
}

.jusCen_aliSta {
    justify-content: center;
    align-items: flex-start;
}

/* 粗体 */

.textBold {
    font-weight: bold;
}

.df {
    display: flex;
}

.df_2 {
    flex: 2;
}

.bg {
    background-color: #fff;
}

.bg_white {
    background-color: #fff;
}

.bg_hui {
    background-color: #eee;
}

.bg_red {
    background-color: #dd2727;
}

.bg_green {
    background-color: #02bf02;
}

.bg_cheng {
    background-color: #f85;
}

/* .clear{  clear: both;} */

.ovh1 {
    width: 100%;
    /* overflow:hidden; */
    text-overflow: ellipsis;
    /* display:-webkit-box;
  -webkit-line-clamp:2;
  -webkit-box-orient:vertical; */
}

.yellow {
    color: #FFF600
}

.red {
    color: #FA2209;
}

.redPacketColor {
    color: #FFDCC6;
}

.c3 {
    color: #333;
}

.c6 {
    color: #666;
}

.c9 {
    color: #999;
}

.white {
    color: #fff;
}

.font_24 {
    font-size: 48px;
}

.font_20 {
    font-size: 40px;
}

.font_18 {
    font-size: 36px;
}

.font_16 {
    font-size: 32px;
}

.font_15 {
    font-size: 30px;
}

.font_14 {
    font-size: 28px;
}

.font_13 {
    font-size: 26px;
}

.font_12 {
    font-size: 24px;
}

.font_11 {
    font-size: 22px;
}

.font_10 {
    font-size: 20px;
}

.bold {
    font-weight: bold;
}

.l_h20 {
    line-height: 20px;
}

/*定位*/

.pr {
    position: relative;
}

.pa {
    position: absolute;
}

/*文字超出省略*/

.ellipsis {
    lines: 1;
    text-overflow: ellipsis;
}

/*对齐*/

.tl {
    text-align: left;
}

.tc {
    text-align: center;
}

.tr {
    text-align: right;
}

/*间距*/

.mt1 {
    margin-top: 2px;
}

.mt5 {
    margin-top: 10px;
}

.mt8 {
    margin-top: 16px;
}

.mt10 {
    margin-top: 20px;
}

.mt15 {
    margin-top: 30px;
}

.mt20 {
    margin-top: 40px;
}

.mt25 {
    margin-top: 50px;
}

.mt30 {
    margin-top: 60px;
}

.mt35 {
    margin-top: 70px;
}

.mt40 {
    margin-top: 80px;
}

.mt60 {
    margin-top: 120px;
}

/* .p_all{ padding: 3%;} */

/*边框*/

/* .bte{border-top:solid 1px #eee;}
.bbe{border-bottom: solid 1px #eee;} */

/* .bre{ border-right: 1px solid #eee} */

/*浮动*/

/* .fl_l{ float: left;} */

/* .fl_r{ float: right;} */

.w10 {
    width: 10%;
}

.w20 {
    width: 20%;
}

.w30 {
    width: 30%;
}

.w40 {
    width: 40%;
}

.w50 {
    width: 50%;
}

.w60 {
    width: 60%;
}

.w70 {
    width: 70%;
}

.w80 {
    width: 80%;
}

.w90 {
    width: 90%;
}

.w100 {
    width: 100%;
}

/*边框*/

/* .bte{ border-top: 1px solid #eee;}
.bbe{ border-bottom: solid 1px #eee;}
.bre{ border-right: 1px solid #eee;}
.ble{ border-left: 1px solid #eee;} */

.search_no {
    width: 100%;
    color: #666;
    text-align: center;
    font-size: 14px;
}

.scimg {
    width: 100px;
    height: 100px;
    background-size: 100px;
}

.tradeStateColor_red {
    color: #dd2727;
}

.tradeStateColor_yellow {
    color: #e99936;
}

.tradeStateColor_green {
    color: rgb(88, 183, 133);
}

.main-btn {
    background: linear-gradient(to right, #fc4a17, #fb3732);
    border-radius: 34px;
    text-align: center;
    color: #fff;
    font-size: 32px;
    /* line-height: 64px; */
    height: 66px;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 270px;
    margin: 0;
}

/* .main-btn.disabled{
  color: #fff;
  opacity: 0.4;
} */

/* .breath-btn{ */

/* animation:breathScale 1s infinite; */

/* } */

@keyframes breathScale {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.08);
    }
    100% {
        transform: scale(1);
    }
}

/* .default-btn.small{
	width: 88px;
	height: 40px;
	opacity: 1;
	background-color: #eee;
	border-radius: 21px;
	color: #333;
	font-size: 24px;
	line-height: 40px;
	border: 1px solid #eee;
  padding: 0;
}
.default-btn.active-adress{
	background: linear-gradient( to right,#FC4A17, #FB3732);
	border: 1px solid #FC4A17;
	color: #fff;
} */

.priceMove {
    position: relative;
    top: 6px;
}

body {
    /* height: 100%;
  width: 100%; */
    font-family: PingFang SC;
    background-color: #f5f5f5;
}

.content {
    /* min-height: 100%; */
    /* padding: 20px 24px; */
    background-color: #F5F5F5;
    /* padding-top: 74px; */
    /* margin-top: 64px; */
}

.page-header {
    background: linear-gradient(to right, #fc4a17, #fb3732);
    width: 100%;
    position: fixed;
    justify-content: center;
    align-items: center;
    left: 0;
    right: 0;
    color: #fff;
    font-size: 36px;
    font-weight: bold;
}

.page-header text {
    width: 100%;
    height: 78px;
    color: #fff;
    font-weight: bold;
    text-align: center;
    align-items: center;
}

.page-header .back {
    /* padding: 12px; */
    width: 48px;
    height: 48px;
    position: absolute;
    left: 12px;
    /* z-index: 9999; */
}

.page-header-white {
    background: linear-gradient(to right, #fff, #fff);
}

/* .page-header.white{
  background-color: #fff;
  color: #333;
} */

/* 自定义弹窗 */

.dialog-wrap {
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    position: fixed;
    /* top: 0; */
    left: 0;
    /* right: 0; */
    bottom: 0;
    justify-content: center;
    align-items: center;
    /* z-index: 99991; */
}

.dialog-close {
    width: 64px;
    height: 64px;
    position: absolute;
    right: 40px;
    top: 240px;
}

/* .dialog-close.bottom{
  bottom: 4px;
  left: 250px;
} */

.dialog-module {
    width: 654px;
    height: 670px;
    opacity: 1;
    background-color: #ffffff;
    border-radius: 30px;
    /* position: absolute;
  left: 48px;
  right: 48px;
  top: 332px; */
}

.dialog-title {
    line-height: 108px;
    height: 108px;
    text-align: center;
    font-size: 36px;
    color: #333;
}

.dialog-body {
    padding: 0 30px;
}

.form-item {
    height: 100px;
    line-height: 100px;
    font-size: 28px;
    border-bottom: 1px solid #eee;
    position: relative;
    align-items: center;
}

.form-label {
    position: absolute;
    left: 0;
    /* top: 0; */
    width: 120px;
    /* text-align: left; */
    color: #666;
    /* line-height: 100px; */
    font-size: 28px;
}

.form-input {
    color: #333;
    /* line-height: 100px; */
    padding-left: 130px;
    width: 100%;
}

.input-placeholder {
    color: #999;
    font-size: 28px;
}

.dialog-btn {
    height: 82px;
    width: 400px;
    left: 127px;
    position: absolute;
    bottom: 40px;
    margin: 0;
    line-height: 82px;
    border-radius: 41px;
}

/* button::after{
  border: 0;
} */

/* view::-webkit-scrollbar { width: 0 !important } */

/* view { overflow: -moz-scrollbars-none; } */

/* 商品列表 */

.spu {
    flex-wrap: wrap;
}

.spu-item {
    width: 50%;
    height: 504px;
    margin-bottom: 18px;
}

.spu-item-content {
    border-radius: 14px;
    background-color: #fff;
    position: relative;
    width: 100%;
    margin-left: 9px;
    margin-right: 9px;
}

/* .spu-item:nth-of-type(odd){
  margin-right: 18px;
} */

.spu-item .spu-img {
    height: 342px;
    width: 100%;
    border-top-right-radius: 14px;
    border-top-left-radius: 14px;
}

.spu-descript {
    border-top-right-radius: 20px;
    background: linear-gradient(to right, #FC4A17, #FB3732);
    color: #fff;
    padding-left: 4px;
    padding-right: 16px;
    position: absolute;
    left: 0;
    bottom: 0;
    height: 40px;
    line-height: 40px;
    align-items: center;
}

.img-box {
    position: relative;
    height: 342px;
    background-image: url('https://qkimg.tianyaoguan.cn/weixin_images/home/<USER>');
    background-size: 342px 342px;
    border-radius: 14px;
}

.spu-descript .spu-icon {
    width: 28px;
    height: 28px;
}

.spu-descript .text {
    height: 40px;
    line-height: 40px;
    color: #fff;
}

.spu-name {
    padding: 0 20px;
    color: #333333;
}

/* 状态栏 */

.v_sticky {
    width: 100%;
    position: fixed;
    top: 0;
    z-index: 9999;
    height: 80px;
    align-items: center;
    justify-content: center;
    background: linear-gradient(to right, #fc4a17, #fb3732);
}

.v_sticky text {
    font-size: 36px;
    font-weight: 700;
    color: #fff;
    margin-left: 24px;
}

.back_img {
    width: 18px;
    height: 34px;
    margin-left: 15px;
}

.spu_name_kanjia {
    lines: 1;
    text-overflow: ellipsis;
    color: #333;
    font-weight: 500;
    margin-top: 15px;
    margin-left: 20px;
    margin-right: 20px;
}

.spu_textView {
    margin-top: 7px;
    margin-left: 20px;
    margin-right: 20px;
}

.clickFree {
    margin-bottom: 16px;
    margin-top: 10px;
    margin-left: 20px;
    width: 184px;
    height: 52px;
    background: linear-gradient(90deg, #fc4a17, #fb3732 100%);
    border-radius: 26px;
    justify-content: center;
    align-items: center;
}

.textView {
    font-size: 28px;
    font-weight: 700;
    color: #ffffff;
    line-height: 32px;
}

.advconfigView {
    width: 100%;
    height: 300px;
    border-radius: 20px;
    object-fit: fill;
}

.ad_text {
    position: absolute;
    right: 14px;
    bottom: 12px;
    font-size: 20px;
    color: #ffffff;
}