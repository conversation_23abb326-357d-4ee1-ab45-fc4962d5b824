<template>
  <div class="lists-wrapper">
    <text class="title">常用功能</text>
    <!-- <image class="user-title3" src="https://img.qdreads.com/v155/comon/user-title3.png"></image> -->
    <div class="feature-list">
      <div class="row-list" for="{{value in lists}}">
        <div class="item-wrapper" for="{{item in value}}" @click="menuItemClick(item)">
        <image class="icon" src="{{item.icon}}"></image>
        <text>{{item.name}}</text>
        <image class="jump" src="https://img.qdreads.com/v163/user/jump-normal.png"></image>
      </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data: {
    lists: [
      [
        {
          "icon": "https://img.qdreads.com/v163/user/menu-icon-1.png",
          "name": "浏览记录",
          "path": "/pagesA/Read-History"
        },
        {
          "icon": "https://img.qdreads.com/v163/user/menu-icon-2.png",
          "name": "福利中心",
          "path": "WELFARE"
        },
        {
          "icon": "https://img.qdreads.com/v163/user/menu-icon-5.png",
          "name": "投诉与反馈",
          "path": "/pagesB/Customer-Service"
        },
      ],
      [
        {
          "icon": "https://img.qdreads.com/v163/user/menu-icon-6.png",
          "name": "我的设置",
          "path": "/pagesB/Setting"
        },
      ]
    ]
  },
    /**
   * 个人中心   菜单列表点击
   *  @param index 菜单下标
   *  @param item 菜单选项
   */
  menuItemClick(item) {

    this.$emit('menuItemClick', { item: item })
  }
}
</script>

<style lang="less">
@import '../../../assets/styles/style.less';

.lists-wrapper{
  flex-direction: column;
  padding: 30px;
  padding-bottom: 10px;
}
.title{
  font-weight: bold;
  color: #333333;
  font-size: 36px;
  line-height: 36px;
}
.feature-list {
  margin-top: 14px;
  width: 100%;
  flex-direction: column;
}

.item-wrapper {
  height: 88px;
  align-items: center;
  .icon {
    width: 36px;
    height: 36px;
    margin-right: 18px;
  }

  > text {
    font-size: 30px;
    color: #333333;
    line-height: 30px;
    height: 30px;
    margin-bottom: 3px;
  }
  .jump {
    width: 30px;
    height: 30px;
    margin: 0;
    position: absolute;
    right: 0;
    top: 29px;
    margin-bottom: 3px;
  }
}

.user-title3 {
  width: 148px;
  height: 36px;
}

.row-list {
  flex-direction: column;
  width: 100%;
}
</style>

