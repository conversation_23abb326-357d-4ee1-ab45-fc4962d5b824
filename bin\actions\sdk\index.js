const path = require('path')
const fs = require('fs')
const { setAppBasePath } = require('../../config/app-base')
const { parseConfig } = require('../../config/parse')
const { compBrandCode } = require('../../main/tasks/comp-brand-code')
const { consoleSplit } = require('../../utils/console')
const copyPublicFile = require('../../main/tasks/copy-public')
const copyAppFile = require('../../main/tasks/copy-app')
const { watchAppFile, watchPublicFile } = require('../../main/tasks/watch-file')
const loading = require('../../utils/loading')
const { projectPath } = require('../../main/constant')
const { updateManifest, appGlobalConfig } = require('../../main/tasks/create-config')
const { deleteSrcNoSDK } = require('../../main/tasks/delete-src') // deleteGenerateFile = require('./tasks/delete-src')
const { editFont } = require('../../main/tasks/edit-font')
const { versionCodeFormat } = require('../../main/tasks/version-code-format')
const { createAppDefinePlugin } = require('../../main/tasks/create-app-plugin')
const { getSdkVersion } = require('../../utils/get-sdk-version')

async function mainTask({ brand }) {
  const category = 'read'
  const code = 'qudian'
  const appType = 'read'
  // const appName = '趣点阅读'
  const company = 'cy'
  setAppBasePath(path.resolve(projectPath, './qkapp', `${category}/${code}`))
  const { manifest, app } = parseConfig(brand)

  // 删除src
  deleteSrcNoSDK()

  // 获取sdk版本
  const sdkVersion = getSdkVersion()
  console.log('sdkVersion', sdkVersion)

  // 复制应用文件
  consoleSplit('业务厂商代码生成')
  compBrandCode(brand)
  consoleSplit('复制应用文件')
  copyAppFile({ category, code })

  // 版本号格式化
  consoleSplit('版本号格式化')
  versionCodeFormat(brand, company)

  // 复制公共内容
  // consoleSplit('SDK 厂商代码生成')
  // sdkBrandGen(brand)
  consoleSplit('复制公共文件和SDK')
  copyPublicFile(appType)

  // SDK 厂商代码生成
  consoleSplit('生成 SDK 厂商代码')
  require(path.resolve(process.cwd(), 'scripts/brand/copy-watch'))(brand)
  if (fs.existsSync(path.resolve(process.cwd(), 'scripts/quickapp-manager-config.js'))) {
    require(path.resolve(process.cwd(), 'scripts/quickapp-manager-config.js'))(process.cwd(), company, brand)
  }
  // 更新自定义配置，生成 manifest
  updateManifest(manifest, sdkVersion)

  // 特殊处理
  consoleSplit('特殊处理')
  editFont(process.cwd())

  // 生成 APP-config-define-plugin 配置文件
  createAppDefinePlugin(app)

  // 生成全局 APP 配置
  appGlobalConfig(app)
  // 生成 readme 文档
  // createReadme(getAppBasePath(), brand, appType)

  consoleSplit('开始监听应用和公共文件变化')
  // 监听 APP 代码变更
  watchAppFile({ category, code }, brand)
  // 监听 公共部分 代码变更
  watchPublicFile(appType, brand)
  // 加载动画
  loading()
}

module.exports = mainTask
