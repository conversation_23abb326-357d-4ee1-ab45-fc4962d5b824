<template>
    <div class="{{loading ? 'loadingView': 'loadingHide'}}">
      <block>
        <lottie class="lottie" source="../../lottie/data.json" id="lottie-loading" autoplay={{false}} loop={{true}} 
        ></lottie>
      </block>
      <!-- <block else>
        <div class="loadingViewImage">
          <image src="../../assets/images/load.gif"></image>
          <text>加载中...</text>
        </div>
      </block> -->
    </div>
</template>

<script>
export default {
  data() {
    return {
      isLoading: this.loading
    };
  },
  props: {
    loading: {
      type: <PERSON><PERSON><PERSON>,
    }
  },

  onInit() {
  },

  onReady() {
    if (this.loading) this.watchLoadingChange(true, false)
    this.$watch("loading", "watchLoadingChange");
  },

  watchLoadingChange(newV, oldV) {
    this.isLoading = newV;
    try {
      if (!this.loading) {
        this.$element('lottie-loading') && this.$element('lottie-loading').reset()
      } else {
        if (this.$element('lottie-loading')) {
          this.$element('lottie-loading').play()

        }
      }
    } catch (error) {
    }
  }
};

</script>
<style lang="less">
.loadingHide{
  position: absolute;
  width: 0px;
  height: 0px;
}
.loadingView {
  position: fixed;
  width: 100%;
  height: 100%;
  justify-content: center;
  align-items: center;
  .loadingViewImage {
    width: 202px;
    height: 202px;
    background-color: #ffffff;
    border-radius: 16px;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    image {
      width: 78px;
      height: 78px;
    }
    text {
      height: 28px;
      font-size: 28px;
      font-weight: 500;
      color: #666666;
      line-height: 28px;
      margin-top: 32px;
    }
  }
}

.lottie {
  width: 112px;
  height: 112px;
}
</style>
