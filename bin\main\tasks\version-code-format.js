const { getVersionName, setVersionCode } = require('../../utils/get-versionCode')

function versionCodeFormat(brand, company) {
  const { versionName, versionCodeIndex, versionCode } = getVersionName(brand)
  const newVersionCode = getVersionCode(company, versionName, versionCode)

  setVersionCode(newVersionCode, versionCodeIndex)
}

function getVersionCode(company, versionName, versionCode) {
  switch (company) {
    case 'cy':
      return getVersionCodeCy(versionName, versionCode)
    case 'rabbit':
      return getVersionCodeRabbit(versionName, versionCode)
    case 'kang':
      return getVersionCodeKang(versionName, versionCode)
    default:
      return ''
  }
}

function getVersionCodeCy(versionName, versionCode) {
  // Split version string into components
  const [x, y, z] = versionName.replace(/['"]/g, '').split('.').map(Number)
  if (x >= 10 || y >= 10 || z >= 10) {
    console.log('版本号超出两位数，请检查版本号,versionName:', versionName)
    process.exit(1)
  }
  return `${x}0${y}0${z}000`
}

function getVersionCodeRabbit(versionName, versionCode) {
  const [x, y, z] = versionName.replace(/['"]/g, '').split('.').map(Number)
  if (x >= 10 || y >= 10 || z >= 10) {
    console.log('版本号超出两位数，请检查版本号,versionName:', versionName)
    process.exit(1)
  }
  return `${x}${y}00000${z}`
}

function getVersionCodeKang(versionName, versionCode) {
  const now = new Date()

  const year = now.getFullYear() // 获取年份
  const month = (now.getMonth() + 1).toString().padStart(2, '0') // 获取月份，补零
  const day = now.getDate().toString().padStart(2, '0') // 获取日期，补零
  const hours = now.getHours().toString().padStart(2, '0') // 获取小时，补零

  return `${year}${month}${day}${hours}` // 格式化为“年月日+小时”
}

module.exports = {
  versionCodeFormat
}
