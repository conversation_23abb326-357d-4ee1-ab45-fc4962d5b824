const fs = require('fs')
const path = require('path')

/**
 * 复制文件或目录
 * @param {string} from - 源文件或目录路径
 * @param {string} to - 目标路径
 */
function copyFile(from, to) {
  if (!fs.existsSync(from)) {
    return
  }
  try {
    const stats = fs.statSync(from)

    if (stats.isDirectory()) {
      // 如果 from 是目录,则复制目录下所有文件
      copyDirectory(from, to)
    } else {
      // 如果 from 是文件,则复制单个文件
      copyFileWithPath(from, to)
    }
  } catch (err) {
    console.error('复制文件/目录时出错:', err)
  }
}

/**
 * 复制目录及其所有子文件
 * @param {string} fromDir - 源目录路径
 * @param {string} toDir - 目标目录路径
 */
function copyDirectory(fromDir, toDir) {
  // 确保目标目录存在
  fs.mkdirSync(toDir, { recursive: true })

  const files = fs.readdirSync(fromDir)
  for (const file of files) {
    const fromPath = path.join(fromDir, file)
    const toPath = path.join(toDir, file)
    copyFile(fromPath, toPath)
  }
}

/**
 * 复制单个文件,并确保目标路径存在
 * @param {string} from - 源文件路径
 * @param {string} to - 目标文件路径
 */
function copyFileWithPath(from, to) {
  // 确保目标文件的父目录存在
  const toDir = path.dirname(to)
  fs.mkdirSync(toDir, { recursive: true })

  fs.cpSync(from, to, { recursive: true, force: true })
}

module.exports = {
  copyFile
}
