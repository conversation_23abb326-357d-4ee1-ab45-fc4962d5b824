/**
 * 生成公共组件的厂商代码
 */
const path = require('path')
const fs = require('fs')
const { WATCH_FILE_LIST, APP_BRAND_FILE } = require('../../config/comp-multi-brand')
const { projectPath, SDK_PROJECT_NAME } = require('../constant')
const packageJson = require(path.resolve(process.cwd(), 'package.json'))
const { getAppBasePath } = require('../../config/app-base')

// ===================================================
/**
 * 厂商文件修改
 *
 * @param {string} appFiles - 文件所在的源路径
 * @param {string} brand - 厂商
 */
function brandChange(appFile, brand) {
  const brandDir = path.resolve(appFile, brand)
  const baseDir = path.resolve(appFile, 'base')

  // 确定是否是 base 目录
  const dirToUse = fs.existsSync(brandDir) ? brandDir : baseDir
  if (!fs.existsSync(dirToUse)) return

  const isBase = dirToUse === baseDir
  const brandFiles = fs.readdirSync(dirToUse)

  brandFiles.forEach(curF => {
    _copyFile(path.resolve(dirToUse, curF), isBase ? 'base' : brand)
  })
}
/**
 * 复制文件
 * @param sourceFile
 * @param brand 厂商
 */
function _copyFile(sourceFile, brand) {
  const macPath = sourceFile.replace(/\\/g, '/')
  const targetPath = macPath.replace(`${brand}/`, '')

  fs.cpSync(macPath, targetPath, { recursive: true, force: true })
  console.log(`复制成功：${_simpleDirPath(macPath)} -> ${_simpleDirPath(targetPath)}`)
}

function _simpleDirPath(dir) {
  return dir.replace(projectPath, '')
}

function _getQKPubAbsolutePath(filePath, selectSdkVersion) {
  // sdk 项目，需要用自己的 qkpub
  if (packageJson.name === SDK_PROJECT_NAME) {
    return path.resolve(projectPath, 'qkpub', filePath)
  }

  // 其他项目走，node_modules/ad-sdk/qkpub
  return path.resolve(projectPath, './packages/ad-sdk', selectSdkVersion, 'qkpub', filePath)
}

function compBrandCode(brand, selectSdkVersion) {
  WATCH_FILE_LIST.forEach(filePath => {
    brandChange(_getQKPubAbsolutePath(filePath, selectSdkVersion), brand)
  })
  APP_BRAND_FILE.map(appFile => path.resolve(getAppBasePath(), appFile)).forEach(filePath => {
    brandChange(filePath, brand)
  })
}

function handleBrandFile(name, brand) {
  APP_BRAND_FILE.forEach(appFile => {
    const brandFile = path.resolve(getAppBasePath(), appFile)
    if (!name.includes(brandFile)) return

    console.log('首页厂商', name, brandFile)
    // 判断是否在 base 或当前品牌目录中
    const brandDir = path.resolve(brandFile, brand);
    const baseDir = path.resolve(brandFile, 'base');

    if (fs.existsSync(brandDir) && name.includes(brandDir)) {
      console.log('处理厂商文件夹：', brandDir);
      brandChange(brandFile, brand);
    } else if (!fs.existsSync(brandDir) && fs.existsSync(baseDir) && name.includes(baseDir)) {
      console.log('处理 base 目录文件：', baseDir);
      brandChange(brandFile, 'base');
    }
  })
}

// ====== 启动服务 ======
module.exports = {
  compBrandCode,
  handleBrandFile
}
