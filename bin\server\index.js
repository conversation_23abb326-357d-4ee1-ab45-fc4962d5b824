const Koa = require('koa')
const app = new Koa()
const fs = require('fs')
const path = require('path')
const { convertCardJsonToArray, generateCardHtml } = require('./template/utils')

async function createServer(baseInfo, port = 3000) {
  const res = createCard(baseInfo)
  app.use(async ctx => {
    ctx.contentType = 'text/html; charset=utf-8'
    ctx.body = res
  })

  let availablePort = null

  // 循环直到找到可用端口
  while (!availablePort) {
    availablePort = await listenOnPort(port)
    port++
  }
  const open = (await import('open')).default
  open(`http://localhost:${availablePort}`)
}

function createCard(cardJson) {
  const htmlCode = fs.readFileSync(path.resolve(__dirname, './template/index.html'), 'utf-8').toString()
  const list = convertCardJsonToArray(cardJson)
  const card = generateCardHtml(list)
  return htmlCode.replace('{info}', card)
}

// 监听端口是否被占用
function listenOnPort(port) {
  return new Promise((resolve, reject) => {
    const server = app.listen(port, () => {
      resolve(port)
    })

    // 捕获端口占用的错误
    server.on('error', err => {
      if (err.code === 'EADDRINUSE') {
        console.log(`端口 ${port} 被占用，尝试端口 ${port + 1}`)
        resolve(null) // 继续尝试下一个端口
      } else {
        reject(err)
      }
    })
  })
}

module.exports = createServer
