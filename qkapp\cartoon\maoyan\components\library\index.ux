<import name="book-list" src="./book-list"></import>

<template>
  <div class="page-container">
    <book-list if="{{refreshData}}" list="{{library.list}}" show-load="{{library.load}}">
      <div class="sub-container">
        <stack>
          <swiper autoplay="true" class="swiper">
            <image
              class="swiper-image"
              src="{{$item.swiper}}"
              for="swiperList"
              onclick="goToDetail($item)"
            ></image>
          </swiper>
          <div class="search-wrapper" @click="toSearchPage">
            <image src="../../assets/v100/search-white.png"></image>
          </div>
        </stack>

        <div class="grid-wrapper">
          <div class="grid-content" @click="toRankPage('all')">
            <image src="../../assets/v100/all-rank.png"></image>
            <text>精选排行</text>
          </div>
          <div
            class="grid-content"
            @click="toRankPage('new')"
            style="margin-left:88px;margin-right:88px;"
          >
            <image src="../../assets/v100/new-rank.png"></image>
            <text>热门榜单</text>
          </div>
          <div class="grid-content" @click="toCollectPage">
            <image src="../../assets/v100/to-shelf.png"></image>
            <text>追更收藏</text>
          </div>
        </div>
      </div>
    </book-list>
  </div>
</template>

<script>
import { bookListData } from '../../assets/data/book-list.js'
export default {
  props: ['refreshData'],
  data: {
    library: {
      list: []
    },
    swiperList: []
  },
  onInit() {
    this.queryFn()
  },
  queryFn() {
    let concatList = [...bookListData]
    $utils.shuffleArrayWithSeed(concatList, 25)
    // 更新列表
    this.library.list = this.library.list.concat(concatList)
    this.swiperList = this.library.list.slice(0, 6)
  },
  goToDetail(item) {
    COMMON_REPORT_UTILS.page_click_report('轮播图');
    this.$emit('updatePageHide', { data: true });
    // 根据id查询详情
    $utils.routetheUrl('subPages/book-detail', { info: JSON.stringify(item) })
  },
  toRankPage(type) {
    COMMON_REPORT_UTILS.page_click_report(type == 'all' ? '全站排行' : '新品排行');
    this.$emit('updatePageHide', { data: true });
    $utils.routetheUrl('subPages/rank', { type })
  },
  toShelfPage() {
    this.$emit('changeSelectIndex', { index: 1 })
  },
  toSearchPage() {
    COMMON_REPORT_UTILS.page_click_report('搜索栏');
    this.$emit('updatePageHide', { data: true });
    $utils.routetheUrl('subPages/search')
  },
  toCollectPage() {
    COMMON_REPORT_UTILS.page_click_report('追更收藏');
    this.$emit('updatePageHide', { data: true });
    $utils.routetheUrl('subPages/collect')
  },
  updatePageHide() {
    this.$emit('updatePageHide', { data: true });
  },
}
</script>

<style lang="less">
@import '../../assets/styles/index.less';

.sub-container {
  width: 100%;
  .flex-box-mixins(column, flex-start, flex-start);
}
.swiper {
  indicator-selected-color: @brand;
  indicator-color: #ffffff;
  indicator-size: 12px;
  indicator-bottom: 20px;
  width: 100%;
  height: 563px;
  .swiper-image {
  }
}

.search-wrapper {
  margin-left: @app-padding;
  margin-top: 80px;
  width: 88px;
  height: 88px;

  image {
    width: 44px;
    height: 44px;
  }
}

.grid-wrapper {
  .flex-box-mixins(row, center, flex-start);
  width: 100%;
  margin-top: 65px;
}

.grid-content {
  .flex-box-mixins(row, flex-start, center);
  height: 50px;

  image {
    width: 50px;
    height: 50px;
    margin-right: 8px;
  }

  text {
    height: 26px;
    font-size: 26px;
    color: @text-black;
  }
}
</style>
