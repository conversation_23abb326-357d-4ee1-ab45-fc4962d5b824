<template>
  <div class="wrapper"></div>
</template>

<script>

export default {
  data: {
    isAllowBackPress: false
  },
  onBackPress() {
    if (!this.isAllowBackPress) {
      return true
    }
  },
  onInit() {
    let that = this

    if (that.$page.query.routerPushNewView && that.$page.query.routerPushNewView == 2) {
      console.log('that.$page.query', that.$page.query)
      let uri = '/pagesC/Read'
      switch (that.$page.query.viewName) {
        case 'READ':
          uri = '/pagesC/Read'
          break
        case 'WELFARE':
          uri = '/pagesA/WelfareNew'
          break
        case 'ACTION':
          uri = '/pagesC/Action'
          break
        default:
          uri = '/pagesC/Read'
          break
      }
      $utils.clear()
      $utils.routetheUrl(uri,{...that.$page.query})
      that.isAllowBackPress = true
    } else {
      $utils.clear()
      $utils.routetheUrl('/pagesA/Main',{})
      that.isAllowBackPress = true
    }
  }
}
</script>

<style>
.wrapper {
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.title {
  text-align: center;
  color: #212121;
}
</style>