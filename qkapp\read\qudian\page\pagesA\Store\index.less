.store-wrapper {
    width: 750px;
    // background-color: #ffffff;;
    flex-direction: column;
    align-items: center;
    flex: 1;
    background-color: #F4F5F7;

    .store-top-header{
        width: 100%;
        flex-direction: column;
    }
    .store-header {
        width: 750px;
        height: 100px;
        padding: 12px 24px 0;
    }
    .store-header-search {
        // width: 500px;
        position: absolute;
        height: 68px;
        background-color: #ffffff;
        border-radius: 38px;
        background-image: url(https://img.qdreads.com/image%2F2023-04-12%2F1681292500542_sousuo%402x.png);
        background-size: 34px 34px;
        background-repeat: no-repeat;
        background-position:left 22px top 17px;
        padding: 0 0 0 66px;
    }
    .search-title {
        height: 68px;
        color: #999999;
        font-size: 24px;
    }

    .selected-vip {
        
        background-color: transparent;
        border: 1px solid #ffdfba;
        background-image: url(https://img.qdreads.com/v163/<EMAIL>);
    }

    .selected-vip-title {
        color: #857365
    }
    
}

.margin-ledt-0 {
    margin-left: 0;
}

.title-more {
    width: 112px;
    height: 44px;
    background-color: #f8f8f8;
    border-radius: 8px;
    justify-content: center;
    align-items: center;

    > text {
        font-size: 24px;
        color: #666666;
        height: 44px;
    }
}

.store-header-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    flex-direction: column;
}

.tab-item {
    flex-direction: column;
    align-items: center;
}

.tab-item-title {
    height: 32px;
    font-size: 32px;
    color: #666666;
}

.item-title-selected { 
    color: #333333;
    font-size: 40px;
    height: 40px;
    font-weight: bold;
}

.tab-item-image {
    width: 78px;
    height: 33px;
}

.set-intresting {
    width: 690px;
    height: 80px;
    border-radius: 18px;
    align-items: center;
    background-image: url(https://img.qdreads.com/v163/sc_jz.png);
    background-size: 100%;
    background-repeat: no-repeat;
}

.tabs-list {
    flex-direction: column;
    align-items: center;
    width: 100%;
    height: 100%;
    > list-item {
        justify-content: center;
    }
}

.header-wrapper-index-0, .header-wrapper-index-2 {
    background-image: url('https://img.qdreads.com/v173/sc_bj_wsh.png');
    background-size: 750px 444px;
    background-position: 0 0;
}

.header-wrapper-index-1 {
    background-image: url('https://img.qdreads.com/v173/sc_npbj_wsh.png');
    background-size: 750px 444px;
    background-position: 0 0;
}

.header-wrapper-index-3 {
    background-color: rgba(28, 23, 43,1);
}

.abs-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 750;
    height: 444px;
}

.abs-bg-male {
    position: absolute;
    top: 0;
    left: 0;
    width: 750;
    height: 444px;
    background-image: url('https://img.qdreads.com/v173/sc_npbj_wsh.png');
    background-size: 750px 444px;
    background-position: 0 0;
}

.abs-bg-normal {
    position: absolute;
    top: 0;
    left: 0;
    width: 750;
    height: 444px;
    background-image: url('https://img.qdreads.com/v173/sc_bj_wsh.png');
    background-size: 750px 444px;
    background-position: 0 0;
}

.abs-bg-vip {
    position: absolute;
    top: 0;
    left: 0;
    width: 750;
    height: 806px;
    background-image: url(https://img.qdreads.com/v163/money/vip-top-bg.png);
    background-size: 750px 806px;
    background-repeat: no-repeat;
}

.intre-lottie {
    width: 242px;
    height: 68px;
}

