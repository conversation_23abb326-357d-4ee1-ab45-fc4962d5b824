/**
* page : 搜索页面样式
* author: yang<PERSON>
* date: 2022-03-08
*/
.search-wrapper {
    flex-direction: column;
    background-color: #f4f5f7;
    width: 750px;
    height: 100%;
    .search-input-section {
        padding: 12px 30px;
        flex-shrink: 0;
        .search-input-box {
            height: 76px;
            width: 702px;
            background-color: #ffffff;
            border-radius: 200px;
            align-items: center;

            .search-input-icon {
                width: 34px;
                height: 34px;
                margin-left: 22px;
                margin-right: 16px;
            }
            .search-input {
                flex: 1;
                font-size: 28px;
                color: #999999;
                caret-color:#333333
            }
            .search-del {
                width: 36px;
                height: 36px;
                // margin-top: 20px;
                margin-right: 20px;
                margin-left: 40px;
            }
        }
    }
    // 热门搜索
    .search-hot-section {
        padding: 20px 30px;
        flex-direction: column;
        .search-hot-title {
            align-items: center;
            width: 100%;
            .search-hot-name {
                font-size: 28px;
                font-weight: 700;
                color: #999999;
                height: 32px;
            }
            .search-hot-change {
                font-size: 24px;
                font-weight: 500;
                color: #666666;
                padding: 10px 20px;
                background-color: #f8f8f8;
                border-radius: 8px;
            }
        }
        .search-hot-content {
            flex-wrap: wrap;
            margin-top: 20px;
            .search-hot-label {
                margin-right: 30px;
                margin-bottom: 30px;
                padding: 14px 17px;
                background-color: #ffffff;
                border-radius: 100px;
                .search-hot-text {
                    font-size: 24px;
                    color: #999999;
                    height: 24px;
                }
            }
        }
        
    }
    // 排行榜
    .search-top-section {
        width: 690px;
        padding-bottom: 30px;
        flex-direction: column;
        background-color: #ffffff;
        border-radius: 24px;
        align-items: center;
        margin-left: 30px;

        .search-top-title {
            align-items: center;
            width: 100%;
            padding: 0 30px;
            width: 100%;
            height: 83px;
            background-image: url(https://img.qdreads.com/v163/sc_ssbj.png);
            background-repeat: no-repeat;
            background-size: 100%;
            align-items: center;
            margin-bottom: 20px;

                .top-all-image {
                    width: 24px;
                    height: 24px;
                }
            
            .search-top-all {
                font-size: 24px;
                color: #999999;
                height: 24px;
                margin-left: auto;
            }
        }
        
    }
    // 搜索结果页
    .search-result-section {
        flex-direction: column;
        flex: 1;
        padding: 0 24px;
        .search-result-total {
            margin-bottom: 32px;
            margin-top: 30px;
            text{
                font-size: 24px;
                font-weight: 500;
                color: #999999;
                height: 24px;
            }
            .result-total-num {
                color:#333333;
                margin: 0 6px;
            }

        }
        .search-result-list {
            flex: 1;
            .result-item {
                margin-bottom: 40px;
                .result-item-book-pic {
                    width: 108px;
                    height: 144px;
                    border-radius: 10px;
                    flex-shrink: 0;
                }
                .result-item-content {
                    margin-left: 24px;
                    flex-direction: column;
                    justify-content: center;
                    .result-item-name {
                        font-size: 32px;
                        font-weight: 500;
                        color: #333333;
                        height: 32px;
                    }
                    .result-item-des {
                        font-size: 24px;
                        font-weight: 500;
                        color: #666666;
                        height: 36px;
                        margin-top: 20px;
                        lines: 1;
                        text-overflow: ellipsis;
                    }
                    .result-item-read-num {
                        font-size: 20px;
                        font-weight: 500;
                        color: #bbbbbb;
                        height: 20px;
                        margin-top: 16px;
                    }
                }
            }  
        }
    }
}
.search-empty-content {
    flex-direction: column;
    align-items: center;
    justify-content: center;
    // 搜索空数据页面
    .search-empty {
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 364px;

        .search-empty-img {
            height: 278px;
            width: 280px;
        }

        .search-empty-des {
            color: #999;
            margin-top: 18px;
            height: 24px;
            font-size: 24px;
        }
    }
}