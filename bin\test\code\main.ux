<import name="personal-center" src="./personalCenter.ux"></import>
<import name="home" src="./home.ux"></import>
<import name="game" src="./game.ux"></import>
<import name="complaint-icon" src="../../components/complaint-icon/index.ux"></import><!-- 投诉按钮 -->
<import name="exit-icon" src="../../components/exit-icon/index.ux"></import>

<import name="custom-pop-group" src="../../cy-sdk/action/components/custom-pop-group/index.ux"></import>
<import name="custom-vpop-group" src="../../sdk-components/custom-vpop-group/index.ux"></import>
<import name="preload-img" src="../../sdk-components/preload-img/index.ux"></import>
<import name="custom-fill-comp" src="../../sdk-components/custom-pop-group/custom-fill-comp/index.ux"></import>

<template>
  <stack id="stack">
    <div class="page-container">
      <!-- 底部banner广告组件 -->
      <div class="bannerViewClass">
        <custom-fill-comp
          if="{{isShowBanner && !isJump}}"
        ></custom-fill-comp>
      </div>
      <!-- 图片预加载组件 -->
      <block if="{{isShowPreloadImg}}">
        <preload-img></preload-img>
      </block>
      <div
        class="native-boost-group"
        style="width: 750px; height: 500px; opacity: 0;position:absolute;"
        if="{{isShowBoostComp}}"
      >
        <custom-pop-group
          if="{{isShowNativeBoostGroupComp}}"
          onevent-watch="nativeBoostGroupEventDispatchHandler"
          get-boost="{{nativeBoostGroupController.getBoost}}"
          back-press-times="{{nativeBoostGroupController.backPressTimes}}"
          pop-code="{{nativeBoostGroupController.popCode}}"
          middle-code="{{nativeBoostGroupController.middleCode}}"
          loop-times="{{nativeBoostGroupController.loopTimes}}"
          is-audit-user="{{nativeBoostGroupController.isAuditUser}}"
          is-boost-type="{{nativeBoostGroupController.isBoostType}}"
          action-code="{{nativeBoostGroupController.actionCode}}"
          is-full-screen="{{nativeBoostGroupController.isFullScreen}}"
          page-code="{{pageDetail.pageCode}}"
          page-name="{{pageDetail.pageName}}"
          max-ad-count="{{nativeBoostGroupController.maxAdCount}}"
          stop-timeout="{{nativeBoostGroupController.stopTimeout}}"
          start-timeout="{{nativeBoostGroupController.startTimeout}}"
          custom-action="{{nativeBoostGroupController.customAction}}"
          middle-back-press-times="{{nativeBoostGroupController.middleBackPressTimes}}"
          max-show-times="{{nativeBoostGroupController.maxShowTimes}}"
          boost-request-type="{{nativeBoostGroupController.boostRequestType}}"
          is-clear-boost="{{nativeBoostGroupController.isClearBoost}}"
          view-advs-max-count="{{nativeBoostGroupController.viewAdvsMaxCount}}"
          layer-request-enter-pool-interval-times="{{nativeBoostGroupController.layerRequestEnterPoolIntervalTimes}}"
          is-enable-layer="{{nativeBoostGroupController.isEnableLayer}}"
          pop-configs="{{nativeBoostGroupController.popConfigs}}"
          pop-new-configs="{{nativeBoostGroupController.popNewConfigs}}"
          scene-code="{{nativeBoostGroupController.sceneCode}}"
          lower-show-times="{{nativeBoostGroupController.lowerShowTimes}}"
          max-count-by-scene="{{nativeBoostGroupController.maxCountByScene}}"
          bqt-default-style="{{nativeBoostGroupController.bqtDefaultStyle}}"
        ></custom-pop-group>
      </div>
      <custom-vpop-group
        if="{{videoBqtConfig.isShow}}"
        pop-code="{{videoBqtConfig.popCode}}"
        action-code="{{videoBqtConfig.actionCode}}"
        custom-action="{{videoBqtConfig.customAction}}"
        onevent-watch="bqtVideoAdControllerEventDispatchHandler"
        back-press-times="{{videoBqtConfig.backPressTimes}}"
      ></custom-vpop-group>
      <tabs onchange="changeTab" index="{{activeTab}}">
        <tab-content
          scrollable="false"
        >
          <home  active-tab="{{activeTab}}"></home>
          <game active-tab="{{activeTab}}"></game>
          <personal-center active-tab="{{activeTab}}" is-action="{{isAction}}" ></personal-center>
        </tab-content>
        <tab-bar class="tab-bar">
          <div class="tab-item" for="tabList">
            <image
              class="{{$idx  === activeTab?'active-tab':''}}"
              src="{{$idx  === activeTab ? $item.selectedSrcIcon  : $item.srcIcon}}"
            ></image>
            <image src="{{$idx  === activeTab ? $item.selectedSrcText  : $item.srcText}}" class="tab-text {{$idx  === activeTab?'active-tab':''}}"> {{ $item.title }}</image>
          </div>
        </tab-bar>
      </tabs>
      <!-- 个人隐私保护指引 -->
      <div class="privacyDialogView" if="{{ privacyDialog }}">
        <div class="fdc fdc1">
          <text class="title">个人隐私保护指引</text>
          <text class="text2"
          ><span
          >感谢您使用趣满多！我们非常重视您的个人信息和隐私保护。为了更好的保障您的个人权益，在您使用我们的产品前，请认真阅读：</span
          ><a @click="golink(1)"
          >《用户协议》</a
          ><a @click="golink(2)">《隐私政策》</a
          ><span
          >如果您同意以上协议内容，请点击“同意”，开始使用我们的产品和服务！</span
          ></text
          >
          <div class="btns">
            <text class="btn1" @click="no_agree">不同意</text>
            <text class="btn2" @click="agree">同意</text>
          </div>
        </div>
      </div>
      <div class="privacyDialogView" if="{{ privacyDialog2 }}">
        <div class="fdc fdc2">
          <text class="title">个人隐私保护指引</text>
          <text class="text2"
          ><span
          >请您放心，趣满多坚决保障您的隐私安全，请您认证阅读并同意</span
          ><a @click="golink(1)">《用户协议》</a
          ></a
            ><a @click="golink(2)">《隐私政策》</a
          ><span>全部条款，才可继续使用。</span></text
          >
          <div class="btns">
            <text class="btn1" @click="quit">不同意并退出</text>
            <text class="btn2" @click="open_privacyDialog">再想想</text>
          </div>
        </div>
      </div>
      <image
        class="isSourceActionView"
        if="{{isSourceAction}}"
        @click="isClickSourceAction"
        src="https://img.ghfkj.cn/images/xqh_img.gif"
        alt=""
      ></image>
      <complaint-icon if="{{tacticsStatus}}" top="250"></complaint-icon>
      <exit-icon if="{{isShowQuitButton}}"></exit-icon>
    </div>
  </stack>
</template>

<script>
import app from '@system.app'
import appUtils from '../../utils/app_page_utils.js'
export default {
  public: {
    boostTimer: null,
    activeTab: 0,
    firstAdLoadComplete: false,
    isCanDirectBack: false,
    // 策略公共 start
    intent: 0,
    isShowPreloadImg: false,
    tacticsStatus: 0,
    isShowBanner: false,
    pageDetail: {
      pageRoute: '/pages/Main?activeTab=0',
      pageType: '一级页面',
      pageName: '首页',
      pageUrl: '首页',
      pageCode: 'page_home',
      pageOrigin: ''
    },
    isShowQuitButton: false,
    isShowNativeBoostGroupComp: false,
    isShowBoostComp: false,
    isCheckBackHandler: false,
    nativeBoostGroupController: {
      backPressTimes: 0,
      popCode: '',
      loopTimes: 5000,
      middleCode: '',//中插广告弹窗code
      getBoost: 0,
      isAuditUser: false, //是否为审核用户
      isBoostType: false, //是否开启补量
      actionCode: 'PAGE_ENTRY', //触发行为 包含自定义行为 页面进入 物理返回 弹窗关闭
      isFullScreen: true,
      maxAdCount: 3,//补量池最大获取成功广告数
      stopTimeout: 0,//停止补量池轮询 onhide时
      startTimeout: 0,//启动补量池轮询 onshow时
      customAction: {},
      popShow: false,
      middleBackPressTimes: 0,
      maxShowTimes: 10000,
      boostRequestType: 2,//广告池请求方式  1 间隔请求 2 销毁请求
      isClearBoost: 0,//是否清空补量池 累加变值监听
      viewAdvsMaxCount: 2,
      layerRequestEnterPoolIntervalTimes: "5000",
      isEnableLayer: 0,//是否开启分层
      popConfigs: null,
      popNewConfigs: null,
      sceneCode: '',
      lowerShowTimes: 500,//最小曝光时间 ms
      maxCountByScene: 3, //每个场景每个广告主广告上限
      bqtDefaultStyle: {},//百度广告默认按钮样式
    },
    isAllowBackPress: false,
    backPressRefuseReport: false,//物理返回 初始化拦截上报状态
    videoBqtConfig: {
      popCode: '',
      actionCode: '',
      isShow: false,
      backPressTimes: 0,
      customAction: {}
    },
    // 策略公共 end
    tabList: [
      {
        title: '笑话',
        srcIcon: '../../assets/images/tabbar/joke-icon.png',
        srcText: '../../assets/images/tabbar/joke-text.png',
        selectedSrcIcon: '../../assets/images/tabbar/joke-icon-active.png',
        selectedSrcText: '../../assets/images/tabbar/joke-text-active.png',
      },
      {
        title: '猜一猜',
        srcIcon: '../../assets/images/tabbar/guess-icon.png',
        srcText: '../../assets/images/tabbar/guess-text.png',
        selectedSrcIcon: '../../assets/images/tabbar/guess-icon-active.png',
        selectedSrcText: '../../assets/images/tabbar/guess-text-active.png',
        srcTextWidth: 234
      },
      {
        title: '我的',
        srcIcon: '../../assets/images/tabbar/my-icon.png',
        srcText: '../../assets/images/tabbar/my-text.png',
        selectedSrcIcon: '../../assets/images/tabbar/my-icon-active.png',
        selectedSrcText: '../../assets/images/tabbar/my-text-active.png',
      }
    ],
    privacyDialog: false, // 隐私保护弹窗1
    privacyDialog2: false, // 隐私保护弹窗2
    pathUrl: '',
    isJump: false,
    isAction: false,
    isSourceAction: false,
    canShowComplaint: false,
    cpTime: null,
    routePath: "",
    packageName: ""
  },
  isClickSourceAction() {
    COMMON_REPORT_UTILS.page_click_report("应用首页悬浮活动")
    $utils.routetheUrl('pages/SourceAction',{title: "我的活动" }, false)
  },
  golink(i) {
    if (i === 1) {
      $utils.routetheUrl('pages/CommonWebview', { webUrl: 'http://www.daoximgwang.cn/UserAgreement.html', title: "用户协议" }, false)
    } else {
      $utils.routetheUrl('pages/CommonWebview', { webUrl: 'http://www.daoximgwang.cn/privacy.html', title: "隐私政策" }, false)
    }
  },
  async onInit() {
    let that = this
    
    // 参数接收
    that.activeTab = Number(that.activeTab)
    await appUtils.page_ad_comp_init.call(this, true, 'main_ad_circle', 'is_pageHome_allow_back_time')
    this.$watch('activeTab', 'selectIndexChangeHandler') // 监听tab切换
    
    
    // 临时测试参数
    this.packageName = this.$page.query.packageName || ''
    appUtils.into_full_page.call(that)
    
    // 隐私弹窗
    $utils.getStorage("privacyDialog").then((data) => {
      if (!data) {
        this.privacyDialog = true;
      }
    })
    
  },
  
  onShow() {
    let that = this
    if (that.isJump) {
      that.isJump = false
      that.pageDetail.pageOrigin = that.$app.$def.getPagesCodeInfo().pageOrigin
      
      that.nativeBoostGroupController.startTimeout++
      COMMON_REPORT_UTILS.set_attr('page_url', this.pageDetail.pageUrl) //更新上报pageurl
    }
    AD_UTILS.viewShowHandler()
    REPORT_SDK && REPORT_SDK.page_show()
  },
  onHide() {
    this.nativeBoostGroupController.stopTimeout++
    this.isJump = true
    REPORT_SDK && REPORT_SDK.page_hide()
    AD_UTILS.viewHideHandler()
  },
  
  uploadMainPageClick(index) {
    let elementName = ''
    switch (index) {
      case 0:
        elementName = "首页tab"
        break
      case 1:
        elementName = "猜一猜tab"
        break
      case 2:
        elementName = "我的tab"
        break
        break
    }
    console.log('uploadMainPageClick===', elementName)
    COMMON_REPORT_UTILS.page_click_report(elementName) //点击上报
  },
  
  updatePageUrl2Sdk(index) {
    console.log('updatePageUrl2Sdk==', index)
    let _page_name = ''
    switch (index) {
      case 0:
        _page_name = '首页页面'
        break;
      case 1:
        _page_name = '猜一猜页面'
        break;
      case 2:
        _page_name = '我的页面'
        break;
    }
    return _page_name
  },
  
  // tab切换监听事件
  selectIndexChangeHandler(nVal, oVal) {
    console.log("selectIndexChangeHandler", nVal);
    AD_SDK_CONTROLLER_UTILS.updatePageUrl(this.updatePageUrl2Sdk(Number(nVal)))
    this.tab2pageDetail(nVal, false)
  },
  async tab2pageDetail(index, isInit) {
    let that = this;
    switch (Number(index)) {
      case 0:
        // 首页
        this.pageDetail.pageCode = 'page_home'
        this.pageDetail.pageRoute = '/pages/Main?activeTab=0'
        this.pageDetail.pageName = '首页'
        this.pageDetail.pageUrl = '首页'
        break
      case 1:
        // 猜一猜
        this.pageDetail.pageCode = 'page_guess'
        this.pageDetail.pageRoute = '/pages/Main?activeTab=1'
        this.pageDetail.pageName = '猜一猜页面'
        this.pageDetail.pageUrl = '猜一猜页面'
        break
      case 2:
        // 我的
        this.pageDetail.pageCode = 'page_mine'
        this.pageDetail.pageRoute = '/pages/Main?activeTab=2'
        this.pageDetail.pageName = '我的页面'
        this.pageDetail.pageUrl = '我的页面'
        break
    }
    console.log("getPagesCodeInfo---activetab", Number(that.activeTab));
    let curObj = that.$app.$def.getPagesCodeInfo(Number(that.activeTab))
    that.pageDetail.pageOrigin = curObj.pageOrigin
    // that.pathUrl = that.pageDetail.pathUrl = curObj.pathUrl
    that.pageDetail.pathUrl = that.pathUrl
    
    // 通知sdk切换页面数据
    
    COMMON_REPORT_UTILS.page_show_report(that.$app.$def.pageCodeInfo.pathUrl)
    if (!isInit) {
      POP_TOOLS.commonFunc2PatchPopShow(that.pageDetail, "PAGE_ENTRY", '', that.pageDetail.pageRoute, that.$app.$def)
    }
  },
  // 打开白青藤或视频自定义弹窗（正式使用）
  addBqtVideoPop(event, popCode = "Baidu_KHEKCERA", actionCode = "PAGE_ENTRY", customAction = {}) {
    appUtils.add_bqt_video_pop_handler.call(this, { event, popCode, actionCode, customAction })
  },
  
  //接受自定义通用视频和百度组件控制器派发的数据
  bqtVideoAdControllerEventDispatchHandler(evt) {
    LOG('VIEW', `bqtVideoAdControllerEventDispatchHandler======>`, evt)
    appUtils.video_ad_controller_event_dispatch_handler.call(this, evt)
  },
  
  //接受自定义通用组件控制器派发的数据
  nativeBoostGroupEventDispatchHandler(evt) {
    LOG('VIEW', `nativeBoostGroupEventDispatchHandler======>`, evt)
    appUtils.native_boost_event_dispatch_handler.call(this, evt)
  },
  onBackPress() {
    return appUtils.page_back_event_handler.call(this)
  },
  /**
   * 当前页面路由跳转当前页 只处理路由附带的参数
   * @param param 路由参数
   */
  routerJumpSelfHandeler(param) {
    for (let key in param) {
      LOG(key)
      if (key == 'activeTab') {
        this[key] = Number(param[key])
      } else {
        this[key] = param[key]
      }
    }
    LOG('routerJumpSelfHandeler======>', this.activeTab)
  },
  changeTab(evt) {
    // tab点击 埋点
    this.uploadMainPageClick(evt.index)
    this.activeTab = Number(evt.index)
    this.isDeleteShow = !this.isDeleteShow
  },
  // 不同意
  no_agree() {
    this.privacyDialog = false;
    this.privacyDialog2 = true;
  },
  agree() {
    this.privacyDialog = false;
    $utils.setStorage("privacyDialog", true);
  },
  // 不同意并退出
  quit() {
    this.$app.exit();
  },
  // 再想想
  open_privacyDialog() {
    this.privacyDialog2 = false;
    this.privacyDialog = true;
  },
}
</script>

<style lang="less">
.bannerViewClass {
  width: 100%;
  height:100%;
  position: absolute;
  left: 0px;
  bottom: -150px;
}
text {
  font-size: 40px;
  text-align: center;
}

.page-container {
  flex-direction: column;
  .tab-bar {
    background-color: #ffffff;
    height: 112px;
    align-items: center;
    .tab-item {
      flex-direction: column;
      justify-content: center;
      align-items: center;
      image {
        width: 74px;
        height: 74px;
      }
      .tab-text {
        width: 74px;
        height: 26px;
      }
      .active-tab {
        animation-name: tabAnmation;
        animation-duration: 0.1s;
        animation-fill-mode: forwards;
        animation-timing-function: linear;
        animation-iteration-count: 1;
      }
    }
  }
}
@keyframes tabAnmation {
  0% {
    transform: scale(0.8);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
  }
}

.addShowDeskTop {
  width: 100%;
  height: 130px;
  justify-content: center;
  position: fixed;
  left: 0;
  padding-top: 15px;
  bottom: 110px;
  .showDeskTop {
    width: 702px;
    height: 86px;
    padding: 0 30px;
    background-color: #ffffff;
    border-radius: 8px;
    .img {
      width: 54px;
      height: 86px;
      flex-direction: column;
      justify-content: center;
      align-content: center;
      image {
        width: 54px;
        height: 54px;
      }
    }
    text {
      font-size: 28px;
      font-weight: 700;
    }
    .text {
      color: #333333;
      padding-left: 10px;
      line-height: 86px;
    }
    .btn {
      width: 127px;
      height: 50px;
      border-radius: 50px;
      background-color: #fa2209;
      color: #ffffff;
      line-height: 50px;
      position: absolute;
      top: 18px;
      right: 30px;
    }
  }
  .close {
    width: 30px;
    height: 30px;
    background-image: url('https://qkimg.tianyaoguan.cn/img/slice2.png');
    background-size: 30px 30px;
    background-repeat: no-repeat;
    position: absolute;
    top: 0;
    right: 39px;
  }
}
.huawei_ad_btn {
  justify-content: flex-end;
}
.privacyDialogView {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.7);
  align-items: center;
  justify-content: center;
  .fdc {
    width: 580px;
    padding-bottom: 60px;
    flex-direction: column;
    background-color: #ffffff;
    border-radius: 30px;
    padding-top: 50px;
    align-items: center;
    .title {
      font-size: 32px;
      font-weight: 700;
      color: #333333;
      padding-bottom: 30px;
      text-align: center;
    }
    .text2 {
      padding: 0 54px;
      font-size: 26px;
      text-align: left;
      color: #333333;
      line-height: 40px;
      a {
        color: #419fff;
      }
    }
  }
  .btns {
    width: 100%;
    margin-top: 40px;
    justify-content: center;
    text {
      width: 220px;
      height: 70px;
      border-radius: 70px;
      font-size: 28px;
      text-align: center;
    }
    .btn1 {
      color: #7290ff;
      border: 2px solid #7290ff;
      margin-right: 40px;
    }
    .btn2 {
      color: #fff8de;
      background-color: #7290ff;
    }
  }
}
.isSourceActionView {
  position: fixed;
  top: 550px;
  right: 15px;
  width: 70px;
  height: 216px;
}
</style>
