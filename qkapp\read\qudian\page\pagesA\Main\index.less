.change-tab-color {
    color: @color-text-select;
}
.normal-tab-color {
    color: @color-text-normal;
}
.tab-bar-wrapper {
    height: 100px;
}

.tab-item {
    height: 100px;
    .flex-box-mixins(column, center, center)
}
.tab-item-2 {
    height: 100px;
    width: 54px;
    padding-top: 0px;
    flex-direction: column;
    align-items: center;
}
.tab-icon{
    width: 54px;
    height: 54px;
    object-fit: fill;
}
.tab-icon-2 {
    width: 42px;
    height: 52px;
    margin-bottom: 10px;
}
.tab-title{
    height: 28px;
    font-size: 20px;
}
.add-desktop-container-empty{
    width: 750px;
}
.add-desktop-container {
    position: absolute;
    height: 80px;
    bottom: 126px;
    left: 30px;
    right: 30px;

    .add-desktop-close{
        width: 30px;
        height: 30px;
        position: absolute;
        top: 25px;
        left: 15px;
    }
    .add-desktop-tip{
        color: #ffffff; 
        margin-left: 50px;
    }
    .add-desktop-button {
        background-color: #ffef9d;
        border-radius: 74px;
        margin-right: 20px;
        text {
            font-size: 26px;
            color: #826d00;
            font-weight: 400;
            line-height: 26px;
            text-align: center;
            padding: 8px 17px;
        }
    }
}
.box-fixed-container{
    position: fixed;
    right: 1px;
    bottom: 454px;
    width: 130px;
    height: 130px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}
.box-fixed-image{
    width: 129px;
    height: 96px;
}
.box-fixed-button-icon{
    width: 117px;
    height: 37px;
    object-fit: fill;
    margin-top: 79px;
    margin-left: 6px;
}
.box-fixed-text{
    width: 117px;
    height: 37px;
    margin-top: 79px;
    text-align: center;
    line-height: 37px;
    font-size: 20px;
    color: #FA2D18;
    margin-left: 6px;
}

.boots-hide{
    width: 750px; 
    padding: 0 0 0 24px;
    opacity:0;
}
.boots-show{
    width: 750px; 
    padding: 0 0 0 24px;
}
.native-boost-group{
    position: absolute;
    top: 0px;
    left: 0px;
    width: 750px;
    height: 500px;
    // background-color: rgba(0, 0, 0, .2);
}

.tab-lottie {
    width: 54px;
    height: 54px;
}

.circle-boost-wrap{
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
}
