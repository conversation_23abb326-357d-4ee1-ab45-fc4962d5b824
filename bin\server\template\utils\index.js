function convertCardJsonToArray(cardJson) {
  const mapping = {
    package: '包名',
    name: '应用名',
    versionName: '版本名',
    versionCode: '版本号',
    minPlatformVersion: '最低支持平台',
    sdkVersion: 'SDK 版本',
    interfaceVersion: '对接版本',
    brand: '厂商',
    appType: 'app 类型'
  }

  return Object.keys(mapping).map(key => {
    return {
      label: mapping[key],
      value: cardJson[key]
    }
  })
}

function generateCardHtml(cardArray) {
  return cardArray
    .map(item => {
      return `
      <div class="card">
        <div class="title">
          ${item.label}
        </div>
        <div class="info">
          ${item.value !== undefined ? item.value : ''}
        </div>
      </div>
    `
    })
    .join('') // 将数组中的HTML拼接成字符串
}

module.exports = {
  convertCardJsonToArray,
  generateCardHtml
}
